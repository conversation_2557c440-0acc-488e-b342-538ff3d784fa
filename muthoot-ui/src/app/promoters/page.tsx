import DirectorBox from "@/components/about/DirectorBox";
import Founder<PERSON>ox from "@/components/about/FounderBox";
import CommonBanner from "@/components/common/CommonBanner";
import React from "react";
import { generateMetadata as generateMetadataUtil } from "@/lib/metaData";
import { Metadata } from "next";
import { GetDirectorsPage, GetDirectorsSeo } from "@/lib/api/cms";
import CommonText from "@/components/common/CommonText";
export async function generateMetadata(): Promise<Metadata> {
  const { data: SEOData } = await GetDirectorsSeo();

  return await generateMetadataUtil(
    SEOData?.SEO?.Title,
    SEOData?.SEO?.Description,
    "/directors",
    SEOData?.SEO?.keywords
  );
}

export default async function Promoters() {
  const { data } = await GetDirectorsPage();
  return (
    <main>
      <CommonBanner titleClassName="text-[44px] font-semibold text-[#FECB05] line-clamp-4 max-sm:text-[30px]"
        backgroundImage={`${process.env.NEXT_PUBLIC_API_BASE_URL}${
          data?.Banner?.Banner?.url || ""
        }`}
        title={data?.Banner?.Title || ""}
        description={data?.Banner?.Description || ""}
        sideImage={`${process.env.NEXT_PUBLIC_API_BASE_URL}${
          data?.Banner?.SRK_Image?.url || ""
        }`}
        underOverlay={false}
        sideImageHeight={1000}
        sideImageWidth={1000}
        sideImageClassName="mt-[1.8rem]  md:w-[476px]"
        backgroundImageHeight={1000}
        backgroundImageWidth={1000}
        breadcrumbs={[
          { label: "About Us", href: "/about" },
          { label: "Our Promoters", href: "#" },
        ]}
      />

      <div className="directors_page py-[100px] max-md:py-[50px] relative">
        <div className=" absolute top-0 left-0 w-full h-full bg-[linear-gradient(180deg,_#008BD2_6.98%,_rgba(0,139,210,0.804112)_37.87%,_rgba(0,139,210,0.480664)_53.78%,_rgba(0,139,210,0.146018)_69.69%,_rgba(0,139,210,0)_80.59%)]"></div>
        <div className="container">
          {/* {data?.Memorial_Card && (
            <div className="founder_mainwrapper">
              <FounderBox data={data?.Memorial_Card} />
            </div>
          )} */}

          <div className="directors_list">
            <div
              data-aos="fade-up"
              className="title max-w-[566px] w-full mx-auto"
            >
              {data?.Directors_Section?.Title && (
                <h2 className="text-[#141414] text-[44px] font-semibold text-center max-md:text-[35px] max-md:leading-[44px]">
                  <CommonText title={data?.Directors_Section?.Title} />
                </h2>
              )}
              {data?.Directors_Section?.Description && (
                <p className="text-[16px] leading-[24px] fontnormal text-[white] text-center mt-[22px] max-md:text-[14px] max-md:leading-[22px]">
                  {data?.Directors_Section?.Description}.
                </p>
              )}
            </div>

            <DirectorBox data={data?.Directors} />
          </div>
        </div>
      </div>
    </main>
  );
}
