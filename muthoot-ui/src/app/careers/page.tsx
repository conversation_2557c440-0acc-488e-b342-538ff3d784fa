import CommonBanner from "@/components/common/CommonBanner";
import React from "react";
import Image from "next/image";
import Videoplayer from "@/components/careers/videoplayer";
import EmployeeSlider from "@/components/careers/TestimonialSlider";
import Button from "@/components/ui/Button";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import {
  GetCareerMuthootees,
  GetCareersPage,
  GetCareersSeo,
  GetTestimonialCareer,
} from "@/lib/api/cms";
import { Metadata } from "next";
import { generateMetadata as generateMetadataUtil } from "@/lib/metaData";
import CommonText from "@/components/common/CommonText";
import CommonTextBlue from "@/components/common/CommonTextBlue";
import Link from "next/link";
import TestimonialSlider from "@/components/careers/TestimonialSlider";

export async function generateMetadata(): Promise<Metadata> {
  const { data: SEOData } = await GetCareersSeo();

  return await generateMetadataUtil(
    SEOData?.SEO?.Title,
    SEOData?.SEO?.Description,
    "/corparote",
    SEOData?.SEO?.keywords
  );
}

export default async function Careers() {
  const { data } = await GetCareersPage();
  const { data: muthootees } = await GetCareerMuthootees();
  const { data: testimonial } = await GetTestimonialCareer();

  return (
    <main>
      <div className="common_banner w-full h-[489px] bg-cover bg-no-repeat relative max-xl:h-full">
        <div className="background_layer absolute inset-0 -z-[10]">
          <Image
            src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${data?.Banner?.Banner?.url}`}
            alt="background image"
            layout="fill"
            objectFit="cover"
            priority
          />
        </div>

        <div className="background_shade w-full h-full absolute top-0 left-0 -z-[5] bg-[rgba(0,84,157,0.9)]"></div>
        <div className="gradient_shade bg-[linear-gradient(1deg,_#018bd2_15.46%,_#0763ac00_77.64%)] w-full h-full absolute bottom-0 left-0 z-[28]"></div>

        <div className="container relative z-[35] h-[489px] flex justify-between items-start gap-[40px] max-xl:flex-col max-xl:items-start max-xl:gap-[40px] max-xl:h-full">
          <div className="content_wrapper max-w-[calc(100%-380px)] w-full  max-lg:max-w-[700px] flex justify-start items-start gap-[40px] max-xl:max-w-[90%] max-lg:flex-col max-lg:pt-[0]">
            <div className="wrapper flex justify-start items-start gap-[60px] max-w-[280px] w-full">
              <div className="breadcrumbs_wrapper pt-[36px]">
                <Breadcrumb>
                  <BreadcrumbList className="!gap-[9px]">
                    <BreadcrumbItem className="mt-[-2px]">
                      <BreadcrumbLink
                        className="text-[13px] font-[400] text-[#FFFFFF]"
                        href="/"
                      >
                        Home
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator />
                    <BreadcrumbItem className="mt-[-2px]">
                      <BreadcrumbPage className="text-[13px] font-[400] text-[#FFFFFF]">
                        Career
                      </BreadcrumbPage>
                    </BreadcrumbItem>
                  </BreadcrumbList>
                </Breadcrumb>
              </div>
              <div
                data-aos="fade-down"
                className="certification max-w-[100px] w-full"
              >
                <div className="w-120px h-120">
                  <Image
                    className=" w-full h-full object-contain"
                    src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${data?.Certified_Label_Image?.url}`}
                    alt="banner side image"
                    width={500}
                    height={500}
                    priority
                  />
                </div>
              </div>
            </div>
            <div className="content max-w-[500px] w-full pt-[120px] max-lg:pt-[0] ">
              <h2
                data-aos="fade-up"
                className="text-[44px] font-semibold text-white line-clamp-2 max-sm:text-[35px]"
              >
                <CommonText title={data?.Banner?.Title} />
              </h2>
              <p
                data-aos="fade-up"
                className="font-normal text-[#EAEAEA] text-[16px] mt-[16px] line-clamp-[6] max-sm:text-[14px] max-sm:line-clamp-[8] text-justify"
              >
                {data?.Banner?.Description}
              </p>
            </div>
          </div>

          <div
            data-aos="fade-up"
            className="ad_banner self-end max-w-[340px] w-full h-full max-lg:max-w-[330px] max-lg:w-full max-sm:max-w-[250px] relative"
          >
            <div className=" side_imagegradient bg-[linear-gradient(0.4deg,_rgb(1,139,210)_9.46%,_rgba(7,99,172,0)_32.64%)] w-full h-full absolute top-0 left-0 z-[5]"></div>

            <Image
              className="max-w-[336px] w-full h-full object-contain"
              src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${data?.Banner?.SRK_Image?.url}`}
              alt="banner side image"
              width={1000}
              height={1000}
              priority
            />
          </div>
        </div>
      </div>

      <div className="careers_pagewrapper relative">
        <div className=" absolute top-0 left-0 w-full h-full bg-[linear-gradient(180deg,_#008BD2_6.98%,_rgba(0,139,210,0.804112)_37.87%,_rgba(0,139,210,0.480664)_53.78%,_rgba(0,139,210,0.146018)_69.69%,_rgba(0,139,210,0)_80.59%)]"></div>
        <section
          data-aos="fade-up"
          className="join_us pt-[240px] pb-[100px]  max-md:pb-[60px] relative max-lg:pt-[200px] max-md:pt-[60px]"
        >
          <div className="container flex justify-center items-start gap-[0] max-md:flex-col max-md:gap-[30px] relative z-[10]">
            <div className="join_banner h-auto max-w-[400px] w-full relative z-[9]  max-lg:max-w-[300px] max-md:hidden ">
              <Image
                className="mt-[-210px] ml-[35px]  max-w-[396] max-h-[737] max-lg:max-w-[320px] max-lg:mt-[-169px]"
                src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${data?.Header?.SRK_Image?.url}`}
                alt="sharukh"
                width={1000}
                height={1000}
              />
            </div>

            <div className="content max-w-[790px] overflow-hidden w-full max-lg:max-w-full relative bg-[#E7F7FF] border border-[#AFE3FC] rounded-[14px] pl-[70px] pr-[20px] py-[40px] max-lg:p-[30px] max-md:max-w-full">
              <Image
                className="absolute top-0 left-0 w-full h-full object-cover"
                src="/careerbox_partcile.png"
                alt="particle"
                width={584}
                height={481}
              />
              <h2 className="text-[44px] leading-[58px] font-semibold text-[#141414] max-md:text-[30px] max-md:leading-[40px] relative z-[8] max-lg:text-[32px] max-lg:leading-[45px]">
                <CommonTextBlue title={data?.Header?.Title} />
              </h2>
              <h6 className="font-semibold text-[#141414] text-[20px]  mt-[16px] max-md:text-[18px] max-md:leading-[24px] max-md:mt-[14px] relative z-[8] max-lg:text-[18px]">
                {data?.Header?.SubTitle}
              </h6>
              <p className="font-normal text-[#484848] text-[16px] mt-[28px] max-md:text-[14px] max-md:leading-[24px] max-md:mt-[15px] relative z-[8] max-lg:text-[14px] max-sm:mt-[20px]">
                {data?.Header?.Description}
              </p>
              <div className="btn_wrapper mt-[40px] max-md:mt-[25px] relative z-[8] max-lg:mt-[25px]">
                <Link href={data?.Header?.Button?.Link} target="_blank">
                  <Button
                    className="max-lg:text-[14px]"
                    children={data?.Header?.Button?.Label}
                  />
                </Link>
              </div>
            </div>
          </div>

          <div className="muthoot_gallery">
            <div className="container"></div>
          </div>
        </section>

        <section className="our_muthootis relative z-[9]">
          <div className="container">
            <div className="ladies_wrapper flex justify-start items-start gap-[40px]  max-lg:flex-col max-sm:gap-[20px]">
              <div className="content max-w-[550px] w-full max-lg:max-w-full">
                <h2
                  data-aos="fade-up"
                  className="text-[44px] font-semibold text-[#0F0F0F] max-sm:text-[35px]"
                >
                  <CommonText
                    defaultColor="#0F0F0F"
                    title={muthootees?.Muthootees?.Header?.Title}
                  />
                </h2>
                <p
                  data-aos="fade-up"
                  className="text-white text-[16px] font-[400] mt-[18px] max-sm:text-[14px] "
                >
                  {muthootees?.Muthootees?.Header?.Description}
                </p>

                <div
                  data-aos="fade-up"
                  className="muthootis_singleimage max-w-[320px] w-full mt-[140px] ml-auto max-lg:mx-auto max-lg:mt-[50px]"
                >
                  <Image
                    className="w-full h-full aspect-[320/250] object-cover rounded-[12px]"
                    src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${muthootees?.Muthootees?.Image1?.url}`}
                    alt="sharukh"
                    width={320}
                    height={250}
                  />
                </div>
              </div>
              <div className="image_gridwrapper max-w-[calc(100%-590px)] w-full flex gap-[40px] max-xl:max-w-[80%] max-xl:mx-auto max-sm:max-w-full max-sm:gap-[20px] max-lg:justify-center">
                <div className="single_col max-w-[320px] w-full flex flex-col gap-[40px] max-sm:gap-[20px]">
                  <div data-aos="fade-down" className="single_gridimg w-full">
                    <Image
                      className="w-full h-full aspect-[320/420] object-cover rounded-[12px]"
                      src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${muthootees?.Muthootees?.Image2?.url}`}
                      alt="sharukh"
                      width={320}
                      height={420}
                    />
                  </div>
                  <div data-aos="fade-up" className="single_gridimg w-full">
                    <Image
                      className="w-full h-full aspect-[320/250] object-cover rounded-[12px]"
                      src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${muthootees?.Muthootees?.Image3?.url}`}
                      alt="sharukh"
                      width={320}
                      height={250}
                    />
                  </div>
                </div>
                <div className="single_col max-w-[320px] w-full flex flex-col gap-[40px] max-sm:gap-[20px]">
                  <div data-aos="fade-down" className="single_gridimg w-full">
                    <Image
                      className="w-full h-full aspect-[320/250] object-cover rounded-[12px]"
                      src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${muthootees?.Muthootees?.Image4?.url}`}
                      alt="sharukh"
                      width={320}
                      height={420}
                    />
                  </div>
                  <div data-aos="fade-up" className="single_gridimg w-full">
                    <Image
                      className="w-full h-full aspect-[320/360] object-cover rounded-[12px]"
                      src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${muthootees?.Muthootees?.Image5?.url}`}
                      alt="sharukh"
                      width={320}
                      height={250}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <section className="employee_list py-[100px] max-md:py-[50px]">
          <div className="container">
            <h2
              data-aos="fade-up"
              className="text-[44px] font-semibold text-[#0F0F0F] leading-[58px] max-w-[611px] w-full mx-auto text-center max-md:text-[30px] max-md:leading-[40px]"
            >
              <CommonTextBlue title={data?.Testimonial_Title} />
            </h2>

            <div className="employee_slider">
              <TestimonialSlider data={testimonial} />
            </div>
          </div>
        </section>
      </div>
    </main>
  );
}
