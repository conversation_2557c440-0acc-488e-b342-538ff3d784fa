import BlogBox from "@/components/blogs/BlogBox";
import CommonBanner from "@/components/common/CommonBanner";
import { GetBlogPage } from "@/lib/api/cms";
import { GetBlogs } from "@/lib/api/general";
import { Metadata } from "next";
import React from "react";
import { generateMetadata as generateMetadataUtil } from "@/lib/metaData";
export async function generateMetadata(): Promise<Metadata> {
  const { data: SEOData } = await GetBlogPage();

  return await generateMetadataUtil(
    SEOData?.SEO?.Title,
    SEOData?.SEO?.Description,
    "/blogs",
    SEOData?.SEO?.keywords
  );
}

export default async function Blogs() {
  const { data } = await GetBlogPage();
  const { data: blogs } = await GetBlogs();
  return (
    <main>
      <CommonBanner
        backgroundImage={`${process.env.NEXT_PUBLIC_API_BASE_URL}${data?.Banner?.Banner?.url}`}
        title={data?.Banner?.Title}
        description={data?.Banner?.Description}
        sideImage={`${process.env.NEXT_PUBLIC_API_BASE_URL}${data?.Banner?.SRK_Image?.url}`}
        sideImageHeight={1000}
        sideImageWidth={1000}
        sideImageClassName="mt-1.5 md:w-[568px] md:h-[433px]"
        backgroundImageHeight={1000}
        backgroundImageWidth={1000}
        breadcrumbs={[
          { label: "Media", href: "/media" },
          { label: "Our Blogs", href: "/blogs" },
        ]}
      />

      <div className="news_listpage pb-[100px] pt-[50px] max-sm:py-[50px] relative">
        <div className=" absolute top-0 left-0 w-full h-[1070px] bg-[linear-gradient(180deg,_#008BD2_6.98%,_rgba(0,139,210,0.804112)_37.87%,_rgba(0,139,210,0.480664)_53.78%,_rgba(0,139,210,0.146018)_69.69%,_rgba(0,139,210,0)_80.59%)]"></div>
        <div className="container relative z-[9]">
          <div
            data-aos="fade-up"
            className="title_wrapper max-w-[900px] w-full mx-auto"
          >
            <h2 className="text-[44px] text-center leading-[58px] text-[white] font-semibold max-md:text-[35px] max-md:leading-[45px] ">
              Our <span className="text-[#FECB05]">Blogs</span>
            </h2>
            <p className="text-[16px] leading-[24px] text-center font-normal text-[white] mt-[16px] max-md:text-[14px] max-md:leading-[22px]">
              {data?.Header?.Description}
            </p>
          </div>

          <div className="news_listing flex justify-start gap-[36px] flex-wrap items-start mt-[36px]">
            <BlogBox blogs={blogs} />
          </div>
        </div>
      </div>
    </main>
  );
}
