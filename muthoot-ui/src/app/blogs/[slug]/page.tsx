import SecondaryBanner from "@/components/common/SecondaryBanner";
import Image from "next/image";
import Link from "next/link";
import { GetBlogs, GetSingleBlog } from "@/lib/api/general";
import { generateMetadata as generateMetadataUtil } from "@/lib/metaData";
import { GetBlogPage } from "@/lib/api/cms";
import { Metadata } from "next";
import BlogNextPrev from "@/components/blogs/BlogNextPrev";
import RecommendedBlogs from "@/components/blogs/RecommendedBlogs";

export async function generateMetadata(): Promise<Metadata> {
  const { data: SEOData } = await GetBlogPage();
  return await generateMetadataUtil(
    SEOData?.SEO?.Title,
    SEOData?.SEO?.Description,
    "/blogs",
    SEOData?.SEO?.keywords
  );
}

const RenderDescriptionContent = ({ item }: any) => {
  switch (item.type) {
    case "paragraph":
      return (
        <p className="text-[16px] font-normal text-[#141414] leading-[22px] mt-[14px] max-md:text-[14px]">
          {item.children.map((child: any, index: number) =>
            child.bold ? (
              <span
                key={index}
                className="text-[22px] font-[600] text-[#141414]"
              >
                {child.text}
              </span>
            ) : (
              <span key={index}>{child.text}</span>
            )
          )}
        </p>
      );
    case "quote":
      return (
        <h4 className="text-[28px] font-semibold text-[#008BD2] mt-[34px] max-sm:text-[28px]">
          {item.children[0].text}
        </h4>
      );
    case "image":
      return (
        <div className="img_wrapper  flex justify-start items-center gap-[21px] flex-wrap mt-[34px] max-sm:flex-col">
          <Image
            className="h-full object-cover  rounded-[10px] aspect-[373/267] w-[calc(100%/2-11px)] max-sm:w-full"
            src={`${item?.image?.url}`}
            alt={item.image.alternativeText || "blog image"}
            width={1000}
            height={1000}
          />
        </div>
      );
    default:
      return null;
  }
};

export default async function BlogDetailPage({
  params,
}: {
  params: Promise<{ slug: string }>;
}) {
  const { slug } = await params;
  const { data } = await GetSingleBlog(slug);
  const { data: allBlogs } = await GetBlogs();

  const currentIndex = allBlogs.findIndex(
    (blog: any) => blog.documentId === slug
  );

  const prevBlog =
    currentIndex > 0
      ? allBlogs[currentIndex - 1]
      : allBlogs[allBlogs.length - 1];

  const nextBlog =
    currentIndex < allBlogs.length - 1
      ? allBlogs[currentIndex + 1]
      : allBlogs[0];

  const formatDate = (dateString: any) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  return (
    <main>
      <SecondaryBanner
        backgroundImage={`${process.env.NEXT_PUBLIC_API_BASE_URL}${data?.Banner?.url}`}
        imageHeight={1000}
        imageWidth={1000}
        description=""
        bannerClass="common_banner w-full py-[230px] bg-cover bg-no-repeat relative max-lg:h-full max-md:py-[100px] intiative_detailbanner"
        title={data?.Title}
        breadcrumbs={[
          { label: "Media", href: "/media" },
          { label: "Our Blogs", href: "/blogs" },
          { label: "Details", href: "#" },
        ]}
      />
      <div className="digital_intiative_detailpage pt-[32px] pb-[100px] max-md:pb-[50px] relative">
        <div className="overlay_fade bg-[linear-gradient(180.84deg,_#008BD2_-64.11%,_rgba(0,139,210,0.804112)_-45.11%,_rgba(0,139,210,0.480664)_1.8%,_rgba(0,139,210,0.146018)_35.65%,_rgba(0,139,210,0)_80.15%)] w-full h-[700px] absolute top-0 left-0"></div>
        <div className="container">
          <div className="detailed_wrapper flex justify-between items-start gap-[60px] max-lg:flex-col max-sm:gap-[40px] relative z-[9]">
            <div className="main_contents max-w-[767px] w-full max-lg:max-w-full">
              <div className="date text-[16px] font-medium text-[#484848] w-fit">
                {formatDate(data?.Date)}
              </div>
              <h3 className="text-[32px] font-semibold text-[#3C3C3C] mt-[8px]">
                {data?.DescriptionTitle}
              </h3>
              {data?.Description?.map((item: any, index: any) => (
                <RenderDescriptionContent key={index} item={item} />
              ))}
              <BlogNextPrev prevBlog={prevBlog} nextBlog={nextBlog} />
            </div>
            <RecommendedBlogs currentBlog={data} allBlogs={allBlogs} />
          </div>
        </div>
      </div>
    </main>
  );
}
