import CommonBanner from "@/components/common/CommonBanner";
import React from "react";

import { Metadata } from "next";
import CommonText from "@/components/common/CommonText";
import { generateMetadata as generateMetadataUtil } from "@/lib/metaData";
import {
  GetNextGenerationPage,
  GetNextGenerationSeo,
  GetTncPage,
  GetTncSeo,
} from "@/lib/api/cms";
import RenderDescriptionContent from "@/components/common/RenderDescriptionContent";

export async function generateMetadata(): Promise<Metadata> {
  const { data: SEOData } = await GetTncSeo();

  return await generateMetadataUtil(
    SEOData?.SEO?.Title,
    SEOData?.SEO?.Description,
    "/terms-conditions",
    SEOData?.SEO?.keywords
  );
}

export default async function TermsCondition() {
  const { data } = await GetTncPage();
  return (
    <main>
      <CommonBanner
        backgroundImage={`${process.env.NEXT_PUBLIC_API_BASE_URL}${
          data?.Banner?.Banner?.url || ""
        }`}
        title={data?.Banner?.Title || ""}
        description={data?.Banner?.Description || ""}
        sideImage={`${process.env.NEXT_PUBLIC_API_BASE_URL}${
          data?.Banner?.SRK_Image?.url || ""
        }`}
        sideImageClassName="mt-[.7rem] md:w-[344px]"
        sideImageHeight={1000}
        sideImageWidth={1000}
        backgroundImageHeight={1000}
        backgroundImageWidth={1000}
        breadcrumbs={[{ label: "Terms & Condition", href: "/terms-condition" }]}
      />

      <div className="policy_page py-[100px] max-md:py-[50px]">
        <div className="container">
          <div className="title_wrapper max-w-[828px] w-full">
            <h2
              data-aos="fade-up"
              className="text-[44px] leading-[58px] font-semibold text-[#141414] max-md:text-[35px] max-md:leading-[44px]"
            >
              {data?.Header?.Title}
            </h2>
            <p
              data-aos="fade-up"
              className="text-[16px] font-normal text-[#505050] leading-[24px] mt-[25px] max-md:text-[14px] max-md:leading-[22px] max-md:mt-[15px]"
            >
              {data?.Header?.Description}{" "}
            </p>
          </div>
          <div className="contents">
            {data?.Content?.map((item: any, index: any) => (
              <RenderDescriptionContent key={index} item={item} />
            ))}
          </div>
        </div>
      </div>
    </main>
  );
}
