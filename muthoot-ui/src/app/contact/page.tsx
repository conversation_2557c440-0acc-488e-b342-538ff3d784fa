import CommonBanner from "@/components/common/CommonBanner";
import React from "react";
import Image from "next/image";
import Button from "@/components/ui/Button";
import BranchBox from "@/components/contact/BranchBox";
import Link from "next/link";
import { GetBranches, GetContactPage, GetContactPageSeo } from "@/lib/api/cms";
import { generateMetadata as generateMetadataUtil } from "@/lib/metaData";
import { Metadata } from "next";
import ContactForm from "@/components/form/ContactForm";
import CommonText from "@/components/common/CommonText";
import BranchList from "../branch/BranchList";
import { GetBranchHome12, GetBranchHome3 } from "@/lib/api/home";

export async function generateMetadata(): Promise<Metadata> {
  const { data: SEOData } = await GetContactPageSeo();

  return await generateMetadataUtil(
    SEOData?.SEO?.Title,
    SEOData?.SEO?.Description,
    "/contact",
    SEOData?.SEO?.keywords
  );
}

export default async function Contact() {
  const { data } = await GetContactPage();
  const { data: branches } = await GetBranches();
  const { data: branchHome12 } = await GetBranchHome12();
  return (
    <main>
      {data?.Banner && (
        <CommonBanner
          backgroundImage={`${process.env.NEXT_PUBLIC_API_BASE_URL}${
            data?.Banner?.Banner?.url || ""
          }`}
          title={data?.Banner?.Title || ""}
          description={data?.Banner?.Description || ""}
          sideImage={`${process.env.NEXT_PUBLIC_API_BASE_URL}${
            data?.Banner?.SRK_Image?.url || ""
          }`}
          sideImageHeight={1000}
          sideImageWidth={1000}
          sideImageClassName="mt-[.4rem] md:w-[425px] md:h-[423px]"
          backgroundImageHeight={1000}
          backgroundImageWidth={1000}
          breadcrumbs={[{ label: "Contact Us", href: "/contact" }]}
        />
      )}

      <div className="contact_page pb-[100px] pt-[50px] max-md:py-[50px] relative ">
        <div className=" absolute top-0 left-0 w-full h-full bg-[linear-gradient(#008bd2_6.98%,_#008bd2cd_37.87%,_#008bd27b_53.78%,_#008bd225_200.69%,_#008bd200_121.59%)]"></div>
        <div className="container">
          <div
            data-aos="fade-up"
            className="title_wrapper max-w-[900px] w-full mx-auto"
          >
            <h2 className="text-[44px] text-center leading-[58px] text-[#FECB05] font-semibold max-md:text-[35px] max-md:leading-[45px] ">
              {data?.Header?.Title}{" "}
            </h2>

            <p className="text-[16px] leading-[24px] text-center font-normal text-[#ffffff] mt-[16px] max-md:text-[14px] max-md:leading-[22px]">
              {data?.Header?.Description}
            </p>
          </div>

          <div
            data-aos="fade-up"
            className="contact_formwrapper  bg-[#E9F6FC] flex justify-center items-start mt-[42px] mb-[100px] rounded-[8px] overflow-hidden max-lg:flex-col max-md:mt-[70px] max-xl:w-fit mx-auto max-lg:w-full max-lg:max-w-[650px] max-sm:mb-[50px]"
          >
            {data?.Enquiry?.Image && (
              <div className="banner max-w-[563px] w-full h-auto self-stretch max-lg:h-[400px] max-lg:max-w-full">
                <Image
                  className=" w-full  h-full object-cover max-lg:object-top "
                  src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${data?.Enquiry?.Image?.url}`}
                  alt={data?.Enquiry?.Image?.name}
                  width={1000}
                  height={1000}
                />
              </div>
            )}

            <ContactForm />
          </div>

          <div
            data-aos="fade-up"
            className="registered_box overflow-hidden  rounded-[8px]  max-w-full w-full mx-auto mt-[42px] relative flex items-center max-lg:flex-col max-lg:max-w-[750px]"
          >
            <div className="muthoot_buildingimage self-stretch max-w-[calc(100%-550px)] w-full max-lg:max-w-full max-lg:h-[400px] max-sm:h-[280px]">
              <Image
                className=" w-full   h-full object-cover max-lg:object-top "
                src="/muthoot_building.png"
                alt="building"
                width={680}
                height={562}
              />
            </div>
            <div className="content p-[50px] max-w-[650px] w-full bg-[#008BD2] max-xl:p-[35px] max-lg:max-w-full max-sm:p-[25px] self-stretch">
              <div className="particle absolute top-0 right-0 w-full h-full max-lg:hidden flex justify-end">
                <Image
                  className=" w-[513px]   h-full object-cover"
                  src="/muthoot_officeparticle.png"
                  alt="particle"
                  width={513}
                  height={270}
                />
              </div>

              <h2 className="text-[24px] font-semibold text-white text-start">
                <CommonText title={data?.Office_Address_Title || ""} />
              </h2>

              <div className="contact_wrapper relative z-[9] flex justify-center items-start gap-[24px] mt-[42px] max-lg:flex-wrap max-lg:gap-[25px] flex-col max-sm:mt-[30px]">
                {data?.OfficeAddress &&
                  data?.OfficeAddress?.length > 0 &&
                  data?.OfficeAddress?.map((item: any, index: number) => (
                    <div
                      key={index}
                      className="email_wrapper w-full flex justify-start flex-col items-start  max-sm:w-full"
                    >
                      <Image
                        className="w-[50px] rounded-[50%] aspect-square h-full object-contain"
                        src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${item?.Icon?.url}`}
                        alt={item?.Icon?.name}
                        width={1000}
                        height={1000}
                      />
                      <h4 className="text-[18px] font-semibold text-white mt-[10px] text-center">
                        {item?.Title}
                      </h4>
                      {/* Email (id 61) */}
                      {item.id === 61 && (
                        <h6 className="text-[16px] font-normal text-white mt-[5px] text-center">
                          <a
                            href={`mailto:${item?.Value1}`}
                            className="hover:underline hover:text-[#ffffff] transition-all cursor-pointer"
                          >
                            {item?.Value1}
                          </a>
                        </h6>
                      )}

                      {/* Address (id 62) */}
                      {item.id === 62 && (
                        <h6 className="text-[16px] font-normal text-white mt-[5px] text-start">
                          {item?.Value1}
                        </h6>
                      )}

                      {/* Phone Numbers (id 63) */}
                      {item.id === 63 && (
                        <>
                          <h6 className="text-[16px] font-normal text-white mt-[5px] text-center">
                            <a
                              href={`tel:${item?.Value1}`}
                              className="hover:underline hover:text-[#ffffff] transition-all cursor-pointer"
                            >
                              {item?.Value1}
                            </a>
                          </h6>
                          {item?.value2 && (
                            <h6 className="text-[16px] font-normal text-white mt-[5px] text-center">
                              <a
                                href={`tel:${item?.value2}`}
                                className="hover:underline hover:text-[#ffffff] transition-all cursor-pointer"
                              >
                                {item?.value2}
                              </a>
                            </h6>
                          )}
                        </>
                      )}
                    </div>
                  ))}
              </div>
            </div>
          </div>

          <div
            data-aos="fade-up"
            className="map_wrapper w-full h-[474px] mt-[100px] max-md:mt-[50px]"
          >
            <iframe
              className="w-full h-full object-cover rounded-[10px]"
              src={data?.MapLink}
              width="1200"
              height="474"
              loading="lazy"
            ></iframe>
          </div>

          <div className="branch_listing mt-[70px]">
            <h2
              data-aos="fade-up"
              className="text-[44px] text-center leading-[58px] text-[black] font-semibold max-md:text-[35px] max-md:leading-[45px]  max-w-[715px] w-full mx-auto "
            >
              {data?.Branch?.Title}
            </h2>
            <p
              data-aos="fade-up"
              className="text-[16px] leading-[24px] text-center font-normal text-[#484848] mt-[16px] max-md:text-[14px] max-md:leading-[22px] max-w-[715px] w-full mx-auto"
            >
              {data?.Branch?.Description}
            </p>

            <div className="branch_mainwrapper">
              <div className="branch_list flex justify-start items-start gap-[28px] mt-[42px] flex-wrap">
                <BranchBox branches={branchHome12} isHome={true} />
              </div>
              <div
                data-aos="fade-up"
                className="btn_wrapper flex justify-center mt-[42px]"
              >
                <Link
                  className="flex items-center justify-center gap-2 py-[19px] px-[68px] bg-[#008BD2] text-[16px]  text-white rounded-[10px] 
      max-md:text-[14px] max-md:py-[14px] max-md:px-[40px] hover:bg-[#004a91]  transition ease-in-out duration-300"
                  href="/branch"
                >
                  View all branches
                </Link>
              </div>
            </div>
          </div>

          <div className="avertise_cardwrapper mt-[100px] flex justify-center gap-[24px] max-xl:flex-col max-md:mt-[60px]">
            {data?.Conncet_Section &&
              data?.Conncet_Section?.length > 0 &&
              data?.Conncet_Section?.map((item: any, index: number) => (
                <div
                  key={index}
                  data-aos="fade-up"
                  className="card bg-[#008BD2] rounded-[14px] flex items-center w-[calc(100%/2-13px)] max-xl:w-full max-xl:max-w-[700px] max-xl:mx-auto max-md:flex-col"
                >
                  <div className="content p-[32px] max-w-[360px] w-full max-xl:max-w-[380px] max-md:max-w-full">
                    <h3 className="text-[30px] leading-tight font-bold text-white">
                      {item?.Title || ""}
                    </h3>
                    <p className="text-[14px] font-light text-white mt-[22px]">
                      {item?.Description || ""}
                    </p>

                    {/* Only render buttons section if Button array exists and has items */}
                    {item?.Button && item?.Button.length > 0 && (
                      <div className="download_btnwrapper flex justify-start items-center gap-[11px] mt-[44px]">
                        {item?.Button.map((btn: any, btnIndex: number) => (
                          <React.Fragment key={btnIndex}>
                            {/* Handle case where Image is null but Label exists */}
                            {!btn?.Image && btn?.Label ? (
                              <Link href={btn?.Link || "#"} target="_blank">
                                <div className="btn_wrapper mt-[28px]">
                                  <Button
                                    className="bg-white font-medium !text-[#141414] !text-[16px] !px-[24px] !py-[14px]"
                                    children={btn?.Label}
                                  />
                                </div>
                              </Link>
                            ) : btn?.Image ? (
                              /* Handle case where Image exists */
                              <div>
                                <Link href={btn?.Link || "#"} target="_blank">
                                  <Image
                                    className="w-full h-full object-cover max-lg:object-top"
                                    src={
                                      process.env.NEXT_PUBLIC_API_BASE_URL +
                                      btn?.Image?.url
                                    }
                                    alt={btn?.Image?.name || "icon"}
                                    width={1000}
                                    height={1000}
                                  />
                                </Link>
                              </div>
                            ) : null}
                          </React.Fragment>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* Handle different images based on index */}
                  {item?.SRK_Image && item?.SRK_Image?.url && (
                    <div
                      className={`banner self-stretch ${
                        index === 0
                          ? "max-w-[230px]"
                          : "max-w-[276px] max-h-[305px] mt-12"
                      } w-full max-md:ml-auto max-md:pr-[30px]`}
                    >
                      <Image
                        className={`w-full h-full object-contain ${
                          index === 0 ? "object-bottom" : "object-center"
                        }`}
                        src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${item?.SRK_Image?.url}`}
                        alt={item?.SRK_Image?.name || "Image"}
                        width={1000}
                        height={1000}
                      />
                    </div>
                  )}
                </div>
              ))}
          </div>

          {/* <div className="faq_wrapper pt-[100px] max-md:pt-[70px]">
            <div className="title_wrapper max-w-[650px] w-full mx-auto">
              <h2
                data-aos="fade-up"
                className="text-[44px] text-center leading-[58px] text-[#0F0F0F] font-semibold max-md:text-[35px] max-md:leading-[45px] "
              >
                {data?.Faq?.Title}
              </h2>
              <p
                data-aos="fade-up"
                className="text-[16px] leading-[24px] text-center font-normal text-[#505050] mt-[16px] max-md:text-[14px] max-md:leading-[22px]"
              >
                {data?.Faq?.Description}
              </p>
            </div>
            <div data-aos="fade-up" className="contact_faq mt-[8px]">
              <ContactAccordion data={data?.FAQ_Section} />
            </div>
          </div> */}
        </div>
      </div>
    </main>
  );
}
