import YouthDirectors from "@/components/about/YouthDirectors";
import CommonBanner from "@/components/common/CommonBanner";
import { Metadata } from "next";
import CommonText from "@/components/common/CommonText";
import { generateMetadata as generateMetadataUtil } from "@/lib/metaData";
import { GetNextGenerationPage, GetNextGenerationSeo } from "@/lib/api/cms";
import YoungDirectors from "@/components/about/youngDirectors";
import CommonTextBlue from "@/components/common/CommonTextBlue";
import NextGenDirectors from "@/components/about/NextGenDirectors";

export async function generateMetadata(): Promise<Metadata> {
  const { data: SEOData } = await GetNextGenerationSeo();

  return await generateMetadataUtil(
    SEOData?.SEO?.Title,
    SEOData?.SEO?.Description,
    "/directors",
    SEOData?.SEO?.keywords
  );
}

export default async function Directors() {
  const { data } = await GetNextGenerationPage();
  return (
    <main>
      <CommonBanner
        backgroundImage={`${process.env.NEXT_PUBLIC_API_BASE_URL}${
          data?.Banner?.Banner?.url || ""
        }`}
        title={data?.Banner?.Title || ""}
        description={data?.Banner?.Description || ""}
        sideImage={`${process.env.NEXT_PUBLIC_API_BASE_URL}${
          data?.Banner?.SRK_Image?.url || ""
        }`}
        sideImageHeight={1000}
        sideImageWidth={1000}
        sideImageClassName="mt-[.6rem] md:w-[560px] md:h-[600px] object-contain w-full h-full"
        backgroundImageHeight={1000}
        backgroundImageWidth={1000}
        underOverlay={false}
        breadcrumbs={[
          { label: "About Us", href: "/about" },
          { label: "Our Directors", href: "#" },
        ]}
      />
      <div className="youth_directors py-[100px] max-md:py-[50px] relative">
        <div className=" absolute top-0 left-0 w-full h-[900px] bg-[linear-gradient(180deg,_#008BD2_6.98%,_rgba(0,139,210,0.804112)_37.87%,_rgba(0,139,210,0.480664)_53.78%,_rgba(0,139,210,0.146018)_69.69%,_rgba(0,139,210,0)_80.59%)]"></div>
        <div className="container">
          <div
            data-aos="fade-up"
            className="title max-w-[630px] w-full mx-auto"
          >
            {data?.Header?.Title && (
              <h2 className="text-[white] text-[44px] font-semibold text-center max-md:text-[35px] max-md:leading-[44px]">
                <CommonText title={data?.Header?.Title} />
              </h2>
            )}
            {data?.Header?.Description && (
              <p className="text-[16px] leading-[24px] fontnormal text-[white] text-center mt-[22px] max-md:text-[14px] max-md:leading-[22px] max-md:mt-[14px]">
                {data?.Header?.Description}
              </p>
            )}
          </div>

          <div className="grid_wrapper flex flex-wrap justify-start items-start gap-[22px] mt-[46px] max-xl:justify-center">
            <NextGenDirectors directors={data?.Nextgen_Card} />
          </div>
          <div className="independent_directors pt-[100px] max-md:pt-[60px]">
            <div
              data-aos="fade-up"
              className="title_wrapper  max-w-[620px] w-full mx-auto"
            >
              <h3 className="font-semibold text-[44px] text-[#141414] text-center max-md:text-[35px]">
                <CommonTextBlue title={data?.Header2?.Title} />
              </h3>
              <p className="text-[16px] font-[400] text-[#5B5B5B] mt-[16px] text-center max-md:text-[14px]  max-md:mt-[14px]">
                {data?.Header2?.Description}
              </p>
            </div>
            <div className="grid_wrapper flex flex-wrap justify-start items-start gap-[16px] mt-[46px] max-xl:justify-center">
              <YouthDirectors directors={data?.Independent_Directors_Card} />
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
