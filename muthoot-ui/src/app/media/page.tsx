import CommonBanner from "@/components/common/CommonBanner";
import AwardsSlider from "@/components/media/AwardsSlider";
import TestimonialSlider from "@/components/media/TestimonialSlider";
import NewsBox from "@/components/news/NewsBox";
import Link from "next/link";
import React from "react";
import { generateMetadata as generateMetadataUtil } from "@/lib/metaData";
import { Metadata } from "next";
import { GetMediaPage, GetMediaSeo } from "@/lib/api/cms";
import {
  GetAwards,
  GetBlogsList,
  GetNewsEvents,
  GetNewsList,
  GetTestimonialListings,
} from "@/lib/api/general";
import CommonText from "@/components/common/CommonText";

export async function generateMetadata(): Promise<Metadata> {
  const { data: SEOData } = await GetMediaSeo();

  return await generateMetadataUtil(
    SEOData?.SEO?.Title,
    SEOData?.SEO?.Description,
    "/media",
    SEOData?.SEO?.keywords
  );
}
export default async function Media() {
  const { data } = await GetMediaPage();
  const { data: awards } = await GetAwards();
  const { data: newsEvents } = await GetNewsList();
  const { data: blogData } = await GetBlogsList();
  const { data: testimonial } = await GetTestimonialListings();

  return (
    <main>
      <CommonBanner
        backgroundImage={`${process.env.NEXT_PUBLIC_API_BASE_URL}${data?.Banner?.Banner?.url}`}
        title={data?.Banner?.Title}
        description={data?.Banner?.Description}
        sideImage={`${process.env.NEXT_PUBLIC_API_BASE_URL}${data?.Banner?.SRK_Image?.url}`}
        sideImageHeight={1000}
        sideImageWidth={1000}
        sideImageClassName="w-[379px] h-[429px] mt-2.5 md:ml-9 object-contain "
        backgroundImageHeight={1000}
        backgroundImageWidth={1000}
        breadcrumbs={[{ label: "Media", href: "/media" }]}
      />

      <div className="medialisting_page pb-[100px] max-md:pb-[50px] pt-[60px] max-md:pt-[30px] relative ">
      <div className=" absolute top-0 left-0 w-full h-[1050px] bg-[linear-gradient(180deg,_#008BD2_6.98%,_rgba(0,139,210,0.804112)_37.87%,_rgba(0,139,210,0.480664)_53.78%,_rgba(0,139,210,0.146018)_69.69%,_rgba(0,139,210,0)_80.59%)]"></div>
        <div className="container relative z-[9]">
          <div className="media_wrapper mb-[100px] max-md:mb-[60px]">
            <div className="media_head flex justify-between items-end mb-[36px] max-lg:flex-col max-lg:items-start max-lg:gap-[30px]">
              <div className="title max-w-[616px] w-full">
                <h3 className="text-[44px] text-[white] font-semibold max-md:text-[35px] max-md:leading-[45px]">
                  <CommonText title={data?.News_Section?.Header?.Title} />
                </h3>
                <p className="text-[16px] font-normal text-[white] leading-[24px] mt-[16px] max-sm:text-[14px] max-sm:leading-[22px]">
                  {data?.News_Section?.Header?.Description}
                </p>
              </div>
              <div className="view_wrapper  max-lg:w-full max-lg:flex max-lg:justify-end">
                <Link
                  className="text-[16px] font-bold text-[#ffffff] flex justify-start items-center gap-[13px]"
                  href="/news"
                >
                  View All{" "}
                  <svg
                    width="8"
                    height="14"
                    viewBox="0 0 8 14"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M1 13L7 7L1 1"
                      stroke="#ffffff"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>{" "}
                </Link>
              </div>
            </div>
            <div className="news_wrapper">
              <div className="news_listing flex justify-start gap-[36px] flex-wrap items-start mt-[36px]">
                {newsEvents?.length > 0 ? (
                  <NewsBox data={newsEvents} />
                ) : (
                  <p></p>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="blogs_listwrapper py-[100px] max-md:py-[50px] bg-[#008BD2]">
          <div className="container">
            <div className="media_wrapper ">
              <div className="media_head flex justify-between items-end mb-[36px] max-lg:flex-col max-lg:items-start max-lg:gap-[30px]">
                <div className="title max-w-[616px] w-full">
                  <h3 className="text-[44px] text-white font-semibold max-md:text-[35px] max-md:leading-[45px]">
                    <CommonText title={data?.Blog_Section?.Header?.Title} />
                  </h3>
                  <p className="text-[16px] font-normal text-white leading-[24px] mt-[16px] max-sm:text-[14px] max-sm:leading-[22px]">
                    {data?.Blog_Section?.Header?.Description}
                  </p>
                </div>
                <div className="view_wrapper  max-lg:w-full max-lg:flex max-lg:justify-end">
                  <Link
                    className="text-[16px] font-bold text-white flex justify-start items-center gap-[13px]"
                    href="/blogs"
                  >
                    View All{" "}
                    <svg
                      width="8"
                      height="14"
                      viewBox="0 0 8 14"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M1 13L7 7L1 1"
                        stroke="white"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>{" "}
                  </Link>
                </div>
              </div>
              <div className="blue_blogwrapper">
                <div className="news_listing flex justify-start gap-[36px] flex-wrap items-start mt-[36px]">
                  {blogData?.length > 0 ? <NewsBox data={blogData} /> : <p></p>}
                </div>
              </div>
            </div>
          </div>
        </div>
        {/* awards */}
        <div className="awards_listwrapper mt-[100px] max-md:mt-[50px] ">
          <div className="container">
            <div className="media_wrapper ">
              <div className="media_head flex justify-between items-end mb-[36px] max-lg:flex-col max-lg:items-start max-lg:gap-[30px]">
                <div className="title max-w-[616px] w-full">
                  <h3 className="text-[44px] text-[#141414] font-semibold max-md:text-[35px] max-md:leading-[45px]">
                    {data?.Award_Section?.Header?.Title}
                  </h3>
                  <p className="text-[16px] font-normal text-[#484848] leading-[24px] mt-[16px] text-justify max-sm:text-[14px] max-sm:leading-[22px]">
                    {data?.Award_Section?.Header?.Description}
                  </p>
                </div>
                <div className="view_wrapper  max-lg:w-full max-lg:flex max-lg:justify-end">
                  <Link
                    className="text-[16px] font-bold text-[#008BD2] flex justify-start items-center gap-[13px]"
                    href="/awards"
                  >
                    View All{" "}
                    <svg
                      width="8"
                      height="14"
                      viewBox="0 0 8 14"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M1 13L7 7L1 1"
                        stroke="#008BD2"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>{" "}
                  </Link>
                </div>
              </div>

              <div className="awards_sliderwrapper">
                {awards?.length > 0 ? <AwardsSlider data={awards} /> : <p></p>}
              </div>
            </div>
          </div>
        </div>
        {/* testmonilas  */}
        <div className="awards_listwrapper mt-[100px] max-md:mt-[50px] ">
          <div className="container">
            <div className="media_wrapper ">
              <div className="media_head flex justify-between items-end mb-[36px] max-lg:flex-col max-lg:items-start max-lg:gap-[30px]">
                <div className="title max-w-[616px] w-full">
                  <h3 className="text-[44px] text-[#141414] font-semibold max-md:text-[35px] max-md:leading-[45px]">
                    {data?.Testimonial_Section?.Header?.Title}
                  </h3>
                  <p className="text-[16px] font-normal text-[#484848] leading-[24px] mt-[16px] text-justify max-sm:text-[14px] max-sm:leading-[22px]">
                    {data?.Testimonial_Section?.Header?.Description}
                  </p>
                </div>
                <div className="view_wrapper  max-lg:w-full max-lg:flex max-lg:justify-end">
                  <Link
                    className="text-[16px] font-bold text-[#008BD2] flex justify-start items-center gap-[13px]"
                    href="/testimonials"
                  >
                    View All{" "}
                    <svg
                      width="8"
                      height="14"
                      viewBox="0 0 8 14"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M1 13L7 7L1 1"
                        stroke="#008BD2"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>{" "}
                  </Link>
                </div>
              </div>

              <div className="awards_sliderwrapper">
                {testimonial?.length > 0 ? (
                  <TestimonialSlider testimonial={testimonial} />
                ) : (
                  <p></p>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
