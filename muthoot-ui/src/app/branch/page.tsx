import CommonBanner from "@/components/common/CommonBanner";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import Button from "@/components/ui/Button";
import BranchBox from "@/components/contact/BranchBox";
import { GetBranches, GetBranchPage, GetBranchSeo } from "@/lib/api/cms";
import { generateMetadata as generateMetadataUtil } from "@/lib/metaData";
import { Metadata } from "next";
import CommonText from "@/components/common/CommonText";
import BranchFilter from "@/components/Filter/BranchFilter";
import BranchList from "./BranchList";
export async function generateMetadata(): Promise<Metadata> {
  const { data: SEOData } = await GetBranchSeo();
  return await generateMetadataUtil(
    SEOData?.SEO?.Title,
    SEOData?.SEO?.Description,
    "/csr",
    SEOData?.SEO?.keywords
  );
}

export default async function Branch() {
  const { data } = await GetBranchPage();

  return (
    <main>
      {data?.Banner && (
        <CommonBanner
          backgroundImage={`${process.env.NEXT_PUBLIC_API_BASE_URL}${
            data?.Banner?.Banner?.url || ""
          }`}
          title={data?.Banner?.Title || ""}
          description={data?.Banner?.Description || ""}
          sideImage={`${process.env.NEXT_PUBLIC_API_BASE_URL}${
            data?.Banner?.SRK_Image?.url || ""
          }`}
          sideImageHeight={1000}
          sideImageWidth={1000}
          sideImageClassName="mt-[.4rem] md:w-[435px] md:h-[401px]"
          backgroundImageHeight={1000}
          backgroundImageWidth={1000}
          breadcrumbs={[
            { label: "Contact Us", href: "/contact" },
            { label: "Branches Near", href: "#" },
          ]}
        />
      )}

      <div className="branch_page py-[100px] relative">
        <div className=" absolute top-0 left-0 w-full h-full bg-[linear-gradient(180deg,_#008BD2_6.98%,_rgba(0,139,210,0.804112)_37.87%,_rgba(0,139,210,0.480664)_53.78%,_rgba(0,139,210,0.146018)_69.69%,_rgba(0,139,210,0)_80.59%)]"></div>
        <div className="container">
          <div className="title_wrapper max-w-[900px] w-full mx-auto">
            <h2
              data-aos="fade-up"
              className="text-[44px] text-center leading-[58px] text-[#ffff] font-semibold max-md:text-[35px] max-md:leading-[45px] "
            >
              <CommonText title={data?.Header?.Title} />
            </h2>
            <p
              data-aos="fade-up"
              className="text-[16px] leading-[24px] text-center font-normal text-[#FFFFFF] mt-[16px] max-md:text-[14px] max-md:leading-[22px]"
            >
              {data?.Header?.Description}
            </p>
          </div>
          <BranchList />
        </div>
      </div>
    </main>
  );
}
