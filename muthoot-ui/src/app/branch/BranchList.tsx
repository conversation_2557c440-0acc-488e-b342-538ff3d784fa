"use client";
import BranchBox from "@/components/contact/BranchBox";
import BranchFilter from "@/components/Filter/BranchFilter";
import endpoints from "@/endpoints";
import React, { useEffect, useState } from "react";

const BranchList = () => {
  interface Branch {
    State: string;
    District: string;
    Filter_Location: string;
  }

  const [branches, setBranches] = useState<Branch[]>([]);
  const [filteredBranches, setFilteredBranches] = useState<Branch[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    fetchBranches();
  }, []);

  const fetchBranches = async (filterParams = "") => {
    setIsLoading(true);
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}${endpoints.getBranches}${filterParams}`
      );
      const data = await response.json();
      if (!filterParams) {
        setBranches(data);
      }
      setFilteredBranches(data);
    } catch (error) {
      console.error("Error fetching branches:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleFilteredBranches = (filters: any) => {
    let filterParams = "&";

    if (filters.state) {
      filterParams += `filters[State][$eq]=${filters.state}`;
    }

    if (filters.district) {
      filterParams += filterParams !== "?" ? "&" : "";
      filterParams += `filters[District][$eq]=${filters.district}`;
    }

    if (filters.location) {
      filterParams += filterParams !== "?" ? "&" : "";
      filterParams += `filters[Filter_Location][$eq]=${filters.location}`;
    }

    if (filterParams !== "?") {
      fetchBranches(filterParams);
    } else {
      setFilteredBranches(branches);
    }
  };

  return (
    <div>
      <BranchFilter data={branches} onFilter={handleFilteredBranches} />
      <div className="branch_listwrapper flex justify-start items-start gap-[28px] mt-[36px] flex-wrap">
        {isLoading ? (
          <div className="flex justify-center items-center p-8 w-full">
            <div className="relative w-16 h-16">
              <div className="absolute top-0 left-0 w-full h-full">
                <div className="w-16 h-16 rounded-full border-4 border-[rgba(0,149,218,0.1)]"></div>
                <div className="absolute top-0 left-0 w-16 h-16 rounded-full border-4 border-t-[#0095DA] animate-spin"></div>
              </div>
              <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center">
                <svg
                  className="w-6 h-6 text-[#0095DA] animate-pulse"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </div>
            </div>
          </div>
        ) : (
          <BranchBox branches={filteredBranches} />
        )}
      </div>
    </div>
  );
};

export default BranchList;
