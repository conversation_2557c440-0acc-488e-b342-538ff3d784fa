import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import "../styles/styles.scss";
import Footer from "@/components/footer/Footer";
import Header from "@/components/header/Header";
import Miniheader from "@/components/header/Miniheader";
import Header2 from "@/components/header/Header2";
import AOSInitializer from "@/components/ui/Aos"; // Import the new AOS component
import MainHeader from "@/components/header/MainHeader";
import Script from "next/script";
import { Toaster } from "react-hot-toast";
import ServiceForm from "@/components/form/ServiceForm";
import RouteChangeProvider from "@/components/providers/RouteChangeProvider";
import { Suspense } from "react";
import LoanList from "@/components/common/LoanList";
import { GetSideBar } from "@/lib/api/common";

export const revalidate = 10;

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Muthoot Capital",
  description:
    "Muthoot Capital Services, part of the Muthoot Pappachan Group, specializes in vehicle financing and diverse financial solutions, promoting financial inclusion across India.",
};

interface LayoutData {
  footer: any;
  header: any;
}

async function getLayoutData(): Promise<LayoutData> {
  const { GetFooter, GetHeader } = await import("@/lib/api/common");
  try {
    const [footerData, headerData] = await Promise.all([
      GetFooter(),
      GetHeader(),
    ]);

    return {
      footer: footerData.data,
      header: headerData.data,
    };
  } catch (error) {
    console.error("Error fetching layout data:", error);
    throw new Error("Failed to fetch layout data");
  }
}

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const layoutData = await getLayoutData();
  const { data: sidebarData } = await GetSideBar();

  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <Suspense fallback={null}>
          <RouteChangeProvider>
            <Toaster />
            <Miniheader data={layoutData.header.HeaderTop} />
            <MainHeader data={layoutData.header} />
            <LoanList data={sidebarData} />

            <div
              id="google_translate_element"
              className="google-translate-container hidden"
            />
            <AOSInitializer />
            {children}
            <ServiceForm />
            <Footer />
            <Script id="google-translate-init" strategy="afterInteractive">
              {`
                function googleTranslateElementInit2() {
                  new google.translate.TranslateElement({
                    pageLanguage: 'en',
                    autoDisplay: false
                  }, 'google_translate_element');
                }
              `}
            </Script>
            <Script
              src="https://translate.google.com/translate_a/element.js?cb=googleTranslateElementInit2"
              strategy="afterInteractive"
              async
            />
            <Script
              src="https://translate.google.com/translate_a/element.js?cb=googleTranslateElementInit2"
              strategy="afterInteractive"
              async
            />
            <Script
              id="google-translate-fire-event"
              strategy="afterInteractive"
            >
              {`
                function GTranslateFireEvent(a, b) {
                  try {
                    if (document.createEvent) {
                      var c = document.createEvent("HTMLEvents");
                      c.initEvent(b, true, true);
                      a.dispatchEvent(c);
                    } else {
                      var c = document.createEventObject();
                      a.fireEvent('on' + b, c);
                    }
                  } catch (e) {}
                }
                function doGTranslate(a) {
                  if (!a) return;
                  var value = a.value || a;
                  if (value === '') return;
                  var b = value.split('|')[1];
                  var c = document.querySelector('select.goog-te-combo');
                  if (!c || !document.getElementById('google_translate_element')) {
                    // Retry only once after a shorter delay
                    setTimeout(function() {
                      var retryC = document.querySelector('select.goog-te-combo');
                      if (retryC) {
                        retryC.value = b;
                        GTranslateFireEvent(retryC, 'change');
                      }
                    }, 100); // Reduced from 500ms to 100ms
                    return;
                  }
                  c.value = b;
                  GTranslateFireEvent(c, 'change');
                }
              `}
            </Script>
          </RouteChangeProvider>
        </Suspense>
      </body>
    </html>
  );
}
