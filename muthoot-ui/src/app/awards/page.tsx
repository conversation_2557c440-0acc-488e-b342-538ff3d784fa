import AwardBox from "@/components/awards/AwardBox";
import CommonBanner from "@/components/common/CommonBanner";
import React from "react";
import { generateMetadata as generateMetadataUtil } from "@/lib/metaData";
import { Metadata } from "next";
import { getAwardsPage, getAwardsSeo } from "@/lib/api/cms";
import CommonText from "@/components/common/CommonText";
import { GetAwards } from "@/lib/api/general";

export async function generateMetadata(): Promise<Metadata> {
  const { data: SEOData } = await getAwardsSeo();

  return await generateMetadataUtil(
    SEOData?.SEO?.Title || "",
    SEOData?.SEO?.Description || "",
    "/digital-intiatives",
    SEOData?.SEO?.keywords || []
  );
}

export default async function Awards() {
  const { data = {} } = await getAwardsPage();
  const { data: awardsBoxData = [] } = await GetAwards();

  return (
    <main>
      <CommonBanner
        backgroundImage={`${process.env.NEXT_PUBLIC_API_BASE_URL}${
          data?.Banner?.Banner?.url || ""
        }`}
        title={data?.Banner?.Title || ""}
        description={data?.Banner?.Description || ""}
        sideImage={`${process.env.NEXT_PUBLIC_API_BASE_URL}${
          data?.Banner?.SRK_Image?.url || ""
        }`}
        sideImageHeight={1000}
        sideImageWidth={1000}
        sideImageClassName="mt-[.1rem] md:w-[431px] md:h-[434px]"
        backgroundImageHeight={1000}
        backgroundImageWidth={1000}
        breadcrumbs={[{ label: "Awards", href: "/awards" }]}
      />

      <div className="awards_page py-[100px] max-md:py-[50px] relative">
        <div className=" absolute top-0 left-0 w-full h-full bg-[linear-gradient(180deg,_#008BD2_6.98%,_rgba(0,139,210,0.804112)_37.87%,_rgba(0,139,210,0.480664)_53.78%,_rgba(0,139,210,0.146018)_69.69%,_rgba(0,139,210,0)_80.59%)]"></div>
        <div className="container">
          <div className="title_wrapper max-w-[900px] w-full mx-auto">
            <h2
              data-aos="fade-up"
              className="text-[44px] text-center leading-[58px] text-[#FFFFFF] font-semibold max-md:text-[35px] max-md:leading-[45px] "
            >
              <CommonText title={data?.Header?.Title || "Awards"} />{" "}
            </h2>
            <p
              data-aos="fade-up"
              className="text-[16px] leading-[24px] text-center font-normal text-[#FFFFFF] mt-[16px] max-md:text-[14px] max-md:leading-[22px]"
            >
              {data?.Header?.Description || "No description available."}{" "}
            </p>
          </div>
          <div className="awards_listing flex justify-start items-start gap-[38px] flex-wrap mt-[42px]">
            <AwardBox data={awardsBoxData || []} />{" "}
          </div>
        </div>
      </div>
    </main>
  );
}
