import React from "react";
import Image from "next/image";
import { Checkbox } from "@/components/ui/checkbox";
import Button from "@/components/ui/Button";
import Link from "next/link";

export default function AddressDetails() {
  return (
    <main>
      <div className="adress_detailwrapper py-[81px] relative max-md:py-[50px] ">
        <div className="gradient_shade w-full h-[723px] absolute top-0 left-0 bg-[linear-gradient(180.84deg,_#008BD2_-64.11%,_rgba(0,139,210,0.804112)_-45.11%,_rgba(0,139,210,0.480664)_1.8%,_rgba(0,139,210,0.146018)_35.65%,_rgba(0,139,210,0)_80.15%)]"></div>
        <div className="cibil_srk max-w-[465px] w-full absolute right-0 bottom-0">
          <Image
            className="aspect-[465/732] object-contain  w-full h-full object-right"
            src="/address_srk.png"
            alt="srk_banner"
            width={465}
            height={732}
          />
        </div>
        <div className="container">
          <div className="personaldetails_box p-[36px] rounded-[20px] bg-[rgba(233,248,255,0.8)] max-w-[918px] w-full border-[2px] border-[#9cd0ed] relative z-[9] max-md:p-[16px]">
            <h3 className="text-[26px] font-semibold text-[#141414]">
              Address details
            </h3>

            <div className="input_wrapper flex justify-start items-start gap-x-[18px] gap-y-[20px] flex-wrap mt-[38px] max-md:flex-col max-sm:gap-y-[15px] max-md:mt-[20px]">
              <div className="input_group w-full max-md:w-full ">
                <label
                  className="text-[14px] font-semibold text-[#141414]"
                  htmlFor=""
                >
                  Address for correspondence{" "}
                  <span className="text-[#FF0000]">*</span>
                </label>
                <div className="input_parent mt-[8px]">
                  <input
                    placeholder="Street address line"
                    className="border  w-full h-[46px] border-[#90C9E8] rounded-[8px] p-[16px] bg-white text-[14px] font-normal text-[#6C727F] max-sm:h-[40px]"
                    type="text"
                  />
                </div>
                <div className="input_parent mt-[20px] w-full">
                  <input
                    placeholder="Street address line 02"
                    className="border border-[#90C9E8] h-[46px] w-full rounded-[8px] p-[16px] bg-white text-[14px] font-normal text-[#6C727F] max-sm:h-[40px]"
                    type="text"
                  />
                </div>
              </div>
              <div className="input_group  w-[calc(100%/2-9px)] max-md:w-full">
                <label
                  className="text-[14px] font-semibold text-[#141414]"
                  htmlFor=""
                >
                  City <span className="text-[#FF0000]">*</span>
                </label>
                <div className="input_parent mt-[8px]">
                  <input
                    placeholder="Enter City"
                    className="border border-[#90C9E8] h-[46px] w-full rounded-[8px] p-[16px] bg-white text-[14px] font-normal text-[#6C727F] max-sm:h-[40px]"
                    type="text"
                  />
                </div>
              </div>
              <div className="input_group  w-[calc(100%/2-9px)] max-md:w-full">
                <label
                  className="text-[14px] font-semibold text-[#141414]"
                  htmlFor=""
                >
                  State/Province<span className="text-[#FF0000]">*</span>
                </label>
                <div className="input_parent mt-[8px]">
                  <input
                    placeholder="Enter State"
                    className="border border-[#90C9E8] h-[46px] w-full rounded-[8px] p-[16px] bg-white text-[14px] font-normal text-[#6C727F] max-sm:h-[40px]"
                    type="text"
                  />
                </div>
              </div>
              <div className="input_group  w-[calc(100%/2-9px)] max-md:w-full">
                <label
                  className="text-[14px] font-semibold text-[#141414]"
                  htmlFor=""
                >
                  Phone number <span className="text-[#FF0000]">*</span>
                </label>
                <div className="input_parent mt-[8px]">
                  <input
                    placeholder="Enter Number"
                    className="border border-[#90C9E8] h-[46px] w-full rounded-[8px] p-[16px] bg-white text-[14px] font-normal text-[#6C727F] max-sm:h-[40px]"
                    type="text"
                  />
                </div>
              </div>
              <div className="input_group  w-[calc(100%/2-9px)] max-md:w-full">
                <label
                  className="text-[14px] font-semibold text-[#141414]"
                  htmlFor=""
                >
                  Email<span className="text-[#FF0000]">*</span>
                </label>
                <div className="input_parent mt-[8px]">
                  <input
                    placeholder="Enter Email"
                    className="border border-[#90C9E8] h-[46px] w-full rounded-[8px] p-[16px] bg-white text-[14px] font-normal text-[#6C727F] max-sm:h-[40px]"
                    type="email"
                  />
                </div>
              </div>

              <div className="input_group  w-full max-md:w-full">
                <label
                  className="text-[14px] font-semibold text-[#141414]"
                  htmlFor=""
                >
                  Proof of address to be provided by applicant{" "}
                  <span className="text-[#FF0000]">*</span>
                </label>
                <div className="check_wrapper mt-[8px] flex justify-start items-start gap-x-[18px] gap-y-[15px] flex-wrap  max-md:flex-col max-sm:gap-y-[15px] mb-[30px]">
                  <div className="single_item flex justify-start items-center gap-[12px] w-[calc(100%/2-9px)] max-md:w-full">
                    <Checkbox className="w-[22px] h-[22px] bg-white rounded-[4px] border-[#90C9E8]" />
                    <h5 className="text-[14px] font-bold text-[#141414]">
                      Passport
                    </h5>
                  </div>
                  <div className="single_item flex justify-start items-center gap-[12px] w-[calc(100%/2-9px)] max-md:w-full">
                    <Checkbox className="w-[22px] h-[22px] bg-white rounded-[4px] border-[#90C9E8]" />
                    <h5 className="text-[14px] font-bold text-[#141414]">
                      Ration Card
                    </h5>
                  </div>
                  <div className="single_item flex justify-start items-center gap-[12px] w-[calc(100%/2-9px)] max-md:w-full">
                    <Checkbox className="w-[22px] h-[22px] bg-white rounded-[4px] border-[#90C9E8]" />
                    <h5 className="text-[14px] font-bold text-[#141414]">
                      Driving Licence
                    </h5>
                  </div>
                  <div className="single_item flex justify-start items-center gap-[12px] w-[calc(100%/2-9px)] max-md:w-full">
                    <Checkbox className="w-[22px] h-[22px] bg-white rounded-[4px] border-[#90C9E8]" />
                    <h5 className="text-[14px] font-bold text-[#141414]">
                      Voter identity card
                    </h5>
                  </div>
                  <div className="single_item flex justify-start items-center gap-[12px] w-[calc(100%/2-9px)] max-md:w-full">
                    <Checkbox className="w-[22px] h-[22px] bg-white rounded-[4px] border-[#90C9E8]" />
                    <h5 className="text-[14px] font-bold text-[#141414]">
                      Registered lease / sale agreement of residence
                    </h5>
                  </div>
                  <div className="single_item flex justify-start items-center gap-[12px] w-[calc(100%/2-9px)] max-md:w-full">
                    <Checkbox className="w-[22px] h-[22px] bg-white rounded-[4px] border-[#90C9E8]" />
                    <h5 className="text-[14px] font-bold text-[#141414]">
                      Latest bank account statement / Passbook
                    </h5>
                  </div>
                  <div className="single_item flex justify-start items-center gap-[12px] w-[calc(100%/2-9px)] max-md:w-full">
                    <Checkbox className="w-[22px] h-[22px] bg-white rounded-[4px] border-[#90C9E8]" />
                    <h5 className="text-[14px] font-bold text-[#141414]">
                      Latest Telephone Bill
                    </h5>
                  </div>
                  <div className="single_item flex justify-start items-center gap-[12px] w-[calc(100%/2-9px)] max-md:w-full">
                    <Checkbox className="w-[22px] h-[22px] bg-white rounded-[4px] border-[#90C9E8]" />
                    <h5 className="text-[14px] font-bold text-[#141414]">
                      Latest electricity Bill
                    </h5>
                  </div>
                  <div className="single_item flex justify-start items-center gap-[12px] w-[calc(100%/2-9px)] max-md:w-full">
                    <Checkbox className="w-[22px] h-[22px] bg-white rounded-[4px] border-[#90C9E8]" />
                    <h5 className="text-[14px] font-bold text-[#141414]">
                      Latest Gas Bill
                    </h5>
                  </div>
                  <div className="single_item flex justify-start items-center gap-[12px] w-[calc(100%/2-9px)] max-md:w-full">
                    <Checkbox className="w-[22px] h-[22px] bg-white rounded-[4px] border-[#90C9E8]" />
                    <h5 className="text-[14px] font-bold text-[#141414]">
                      Other
                    </h5>
                  </div>
                </div>
              </div>

              <div className="input_group w-full max-md:w-full ">
                <label
                  className="text-[14px] font-semibold text-[#141414]"
                  htmlFor=""
                >
                  Permeant address of resident applicant if different from above{" "}
                  <span className="text-[#FF0000]">*</span>
                </label>
                <div className="input_parent mt-[8px]">
                  <input
                    placeholder="Street address line"
                    className="border  w-full h-[46px] border-[#90C9E8] rounded-[8px] p-[16px] bg-white text-[14px] font-normal text-[#6C727F] max-sm:h-[40px]"
                    type="text"
                  />
                </div>
                <div className="input_parent mt-[20px] w-full">
                  <input
                    placeholder="Street address line 02"
                    className="border border-[#90C9E8] h-[46px] w-full rounded-[8px] p-[16px] bg-white text-[14px] font-normal text-[#6C727F] max-sm:h-[40px]"
                    type="text"
                  />
                </div>
              </div>
              <div className="input_group  w-[calc(100%/2-9px)] max-md:w-full">
                <label
                  className="text-[14px] font-semibold text-[#141414]"
                  htmlFor=""
                >
                  City <span className="text-[#FF0000]">*</span>
                </label>
                <div className="input_parent mt-[8px]">
                  <input
                    placeholder="Enter City"
                    className="border border-[#90C9E8] h-[46px] w-full rounded-[8px] p-[16px] bg-white text-[14px] font-normal text-[#6C727F] max-sm:h-[40px]"
                    type="text"
                  />
                </div>
              </div>
              <div className="input_group  w-[calc(100%/2-9px)] max-md:w-full">
                <label
                  className="text-[14px] font-semibold text-[#141414]"
                  htmlFor=""
                >
                  State/Province<span className="text-[#FF0000]">*</span>
                </label>
                <div className="input_parent mt-[8px]">
                  <input
                    placeholder="Enter State"
                    className="border border-[#90C9E8] h-[46px] w-full rounded-[8px] p-[16px] bg-white text-[14px] font-normal text-[#6C727F] max-sm:h-[40px]"
                    type="text"
                  />
                </div>
              </div>
              <div className="input_group  w-[calc(100%/2-9px)] max-md:w-full">
                <label
                  className="text-[14px] font-semibold text-[#141414]"
                  htmlFor=""
                >
                  Phone number <span className="text-[#FF0000]">*</span>
                </label>
                <div className="input_parent mt-[8px]">
                  <input
                    placeholder="Enter Number"
                    className="border border-[#90C9E8] h-[46px] w-full rounded-[8px] p-[16px] bg-white text-[14px] font-normal text-[#6C727F] max-sm:h-[40px]"
                    type="text"
                  />
                </div>
              </div>
              <div className="input_group  w-[calc(100%/2-9px)] max-md:w-full">
                <label
                  className="text-[14px] font-semibold text-[#141414]"
                  htmlFor=""
                >
                  Email<span className="text-[#FF0000]">*</span>
                </label>
                <div className="input_parent mt-[8px]">
                  <input
                    placeholder="Enter Email"
                    className="border border-[#90C9E8] h-[46px] w-full rounded-[8px] p-[16px] bg-white text-[14px] font-normal text-[#6C727F] max-sm:h-[40px]"
                    type="email"
                  />
                </div>
              </div>

              <div className="input_group  w-full max-md:w-full">
                <label
                  className="text-[14px] font-semibold text-[#141414]"
                  htmlFor=""
                >
                  Proof of address to be provided by applicant{" "}
                  <span className="text-[#FF0000]">*</span>
                </label>
                <div className="check_wrapper mt-[8px] flex justify-start items-start gap-x-[18px] gap-y-[15px] flex-wrap  max-md:flex-col max-sm:gap-y-[15px] mb-[30px]">
                  <div className="single_item flex justify-start items-center gap-[12px] w-[calc(100%/2-9px)] max-md:w-full">
                    <Checkbox className="w-[22px] h-[22px] bg-white rounded-[4px] border-[#90C9E8]" />
                    <h5 className="text-[14px] font-bold text-[#141414]">
                      Passport
                    </h5>
                  </div>
                  <div className="single_item flex justify-start items-center gap-[12px] w-[calc(100%/2-9px)] max-md:w-full">
                    <Checkbox className="w-[22px] h-[22px] bg-white rounded-[4px] border-[#90C9E8]" />
                    <h5 className="text-[14px] font-bold text-[#141414]">
                      Ration Card
                    </h5>
                  </div>
                  <div className="single_item flex justify-start items-center gap-[12px] w-[calc(100%/2-9px)] max-md:w-full">
                    <Checkbox className="w-[22px] h-[22px] bg-white rounded-[4px] border-[#90C9E8]" />
                    <h5 className="text-[14px] font-bold text-[#141414]">
                      Driving Licence
                    </h5>
                  </div>
                  <div className="single_item flex justify-start items-center gap-[12px] w-[calc(100%/2-9px)] max-md:w-full">
                    <Checkbox className="w-[22px] h-[22px] bg-white rounded-[4px] border-[#90C9E8]" />
                    <h5 className="text-[14px] font-bold text-[#141414]">
                      Voter identity card
                    </h5>
                  </div>
                  <div className="single_item flex justify-start items-center gap-[12px] w-[calc(100%/2-9px)] max-md:w-full">
                    <Checkbox className="w-[22px] h-[22px] bg-white rounded-[4px] border-[#90C9E8]" />
                    <h5 className="text-[14px] font-bold text-[#141414]">
                      Registered lease / sale agreement of residence
                    </h5>
                  </div>
                  <div className="single_item flex justify-start items-center gap-[12px] w-[calc(100%/2-9px)] max-md:w-full">
                    <Checkbox className="w-[22px] h-[22px] bg-white rounded-[4px] border-[#90C9E8]" />
                    <h5 className="text-[14px] font-bold text-[#141414]">
                      Latest bank account statement / Passbook
                    </h5>
                  </div>
                  <div className="single_item flex justify-start items-center gap-[12px] w-[calc(100%/2-9px)] max-md:w-full">
                    <Checkbox className="w-[22px] h-[22px] bg-white rounded-[4px] border-[#90C9E8]" />
                    <h5 className="text-[14px] font-bold text-[#141414]">
                      Latest Telephone Bill
                    </h5>
                  </div>
                  <div className="single_item flex justify-start items-center gap-[12px] w-[calc(100%/2-9px)] max-md:w-full">
                    <Checkbox className="w-[22px] h-[22px] bg-white rounded-[4px] border-[#90C9E8]" />
                    <h5 className="text-[14px] font-bold text-[#141414]">
                      Latest electricity Bill
                    </h5>
                  </div>
                  <div className="single_item flex justify-start items-center gap-[12px] w-[calc(100%/2-9px)] max-md:w-full">
                    <Checkbox className="w-[22px] h-[22px] bg-white rounded-[4px] border-[#90C9E8]" />
                    <h5 className="text-[14px] font-bold text-[#141414]">
                      Latest Gas Bill
                    </h5>
                  </div>
                  <div className="single_item flex justify-start items-center gap-[12px] w-[calc(100%/2-9px)] max-md:w-full">
                    <Checkbox className="w-[22px] h-[22px] bg-white rounded-[4px] border-[#90C9E8]" />
                    <h5 className="text-[14px] font-bold text-[#141414]">
                      Other
                    </h5>
                  </div>
                </div>
              </div>

              <div className="file_uploadwrapper">
                <h3 className="text-[26px] text-[#141414] font-semibold">
                  File upload
                </h3>
                <p className="text-[14px] font-normal text-[#141414] mt-[5px]">
                  Please upload related photograph and documents
                </p>
              </div>
            </div>

            <div className="file_uploadbox border border-[#90C9E8] rounded-[8px] bg-white p-[60px] flex flex-col items-center mt-[16px] max-md:p-[40px]">
              <svg
                width="33"
                height="33"
                viewBox="0 0 33 33"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M25.785 28.5058H20.4514H19.0151H18.7049V21.3515H21.0447C21.6381 21.3515 21.9888 20.6772 21.6381 20.1917L17.0866 13.8938C16.7967 13.4892 16.1966 13.4892 15.9066 13.8938L11.3551 20.1917C11.0045 20.6772 11.3484 21.3515 11.9485 21.3515H14.2883V28.5058H13.9781H12.5419H6.3586C2.81855 28.3102 0 24.9995 0 21.4122C0 18.9375 1.34185 16.7798 3.33102 15.6133C3.14896 15.121 3.05456 14.5951 3.05456 14.0422C3.05456 11.5136 5.09767 9.47044 7.62628 9.47044C8.17246 9.47044 8.69841 9.56484 9.19064 9.7469C10.6539 6.64514 13.8096 4.49414 17.4777 4.49414C22.2248 4.50088 26.1357 8.13533 26.5807 12.7677C30.2286 13.3948 33 16.7731 33 20.5963C33 24.6825 29.8173 28.2226 25.785 28.5058Z"
                  fill="#008BD2"
                />
              </svg>

              <h5 className="text-[20px] font-bold text-[#008BD2] text-center">
                Browse files
              </h5>
              <h6 className="text-[14px] font-normal text-[#6C727F] mt-[8px] text-center">
                Drag and drop files here
              </h6>
            </div>

            <div className="btn_parentwrapper flex justify-between gap-[40px] items-center  mt-[57px] max-sm:gap-[20px] max-md:mt-[30px]">
              <div className="btn_wrapper  flex justify-center w-full max-w-[246px]">
              <Link className="w-full" href="/cibil/identityDetails">
                <Button
                  className="px-[20px] w-full bg-transparent border !border-[#008BD2] max-w-[246px] !text-[#008BD2] max-sm:px-[30px]"
                  children="Back"
                />
                </Link>
              </div>
              <div className="btn_wrapper  flex justify-center w-full max-w-[246px]">
                <Link className="w-full" href="/cibil/declaration">
                  <Button
                    className="px-[20px] w-full max-w-[246px] max-sm:px-[30px]"
                    children="Continue"
                  />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
