import React from "react";
import Image from "next/image";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import Button from "@/components/ui/Button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import Link from "next/link";

export default function IdentityDetails() {
  return (
    <main>
      <div className="personal_detailwrapper py-[81px] relative max-md:py-[50px]">
      <div className="gradient_shade w-full h-[723px] absolute top-0 left-0 bg-[linear-gradient(180.84deg,_#008BD2_-64.11%,_rgba(0,139,210,0.804112)_-45.11%,_rgba(0,139,210,0.480664)_1.8%,_rgba(0,139,210,0.146018)_35.65%,_rgba(0,139,210,0)_80.15%)]"></div>
        <div className="cibil_srk max-w-[465px] w-full absolute right-0 bottom-0">
          <Image
            className="aspect-[465/732] object-contain  w-full h-full object-right"
            src="/srk_identity.png"
            alt="srk_banner"
            width={465}
            height={732}
          />
        </div>
        <div className="container">
          <div className="personaldetails_box p-[36px] rounded-[20px] bg-[rgba(233,248,255,0.8)] max-w-[918px] w-full border-[2px] border-[#9cd0ed] relative z-[9] max-md:p-[16px]">
            <h3 className="text-[26px] font-semibold text-[#141414]">
              Identity details
            </h3>

            <div className="input_wrapper flex justify-start items-start gap-x-[18px] gap-y-[20px] flex-wrap mt-[38px] max-md:flex-col max-sm:gap-y-[15px] max-md:mt-[20px]">
              <div className="input_group w-[calc(100%/2-9px)] max-md:w-full ">
                <label
                  className="text-[14px] font-semibold text-[#141414]"
                  htmlFor=""
                >
                  Name of the applicant{" "}
                  <span className="text-[#FF0000]">*</span>
                </label>
                <div className="input_parent mt-[8px]">
                  <input
                    placeholder="Enter name"
                    className="border  w-full h-[46px] border-[#90C9E8] rounded-[8px] p-[16px] bg-white text-[14px] font-normal text-[#6C727F] max-sm:h-[40px]"
                    type="text"
                  />
                </div>
              </div>
              <div className="input_group  w-[calc(100%/2-9px)] max-md:w-full">
                <label
                  className="text-[14px] font-semibold text-[#141414]"
                  htmlFor=""
                >
                  Last name <span className="text-[#FF0000]">*</span>
                </label>
                <div className="input_parent mt-[8px]">
                  <input
                    placeholder="Enter last name"
                    className="border border-[#90C9E8] h-[46px] w-full rounded-[8px] p-[16px] bg-white text-[14px] font-normal text-[#6C727F] max-sm:h-[40px]"
                    type="text"
                  />
                </div>
              </div>
              <div className="input_group  w-[calc(100%/2-9px)] max-md:w-full">
                <label
                  className="text-[14px] font-semibold text-[#141414]"
                  htmlFor=""
                >
                  Father/Spouse name <span className="text-[#FF0000]">*</span>
                </label>
                <div className="input_parent mt-[8px]">
                  <input
                    placeholder="Enter name"
                    className="border border-[#90C9E8] h-[46px] w-full rounded-[8px] p-[16px] bg-white text-[14px] font-normal text-[#6C727F] max-sm:h-[40px]"
                    type="text"
                  />
                </div>
              </div>
              <div className="input_group  w-[calc(100%/2-9px)] max-md:w-full">
                <label
                  className="text-[14px] font-semibold text-[#141414]"
                  htmlFor=""
                >
                  Last name<span className="text-[#FF0000]">*</span>
                </label>
                <div className="input_parent mt-[8px]">
                  <input
                    placeholder="Enter Last name"
                    className="border border-[#90C9E8] h-[46px] w-full rounded-[8px] p-[16px] bg-white text-[14px] font-normal text-[#6C727F] max-sm:h-[40px]"
                    type="text"
                  />
                </div>
              </div>
              <div className="input_group  w-[calc(100%/2-9px)] max-md:w-full">
                <label
                  className="text-[14px] font-semibold text-[#141414]"
                  htmlFor=""
                >
                  City <span className="text-[#FF0000]">*</span>
                </label>
                <div className="radio_wrapper mt-[8px]">
                  <div className="radio_group">
                    <RadioGroup
                      className="flex justify-start items-start gap-[28px]"
                      defaultValue="option-one"
                    >
                      <div className="flex items-center space-x-[12px]">
                        <RadioGroupItem
                          className="border border-[#90C9E8] w-[22px] h-[22px] rounded-full bg-white"
                          value="option-one"
                          id="option-one"
                        />
                        <h6 className="text-[14px] font-bold text-[#141414]">
                          Male
                        </h6>
                      </div>
                      <div className="flex items-center space-x-[12px]">
                        <RadioGroupItem
                          className="border border-[#90C9E8] w-[22px] h-[22px] rounded-full bg-white"
                          value="option-two"
                          id="option-two"
                        />
                        <h6 className="text-[14px] font-bold text-[#141414]">
                          Female
                        </h6>
                      </div>
                    </RadioGroup>
                  </div>
                </div>
              </div>
              <div className="input_group  w-[calc(100%/2-9px)] max-md:w-full">
                <label
                  className="text-[14px] font-semibold text-[#141414]"
                  htmlFor=""
                >
                  Martial status <span className="text-[#FF0000]">*</span>
                </label>
                <div className="radio_wrapper mt-[8px]">
                  <div className="radio_group">
                    <RadioGroup
                      className="flex justify-start items-start gap-[28px]"
                      defaultValue="option-one"
                    >
                      <div className="flex items-center space-x-[12px]">
                        <RadioGroupItem
                          className="border border-[#90C9E8] w-[22px] h-[22px] rounded-full bg-white"
                          value="option-one"
                          id="option-one"
                        />
                        <h6 className="text-[14px] font-bold text-[#141414]">
                          Single
                        </h6>
                      </div>
                      <div className="flex items-center space-x-[12px]">
                        <RadioGroupItem
                          className="border border-[#90C9E8] w-[22px] h-[22px] rounded-full bg-white"
                          value="option-two"
                          id="option-two"
                        />
                        <h6 className="text-[14px] font-bold text-[#141414]">
                          Married
                        </h6>
                      </div>
                    </RadioGroup>
                  </div>
                </div>
              </div>
              <div className="input_group  w-[calc(100%/2-9px)] max-md:w-full">
                <label
                  className="text-[14px] font-semibold text-[#141414]"
                  htmlFor=""
                >
                  Date of birth <span className="text-[#FF0000]">*</span>
                </label>
                <div className="input_parent mt-[8px]">
                  <Select>
                    <SelectTrigger className="border border-[#90C9E8] h-[46px] w-full rounded-[8px] p-[16px] bg-white text-[14px] font-normal text-[#6C727F] max-sm:h-[40px]">
                      <SelectValue placeholder="Select Employment" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Male">Developer</SelectItem>
                      <SelectItem value="Female">Designer</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="input_group  w-[calc(100%/2-9px)] max-md:w-full">
                <label
                  className="text-[14px] font-semibold text-[#141414]"
                  htmlFor=""
                >
                  Nationality <span className="text-[#FF0000]">*</span>
                </label>
                <div className="input_parent mt-[8px]">
                  <Select>
                    <SelectTrigger className="border border-[#90C9E8] h-[46px] w-full rounded-[8px] p-[16px] bg-white text-[14px] font-normal text-[#6C727F] max-sm:h-[40px]">
                      <SelectValue placeholder="Please Select" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Male">Developer</SelectItem>
                      <SelectItem value="Female">Designer</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="input_group  w-full max-md:w-full">
                <label
                  className="text-[14px] font-semibold text-[#141414]"
                  htmlFor=""
                >
                  Status <span className="text-[#FF0000]">*</span>
                </label>
                <div className="radio_wrapper mt-[8px]">
                  <div className="radio_group">
                    <RadioGroup
                      className="flex justify-start items-start gap-[28px] max-md:flex-wrap max-md:gap-y-[10px]"
                      defaultValue="option-one"
                    >
                      <div className="flex items-center space-x-[12px]">
                        <RadioGroupItem
                          className="border border-[#90C9E8] w-[22px] h-[22px] rounded-full bg-white"
                          value="option-one"
                          id="option-one"
                        />
                        <h6 className="text-[14px] font-bold text-[#141414]">
                          Reseident Individual
                        </h6>
                      </div>
                      <div className="flex items-center space-x-[12px]">
                        <RadioGroupItem
                          className="border border-[#90C9E8] w-[22px] h-[22px] rounded-full bg-white"
                          value="option-two"
                          id="option-two"
                        />
                        <h6 className="text-[14px] font-bold text-[#141414]">
                          non resident
                        </h6>
                      </div>
                      <div className="flex items-center space-x-[12px]">
                        <RadioGroupItem
                          className="border border-[#90C9E8] w-[22px] h-[22px] rounded-full bg-white"
                          value="option-three"
                          id="option-three"
                        />
                        <h6 className="text-[14px] font-bold text-[#141414]">
                          Foreign national
                        </h6>
                      </div>
                    </RadioGroup>
                  </div>
                </div>
              </div>

              <div className="input_group  w-[calc(100%/2-9px)] max-md:w-full">
                <label
                  className="text-[14px] font-semibold text-[#141414]"
                  htmlFor=""
                >
                  Permanent Account number (PAN)
                  <span className="text-[#FF0000]">*</span>
                </label>
                <div className="input_parent mt-[8px]">
                  <input
                    placeholder="Enter pan number"
                    className="border border-[#90C9E8] h-[46px] w-full rounded-[8px] p-[16px] bg-white text-[14px] font-normal text-[#6C727F] max-sm:h-[40px]"
                    type="text"
                  />
                </div>
              </div>

              <div className="input_group  w-full max-md:w-full">
                <label
                  className="text-[14px] font-semibold text-[#141414]"
                  htmlFor=""
                >
                  Proof of identity submitted for PAN exempt cases{" "}
                  <span className="text-[#FF0000]">*</span>
                </label>
                <div className="radio_wrapper mt-[8px]">
                  <div className="radio_group">
                    <RadioGroup
                      className="flex justify-start items-start gap-[28px] max-md:flex-wrap max-md:gap-y-[14px]"
                      defaultValue="option-one"
                    >
                      <div className="flex items-center space-x-[12px]">
                        <RadioGroupItem
                          className="border border-[#90C9E8] w-[22px] h-[22px] rounded-full bg-white"
                          value="option-one"
                          id="option-one"
                        />
                        <h6 className="text-[14px] font-bold text-[#141414]">
                          UID
                        </h6>
                      </div>
                      <div className="flex items-center space-x-[12px]">
                        <RadioGroupItem
                          className="border border-[#90C9E8] w-[22px] h-[22px] rounded-full bg-white"
                          value="option-two"
                          id="option-two"
                        />
                        <h6 className="text-[14px] font-bold text-[#141414]">
                          Passport
                        </h6>
                      </div>
                      <div className="flex items-center space-x-[12px]">
                        <RadioGroupItem
                          className="border border-[#90C9E8] w-[22px] h-[22px] rounded-full bg-white"
                          value="option-three"
                          id="option-three"
                        />
                        <h6 className="text-[14px] font-bold text-[#141414]">
                          Voter ID
                        </h6>
                      </div>
                      <div className="flex items-center space-x-[12px]">
                        <RadioGroupItem
                          className="border border-[#90C9E8] w-[22px] h-[22px] bg-white rounded-full"
                          value="option-four"
                          id="option-four"
                        />
                        <h6 className="text-[14px] font-bold text-[#141414]">
                          Driving Licence
                        </h6>
                      </div>
                      <div className="flex items-center space-x-[12px]">
                        <RadioGroupItem
                          className="border border-[#90C9E8] w-[22px] h-[22px] rounded-full bg-white"
                          value="option-five"
                          id="option-five"
                        />
                        <h6 className="text-[14px] font-bold text-[#141414]">
                          Other
                        </h6>
                      </div>
                    </RadioGroup>
                  </div>
                </div>
              </div>
            </div>

            <div className="btn_wrapper max-w-[414px] w-full mt-[36px]">
              <div className="btn_wrapper mt-[20px] flex justify-center">
                <Link className="w-full" href="/cibil/addressDetails">
                  <Button className="px-[51px] w-full" children="Continue" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
