import React from "react";
import Image from "next/image";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import Button from "@/components/ui/Button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import Link from "next/link";

export default function PersonalDetails() {
  return (
    <main>
      <div className="personal_detailwrapper py-[81px] relative">
      <div className="gradient_shade w-full h-[723px] absolute top-0 left-0 bg-[linear-gradient(180.84deg,_#008BD2_-64.11%,_rgba(0,139,210,0.804112)_-45.11%,_rgba(0,139,210,0.480664)_1.8%,_rgba(0,139,210,0.146018)_35.65%,_rgba(0,139,210,0)_80.15%)]"></div>
        <div className="cibil_srk max-w-[465px] w-full absolute right-0 bottom-0">
          <Image
            className="aspect-[465/653] object-contain  w-full h-full object-right"
            src="/cibil_srk.png"
            alt="srk_banner"
            width={465}
            height={653}
          />
        </div>
        <div className="container">
          <div className="personaldetails_box p-[36px] rounded-[20px] bg-[rgba(233,248,255,0.8)] max-w-[918px] w-full border-[2px] border-[#9cd0ed] relative z-[9] max-md:p-[16px]">
            <h3 className="text-[26px] font-semibold text-[#141414]">
              Personal details
            </h3>
            <p className="text-[16px] font-normal text-[#484848] mt-[18px] max-md:mt-[5px]">
              All fields are mandatory unless marked “optional”
            </p>

            <div className="input_wrapper flex justify-start items-start gap-x-[18px] gap-y-[20px] flex-wrap mt-[38px] max-md:flex-col max-sm:gap-y-[15px]">
              <div className="input_group w-[calc(100%/2-9px)] max-md:w-full ">
                <label
                  className="text-[14px] font-semibold text-[#141414]"
                  htmlFor=""
                >
                  Your name <span className="text-[#FF0000]">*</span>
                </label>
                <div className="input_parent mt-[8px]">
                  <input
                    className="border  w-full h-[46px] border-[#90C9E8] rounded-[8px] p-[16px] bg-white text-[14px] font-normal text-[#6C727F] max-sm:h-[40px]"
                    type="text"
                  />
                </div>
              </div>
              <div className="input_group  w-[calc(100%/2-9px)] max-md:w-full">
                <label
                  className="text-[14px] font-semibold text-[#141414]"
                  htmlFor=""
                >
                  Permanent Account number (PAN){" "}
                  <span className="text-[#FF0000]">*</span>
                </label>
                <div className="input_parent mt-[8px]">
                  <input
                    className="border border-[#90C9E8] h-[46px] w-full rounded-[8px] p-[16px] bg-white text-[14px] font-normal text-[#6C727F] max-sm:h-[40px]"
                    type="text"
                  />
                </div>
              </div>
              <div className="input_group  w-[calc(100%/2-9px)] max-md:w-full">
                <label
                  className="text-[14px] font-semibold text-[#141414]"
                  htmlFor=""
                >
                  Date of birth <span className="text-[#FF0000]">*</span>
                </label>
                <div className="input_parent mt-[8px]">
                  <input
                    className="border border-[#90C9E8] h-[46px] w-full rounded-[8px] p-[16px] bg-white text-[14px] font-normal text-[#6C727F] max-sm:h-[40px]"
                    type="text"
                  />
                </div>
              </div>
              <div className="input_group  w-[calc(100%/2-9px)] max-md:w-full">
                <label
                  className="text-[14px] font-semibold text-[#141414]"
                  htmlFor=""
                >
                  Pincode <span className="text-[#FF0000]">*</span>
                </label>
                <div className="input_parent mt-[8px]">
                  <input
                    className="border border-[#90C9E8] h-[46px] w-full rounded-[8px] p-[16px] bg-white text-[14px] font-normal text-[#6C727F] max-sm:h-[40px]"
                    type="text"
                  />
                </div>
              </div>
              <div className="input_group  w-[calc(100%/2-9px)] max-md:w-full">
                <label
                  className="text-[14px] font-semibold text-[#141414]"
                  htmlFor=""
                >
                  City <span className="text-[#FF0000]">*</span>
                </label>
                <div className="input_parent mt-[8px]">
                  <input
                    className="border border-[#90C9E8] h-[46px] w-full rounded-[8px] p-[16px] bg-white text-[14px] font-normal text-[#6C727F] max-sm:h-[40px]"
                    type="text"
                  />
                </div>
              </div>
              <div className="input_group  w-[calc(100%/2-9px)] max-md:w-full">
                <label
                  className="text-[14px] font-semibold text-[#141414]"
                  htmlFor=""
                >
                  Gender <span className="text-[#FF0000]">*</span>
                </label>
                <div className="input_parent mt-[8px]">
                  <Select>
                    <SelectTrigger className="border border-[#90C9E8] h-[46px] w-full rounded-[8px] p-[16px] bg-white text-[14px] font-normal text-[#6C727F] max-sm:h-[40px]">
                      <SelectValue placeholder="Select Gender" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Male">Male</SelectItem>
                      <SelectItem value="Female">Female</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="input_group  w-[calc(100%/2-9px)] max-md:w-full">
                <label
                  className="text-[14px] font-semibold text-[#141414]"
                  htmlFor=""
                >
                  Employment type <span className="text-[#FF0000]">*</span>
                </label>
                <div className="input_parent mt-[8px]">
                  <Select>
                    <SelectTrigger className="border border-[#90C9E8] h-[46px] w-full rounded-[8px] p-[16px] bg-white text-[14px] font-normal text-[#6C727F] max-sm:h-[40px]">
                      <SelectValue placeholder="Select Employment" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Male">Developer</SelectItem>
                      <SelectItem value="Female">Designer</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="input_group  w-[calc(100%/2-9px)] max-md:w-full">
                <label
                  className="text-[14px] font-semibold text-[#141414]"
                  htmlFor=""
                >
                  Net monthly salary <span className="text-[#FF0000]">*</span>
                </label>
                <div className="input_parent mt-[8px]">
                  <input
                    className="border border-[#90C9E8] h-[46px] w-full rounded-[8px] p-[16px] bg-white text-[14px] font-normal text-[#6C727F] max-sm:h-[40px]"
                    type="text"
                  />
                </div>
              </div>
              <div className="input_group  w-[calc(100%/2-9px)] max-md:w-full">
                <label
                  className="text-[14px] font-semibold text-[#141414]"
                  htmlFor=""
                >
                  I am looking for (optional){" "}
                  <span className="text-[#FF0000]">*</span>
                </label>
                <div className="input_parent mt-[8px]">
                  <Select>
                    <SelectTrigger className="border border-[#90C9E8] h-[46px] w-full rounded-[8px] p-[16px] bg-white text-[14px] font-normal text-[#6C727F] max-sm:h-[40px]">
                      <SelectValue placeholder="Select Loan" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1200">1200</SelectItem>
                      <SelectItem value="1500">1500</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            <div className="terms_wrapper flex justify-start items-start gap-[8px] mt-[24px]">
              <div className="check_boxwrapper">
                <Checkbox className="w-[22px] h-[22px] rounded-[4px] border-[#90C9E8]" />
              </div>
              <p className="text-[14px] leading-[18px] text-[#484848] font-normal mt-[2px]">
                By proceeding, you are agree to{" "}
                <span className="text-[#008BD2] font-bold">
                  Terms & Conditions
                </span>{" "}
                of{" "}
                <span className="text-[#008BD2] font-bold">
                  muthoot fincorp Ltd. & CIBIL
                </span>
              </p>
            </div>
            <div className="terms_wrapper flex justify-start items-start gap-[8px] mt-[20px]">
              <div className="check_boxwrapper">
                <Checkbox className="w-[22px] h-[22px] rounded-[4px] border-[#90C9E8]" />
              </div>
              <p className="text-[14px]  text-[#484848] font-normal mt-[2px] leading-[18px]">
                I authorize Muthoot Capital & other Muthoot Pappachan Group
                companies (including its Agents/representatives) to
                call/communicate with me on their product offerings/promotions
                through Telephone/Mobile/SMS/email ID/WhatsApp.
              </p>
            </div>

            <div className="btn_wrapper max-w-[414px] w-full mt-[36px]">
              <Dialog>
                <DialogTrigger className="w-full">
                  <Button className="w-full" children="Continue" />
                </DialogTrigger>
                <DialogContent className="p-[35px] bg-white rounded-[10px] max-w-[835px] w-full">
                  <DialogHeader>
                    <DialogTitle></DialogTitle>
                    <DialogDescription>
                      <h3 className="text-[24px] font-bold text-[#000000] text-center leading-[32px]">
                        Your credit score is great!
                      </h3>
                      <p className="text-[16px] leading-[24px] font-normal text-[#505050] mt-[14px] text-center">
                        Congratulations, your credit score helps you get loan
                        approval with ease. Check your latest score on or after
                        : 08/12/2024
                      </p>

                      <Image
                        className="w-full max-w-[270px] aspect-[270/245] h-auto object-contain mx-auto mt-[26px]"
                        src="/cibil_round.png"
                        alt="cibil graph"
                        width={600}
                        height={245}
                      />

                      <h3 className="text-[20px] font-bold text-[#141414] mt-[12px] text-center">
                        Your eligible for this loan
                      </h3>

                      <Link href="/cibil/personalDetails" className="btn_wrapper mt-[20px] flex justify-center">
                        <Button className="px-[51px]" children="Apply Now" />
                      </Link>
                    </DialogDescription>
                  </DialogHeader>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
