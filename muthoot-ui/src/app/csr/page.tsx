import CommonBanner from "@/components/common/CommonBanner";
import CsrTabs from "@/components/csr/CsrTabs";
import React from "react";
import { generateMetadata as generateMetadataUtil } from "@/lib/metaData";
import { Metadata } from "next";
import { GetCsrPage } from "@/lib/api/cms";
import { GetCsrCategories } from "@/lib/api/general";
import { GetCSRCollections } from "@/lib/api/collections";
import CommonText from "@/components/common/CommonText";
export async function generateMetadata(): Promise<Metadata> {
  const { data: SEOData } = await GetCsrPage();
  return await generateMetadataUtil(
    SEOData?.SEO?.Title,
    SEOData?.SEO?.Description,
    "/csr",
    SEOData?.SEO?.keywords
  );
}

export default async function Csr() {
  const { data } = await GetCsrPage();
  const { data: collections } = await GetCSRCollections();
  const { data: categoriesData } = await GetCsrCategories();

  return (
    <main>
      {data?.Banner && (
        <CommonBanner
          backgroundImage={`${process.env.NEXT_PUBLIC_API_BASE_URL}${
            data?.Banner?.Banner?.url || ""
          }`}
          title={data?.Banner?.Title || ""}
          description={data?.Banner?.Description || ""}
          sideImage={`${process.env.NEXT_PUBLIC_API_BASE_URL}${
            data?.Banner?.SRK_Image?.url || ""
          }`}
          sideImageHeight={1000}
          sideImageWidth={1000}
          sideImageClassName="mt-[.4rem] md:w-[370px] md:h-[437px]"
          backgroundImageHeight={1000}
          backgroundImageWidth={1000}
          breadcrumbs={[{ label: "CSR Listing", href: "/csr" }]}
        />
      )}
      <div className="csr_Listingpage py-[100px] max-md:py-[50px] relative">
        <div className=" absolute top-0 left-0 w-full h-[1400px] bg-[linear-gradient(180deg,_#008BD2_6.98%,_rgba(0,139,210,0.804112)_37.87%,_rgba(0,139,210,0.480664)_53.78%,_rgba(0,139,210,0.146018)_69.69%,_rgba(0,139,210,0)_80.59%)]"></div>
        <div className="container">
          <div
            data-aos="fade-up"
            className="title_wrapper max-w-[828px] w-full mx-auto"
          >
            {data?.Header?.Title && (
              <h2 className="text-[44px] text-center max-w-[628px] w-full mx-auto leading-[58px] text-[#FFFFFF] font-semibold max-md:text-[35px] max-md:leading-[45px] ">
                <CommonText title={data?.Header?.Title} />
              </h2>
            )}
            {data?.Header?.Description && (
              <p className="text-[16px] leading-[24px] text-center font-normal text-[#FFFFFF] mt-[16px] max-md:text-[14px] max-md:leading-[22px]">
                {data?.Header?.Description}
              </p>
            )}
          </div>

          <div className="csr_tabs mt-[46px] relative z-[9]">
            <CsrTabs categoryData={categoriesData} data={collections} />
          </div>
        </div>
      </div>
    </main>
  );
}
