import SecondaryBanner from "@/components/common/SecondaryBanner";
import React from "react";
import Image from "next/image";
import PolicyBox from "@/components/policy/PolicyBox";

import { generateMetadata as generateMetadataUtil } from "@/lib/metaData";
import { GetBlogPage, GetCsrPage } from "@/lib/api/cms";
import { Metadata } from "next";
import CommonBanner from "@/components/common/CommonBanner";
import { GetSingleCSR, GetSingleCSRFile } from "@/lib/api/collections";
import { DownloadPolicy } from "@/components/policy/DownloadPolicy";
import CSRBox from "@/components/policy/CSRBox";
import NewsLetterForm from "@/container/CSR/NewsLetter";

// export async function generateMetadata(): Promise<Metadata> {
//   const { data: SEOData } = await GetBlogPage();
//   return await generateMetadataUtil(
//     SEOData?.SEO?.Title,
//     SEOData?.SEO?.Description,
//     "/csr",
//     SEOData?.SEO?.keywords
//   );
// }

export default async function CsrDetailPage({
  params,
}: {
  params: Promise<{ slug: string }>;
}) {
  const { data } = await GetCsrPage();

  const { slug } = await params;
  const { data: CSRData } = await GetSingleCSR(slug);
  const { data: CSRFileData } = await GetSingleCSRFile(slug);

  const groupedResult = processData(CSRFileData?.Reports);

  return (
    <main>
      <div className="csr_detailbannerwrapper">
        <SecondaryBanner
          backgroundImage={`${process.env.NEXT_PUBLIC_API_BASE_URL}${
            CSRData?.Banner?.url || ""
          }`}
          title={CSRData?.Banner_Title || ""}
          description={CSRData?.Banner_Description || ""}
          breadcrumbs={[
            { label: "CSR Listing", href: "/csr" },
            { label: "Details", href: "#" },
          ]}
        />
      </div>
      <div className="csr_detailpage pt-[100px] max-lg:pt-[50px] relative">
        <div className=" absolute top-0 left-0 w-full h-[1200px] bg-[linear-gradient(180deg,_#008BD2_6.98%,_rgba(0,139,210,0.804112)_37.87%,_rgba(0,139,210,0.480664)_53.78%,_rgba(0,139,210,0.146018)_69.69%,_rgba(0,139,210,0)_80.59%)]"></div>
        <div className="container">
          <div
            data-aos="fade-up"
            className="title_wrapper max-w-[1101px] w-full mx-auto relative z-[9]"
          >
            <h2 className="text-[44px] text-center  w-full mx-auto leading-[58px] text-[white] font-semibold max-md:text-[35px] max-md:leading-[45px] ">
              {CSRData?.Title}
            </h2>
            {/* <p className="text-[16px] leading-[24px] text-center font-normal text-[white] mt-[16px] max-md:text-[14px] max-md:leading-[22px]">
              {CSRData?.Description}
            </p> */}
            {CSRData?.Description && (
              <ul>
                <div
                  className="text-[16px] leading-[24px] text-center font-normal text-[white] mt-[16px] max-md:text-[14px] max-md:leading-[22px]"
                  dangerouslySetInnerHTML={{
                    __html: CSRData?.Description.replace("<ul>", "")
                      .replace("</ul>", "")
                      .replace(
                        /<li>/g,
                        '<li data-aos="fade-up" class="text-[16px] mt-[24px] pl-[23px] relative font-normal leading-[24px] text-[#ffffff] flex"><div class="dot w-[12px] h-[12px] bg-[#034791] rounded-full absolute left-0 top-[6px]"></div><span class="text-white">'
                      )
                      .replace(/<\/li>/g, "</span ></li>"),
                  }}
                />
              </ul>
            )}
          </div>

          <div className="csr_detailed_grid pb-[100px] max-lg:pb-[50px] mt-[42px] flex justify-start items-start gap-[24px] flex-wrap relative z-[9]">
            {CSRData?.Images &&
              CSRData?.Images?.map((media: any, index: number) => (
                <div
                  data-aos="fade-up"
                  className="grid_box w-[calc(100%/2-12px)] max-sm:w-full max-sm:max-w-[420px] max-sm:mx-auto"
                  key={index}
                >
                  <Image
                    className=" w-full h-full object-cover rounded-[12px] aspect-[628/407]"
                    src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${
                      media?.url || ""
                    }`}
                    alt={media?.name}
                    width={628}
                    height={407}
                  />
                </div>
              ))}
          </div>

          {/* report section  */}
          <div className="download_csr pb-[100px] max-lg:pb-[50px]">
            <h2
              data-aos="fade-up"
              className="text-[44px] text-center max-w-[708px]  w-full mx-auto leading-[58px] text-[#0F0F0F] font-semibold max-md:text-[35px] max-md:leading-[45px] "
            >
              Download CSR Report
            </h2>
            <p
              data-aos="fade-up"
              className="text-[16px] leading-[24px] max-w-[708px]  w-full mx-auto text-center font-normal text-[#505050] mt-[16px] max-md:text-[14px] max-md:leading-[22px]"
            >
              {CSRData?.Download_Description}
            </p>

            {/* {CSRFileData && } */}
            {groupedResult && (
              <div className="report_Mainwrapper">
                {Object.entries(groupedResult).map(
                  ([yearSpan, data]: [string, any], index: number) => (
                    <div className="csr_reportwrapper mt-[42px]" key={index}>
                      <div
                        data-aos="fade-up"
                        className="title_date text-[32px] font-semibold text-[#141414] max-lg:text-[24px]"
                      >
                        {yearSpan}
                      </div>
                      <div className="flex items-start justify-start gap-[23px] mt-[42px] flex-wrap">
                        <CSRBox data={data} />
                      </div>
                    </div>
                  )
                )}
              </div>
            )}
          </div>
        </div>

        {/* newsletter section  */}
        <div className="news_letter py-[42px] bg-[#008BD2] rounded-t-[14px]">
          <NewsLetterForm data={data?.Csr_Newsletter} />
        </div>
      </div>
    </main>
  );
}

const processData = (data: any) => {
  const groupedData: any = {};

  data.forEach((item: any) => {
    const itemDate = new Date(item.Date);
    const year = itemDate.getFullYear();
    const nextYear = year + 1;
    const yearSpan = `${year}-${nextYear}`;

    if (!groupedData[yearSpan]) {
      groupedData[yearSpan] = [];
    }

    groupedData[yearSpan].push(item);
  });

  return groupedData;
};
