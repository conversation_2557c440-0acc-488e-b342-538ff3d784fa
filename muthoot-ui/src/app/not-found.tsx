import { NotFoundPage } from "@/lib/api/cms";
import Image from "next/image";

export default async function NotFound() {
  const { data } = await NotFoundPage();
  return (
    <div className="flex flex-col items-center justify-center text-center pb-[125px] pt-[100px]">
      <Image
        src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${data?.Section?.Image?.url}`}
        alt="404"
        width={300}
        height={200}
        className="-mt-8"
      />
      <p className="md:text-[40px] text-[26px] font-bold text-[#0F0F0F]">
        {data?.Section?.Title}
      </p>
      <p className="mt-2 md:max-w-[500px] max-w-[300px] text-center md:text-[16px] text-[12px] font-light text-[#3C3C3C]">
        {data?.Section?.Description}
      </p>
    </div>
  );
}
