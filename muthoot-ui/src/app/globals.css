@import url("https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: "Montserrat", serif !important;
}

.container {
  padding: 0 43px;
  margin: auto;
  max-width: 1366px;
}
.container1 {
  margin: auto;
  max-width: 1450px;
}

::selection {
  background-color: #008bd2;
  color: white;
}

.shadow-top-bottom {
  box-shadow: 0 -4px 10px rgba(0, 0, 0, 0.1), 0 4px 10px rgba(0, 0, 0, 0.1);
}

.custom-scrollbar::-webkit-scrollbar {
  height: 3px;
}

.custom-scrollbar {
  padding-bottom: 10px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background-color: #053c6b;
}

@media (max-width: 1280px) {
  /* xl breakpoint */
  .container {
    padding: 0 30px;
  }

  .about_mainwrapper {
    flex-direction: column;
    align-items: center;
  }

  .about_mainwrapper .content {
    max-width: 80%;
  }

  /* .about_mainwrapper .content img {
    margin: auto;
  }

  .about_mainwrapper .content h3 {
    text-align: center;
  }

  .about_mainwrapper .content p {
    text-align: center;
  }

  .about_mainwrapper .content .btn_wrapper {
    display: flex;
    justify-content: center;
  } */
}

@media (max-width: 992px) {
  /* lg breakpoint */
  .container {
    padding: 0 20px;
  }
}

@media (max-width: 576px) {
  /* sm breakpoint */
  .container {
    padding: 0 16px;
  }
}

.container {
  margin: auto;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.mobile_megamenu li {
  text-align: start;
}

.swiper-pagination-bullet {
  width: 14px !important;
  height: 14px !important;
  background-color: #484848 !important;
  opacity: 1 !important;
}

.swiper-pagination-bullet-active {
  background-color: #008bd2 !important;
  width: 20px !important;
  position: relative;
  height: 20px !important;
}

.swiper-pagination {
  bottom: 0 !important;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 13px !important;
}

@media (max-width: 575.98px) {
  .swiper-pagination-bullet {
    width: 8px !important;
    height: 8px !important;
  }
}

@media (max-width: 575.98px) {
  .swiper-pagination-bullet-active {
    width: 14px !important;
    height: 14px !important;
  }
  .swiper-pagination {
    gap: 2px !important;
  }
}

.quick_links a {
  position: relative;
}

.quick_links a:hover {
  color: rgb(98 202 255);
}

.quick_links a:after {
  position: absolute;
  bottom: -5px;
  left: 0;
  right: 0;
  margin: auto;
  width: 0%;
  content: "";
  color: transparent;
  background: rgb(98 202 255);
  height: 1px;
  -webkit-transition: 0.3s ease;
  transition: 0.3s ease;
}

.quick_links a:hover:after {
  width: 100%;
  transition: 0.3s ease;
}

.terms_links a {
  position: relative;
}

.terms_links a:hover {
  color: rgb(98 202 255);
}

.terms_links a:after {
  position: absolute;
  bottom: -5px;
  left: 0;
  right: 0;
  margin: auto;
  width: 0%;
  content: "";
  color: transparent;
  background: rgb(98 202 255);
  height: 1px;
  -webkit-transition: 0.3s ease;
  transition: 0.3s ease;
}

.terms_links a:hover:after {
  width: 100%;
  transition: 0.3s ease;
}

.menu_wrapper ul li .menu_item {
  position: relative;
}

.menu_wrapper ul li .menu_item:after {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
  width: 0%;
  content: "";
  color: transparent;
  background: #008bd2;
  height: 1px;
  -webkit-transition: 0.3s ease;
  transition: 0.3s ease;
}

.menu_wrapper ul li .menu_item:hover:after {
  width: 100%;
  transition: 0.3s ease;
}

.about_megamenu {
  z-index: 99;
}

.intiative_detailbanner .content {
  max-width: 100%;
  text-align: center;
}

.list_wrapper {
  padding-bottom: 15px;
}

.list_wrapper::-webkit-scrollbar {
  height: 6px;
  cursor: pointer;
}

.list_wrapper::-webkit-scrollbar-thumb {
  background-color: #1687c0;
  height: 6px;
  border-radius: 20px;
  cursor: pointer;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
}

.faq_wrapper svg {
  color: black !important;
}

.faq_wrapper .pb-4.pt-0 {
  padding-bottom: 0 !important;
}

.faq_wrapper .contact_accordion_item:last-of-type {
  border-bottom: none !important;
}

input:focus {
  outline: none;
  transition: 0.3s ease-in-out;
  border: 1px solid #008bd2;
  -webkit-transition: 0.3s ease-in-out;
  -moz-transition: 0.3s ease-in-out;
  -ms-transition: 0.3s ease-in-out;
  -o-transition: 0.3s ease-in-out;
}

textarea:focus {
  outline: none;
  transition: 0.3s ease-in-out;
  border: 1px solid #008bd2;
  -webkit-transition: 0.3s ease-in-out;
  -moz-transition: 0.3s ease-in-out;
  -ms-transition: 0.3s ease-in-out;
  -o-transition: 0.3s ease-in-out;
}

.social_icon:hover svg path {
  fill: #008bd2;
  transition: 0.3s ease-in-out;
}

.social_icon:hover {
  background-color: white;
  transition: 0.3s ease-in-out;
}
*::-webkit-scrollbar {
  width: 6px;
  cursor: pointer;
}

*::-webkit-scrollbar-track {
  background-color: transparent;
}

*::-webkit-scrollbar-thumb {
  background-color: #045093;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
  cursor: pointer;
}

*::-webkit-scrollbar-thumb:hover {
  background-color: #053c6b;
  transition: 0.3s ease-in-out;
  -webkit-transition: 0.3s ease-in-out;
  -moz-transition: 0.3s ease-in-out;
  -ms-transition: 0.3s ease-in-out;
  -o-transition: 0.3s ease-in-out;
}

*::-webkit-scrollbar-thumb:active {
  background-color: #045093;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 1px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background-color: transparent;
  border-radius: 20px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: #e2e2e2;
  border-radius: 8px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: #e2e2e2;
}

.custom-scrollbar::-webkit-scrollbar-thumb:active {
  background-color: #e2e2e2;
}
.branch_box {
  /* transition: .3s ease-in-out; */
}

.branch_box:hover {
  background-color: #abe3ff;
  /* transition: .3s ease-in-out; */
}

.blue_blogwrapper h6 {
  color: white;
}
.blue_blogwrapper h3 {
  color: white;
}
.blue_blogwrapper p {
  color: white;
}

.leaders_groupsection h3 {
  color: white;
}
.leaders_groupsection h6 {
  color: white;
}

.leaders_groupsection .employee-slider {
  margin-top: 66px !important;
}

body {
  overflow-x: hidden;
}

.scrolled_header .about_megamenu {
  top: 80px;
}

.timeline_section
  .data-\[state\=active\]\:text-\[\#008BD2\][data-state="active"]
  span {
  background-color: #008bd2;
}

.check_boxwrapper {
  display: flex;
}

.inear_tab
  .data-\[state\=active\]\:text-\[\#008BD2\][data-state="active"]
  span {
  background-color: #008bd2;
}

.grid_image {
  overflow: hidden;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
}

.grid_image:hover img {
  transform: scale(1.1);
  transition: 0.4s ease-in-out;
  -webkit-transition: 0.4s ease-in-out;
  -moz-transition: 0.4s ease-in-out;
  -ms-transition: 0.4s ease-in-out;
  -o-transition: 0.4s ease-in-out;
  -webkit-transform: scale(1.1);
  -moz-transform: scale(1.1);
  -ms-transform: scale(1.1);
  -o-transform: scale(1.1);
}

.grid_image img {
  transition: 0.4s ease-in-out;
  -webkit-transition: 0.4s ease-in-out;
  -moz-transition: 0.4s ease-in-out;
  -ms-transition: 0.4s ease-in-out;
  -o-transition: 0.4s ease-in-out;
}

.sticky-header {
  position: sticky;
  top: 0;
  z-index: 1000; /* Ensure it stays above other content */
  background-color: white; /* Adjust as needed */
}

.sticky-header {
  z-index: 1000;
  background-color: white; /* Adjust as needed */
}

.eligibility_box .hovered_image {
  max-width: 180px;
  transition: 0.6s ease-in-out;
  -webkit-transition: 0.6s ease-in-out;
  -moz-transition: 0.6s ease-in-out;
  -ms-transition: 0.6s ease-in-out;
  -o-transition: 0.6s ease-in-out;
  opacity: 0;
}

.eligibility_box:hover .hovered_image {
  max-width: 290px;
  transition: 0.6s ease-in-out;
  -webkit-transition: 0.6s ease-in-out;
  -moz-transition: 0.6s ease-in-out;
  -ms-transition: 0.6s ease-in-out;
  -o-transition: 0.6s ease-in-out;
  opacity: 1;
}

.timeline_section .data-\[state\=active\]\:text-\[white\][data-state="active"] {
  background: unset !important;
  color: #008bd2;
}

.timeline_section
  .data-\[state\=active\]\:text-\[white\][data-state="active"]
  span {
  background: #008bd2;
}

.timeline_modal .data-\[state\=active\]\:text-\[white\][data-state="active"] {
  background: unset !important;
  color: #008bd2;
}

.timeline_modal
  .data-\[state\=active\]\:text-\[white\][data-state="active"]
  span {
  background: #008bd2;
}

header.sticky.top-0.z-\[80\].bg-\[rgba\(255\,255\,255\,0\.9\)\].transition-shadow.duration-300.bg-\[white\].shadow-lg.sticky.top-0.z-\[80\].bg-\[rgba\(255\,255\,255\,0\.9\)\].transition-shadow.duration-300.bg-\[white\]
  .about_megamenu {
  top: 75px;
}

.menu_links a:hover {
  color: #008bd2;
}

.about_section {
  overflow-x: hidden;
}

.news_letterwrapper input:focus {
  border: none;
}

.news_letterwrapper input:focus-visible {
  border: none;
  outline: none;
}

.news_letterwrapper input:focus {
  outline: none;
}

.news_letter h2 {
  max-width: 565px;
  width: 100%;
}

.news_letter p {
  max-width: 707px;
  width: 100%;
}

.news_letterwrapper {
  border-radius: 16px;
  -webkit-border-radius: 16px;
  -moz-border-radius: 16px;
  -ms-border-radius: 16px;
  -o-border-radius: 16px;
  padding: 6px;
  padding-left: 16px;
  max-width: 497px;
  width: 100%;
  margin: auto;
  margin-top: 28px;
}

.news_letterwrapper button {
  padding: 13px 33px;
  border-radius: 7px;
  -webkit-border-radius: 7px;
  -moz-border-radius: 7px;
  -ms-border-radius: 7px;
  -o-border-radius: 7px;
}

.csr_detailbannerwrapper {
  position: relative;
}

.csr_detailbannerwrapper:after {
  background: linear-gradient(0deg, #018bd2 15.46%, #0763ac00 51.64%);
  width: 100%;
  height: 100%;
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
}

.about_bannermainwrapper .common_banner {
  position: relative;
}

/* .about_bannermainwrapper .common_banner :after {
  background: linear-gradient(1deg, #0560a9 15.46%, #0763ac00 94.64%);
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
} */

.about_bannermainwrapper .common_banner .background_shade {
  display: none;
}

.about_bannermainwrapper .common_banner .common_gradient {
  background-image: linear-gradient(0.4deg, #0560a9 15.46%, #0763ac00 98.64%);
}

.about_bannermainwrapper .common_banner .ad_banner .side_imagegradient {
  background-image: linear-gradient(0.1deg, #0560a9 1.46%, #0763ac00 32.64%);
}

.about_mainpage .about_shade {
  background-image: linear-gradient(
    180.36deg,
    #0058a3 -6.5%,
    #008bd2cd 48.18%,
    #008bd225 74.51%,
    #008bd200 82.32%
  ) !important;
}

.directors_page .founder_box {
  margin: 0;
}

.ethos_mainbanner .common_banner .common_gradient {
  background: linear-gradient(1deg, #0865ad 1.46%, #0763ac00 98.64%);
}

.ethos_mainbanner .common_banner .ad_banner .side_imagegradient {
  background: linear-gradient(1deg, #0865ad 15.46%, #0763ac00 45.64%);
}

.categorydetail_banner {
  position: relative;
}

.categorydetail_banner:after {
  content: "";
  /* background: linear-gradient(178.4deg, #035396 15.17%, rgba(7, 99, 172, 0) 98.64%); */
  background: linear-gradient(3deg, #035396 -26.83%, #0763ac00 98.64%);
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.fincorp_box .time span {
  /* font-size: 18px; */
  font-weight: 700;
  color: #141414;
}

.web_muthootlocation a {
  font-size: 16px;
  font-weight: 500;
  color: #008bd2;
  text-decoration: underline;
}

.policy_hoveredtabs ul li p {
  display: none;
}

.policy_hoveredtabs ul li.active_tab1 p {
  display: block;
}

.policy_hoveredtabs ul li.active_tab1 {
  position: relative;
}

.policy_hoveredtabs ul li:last-child {
  border-bottom: none;
}

.policy_hoveredtabs ul li.active_tab1::after {
  content: "";
  position: absolute;
  top: 50%;
  right: -49px;
  width: 37px;
  height: 37px;
  background-image: url("/Polygon-arrow.png");
  background-size: contain;
  background-repeat: no-repeat;
}

.megamenu_items ul::-webkit-scrollbar {
  background-color: #2699d3;
  border-radius: 5px;
}

.megamenu_items ul::-webkit-scrollbar-thumb {
  background-color: white;
}

.google-translate-container {
  position: absolute;
  top: -9999px;
  left: -9999px;
  height: 0;
  overflow: hidden;
  visibility: hidden;
}

/* Hide Google Translate banner */
.goog-te-banner-frame {
  display: none !important;
}

.goog-te-combo {
  padding: 5px !important;
  border-radius: 4px !important;
  border: 1px solid #ddd !important;
  outline: none !important;
  background-color: white !important;
}

/* Hide Google Translate attribution but keep dropdown */
.goog-te-gadget-simple {
  border: none !important;
  padding: 5px !important;
}

.goog-te-gadget-simple span {
  border-left: none !important;
}

/* Remove specific skiptranslate class hiding */
/* 
.skiptranslate {
  display: none !important;
}  */
.VIpgJd-ZVi9od-ORHb-OEVmcd {
  display: none !important;
}

/* Container styling */
.google-translate-container {
  position: relative;
  display: inline-block;
}

/* Hide only the Google branding text */
.goog-te-gadget span {
  display: none !important;
}

/* .goog-te-gadget {
  font-size: 0 !important;
}

.goog-te-gadget div {
  display: inline-block !important;
} */
/* globals.css */
.google-translate-container {
  display: none !important;
}

/* Hide Google Translate elements */
.skiptranslate,
.goog-te-banner-frame,
.goog-te-menu-frame {
  display: none !important;
}

/* Ensure our custom selector is visible */
.language-select__control {
  z-index: 10;
}

/* Hide Google Translate elements but keep functionality */
#google_translate_element {
  position: absolute;
  top: -9999px;
  left: -9999px;
}

.goog-te-banner-frame {
  display: none !important;
}

body {
  top: 0 !important;
}

/* Fix for translated content */
.translated-ltr {
  margin-top: 0 !important;
}

.searchdrop {
  margin-bottom: 20px !important;
}

@media (max-width: 800.98px) {
  .loan_wrapper {
    margin-top: -40px !important;
  }
}

@media (max-width: 560.98px) {
  .loan_wrapper {
    margin-top: -85px !important;
  }
}

@media (max-width: 1280.98px) {
  .separator-container:nth-child(4) {
    display: none;
  }
}

.milestone_mapwrapper::after {
  content: "";
  position: absolute;
  top: 190px;
  left: 50%;
  transform: translateX(-50%);
  width: 1100px;
  height: 760px;
  background-image: url("/milestone_Tracknew.svg");
  background-size: contain;
  background-repeat: no-repeat;
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  -o-transform: translateX(-50%);
}

.year_box {
  position: relative;
}

.year_box:after {
  content: "";
  position: absolute;
  top: -24px;
  left: 50%;
  transform: translateX(-50%);
  width: 15px;
  height: 15px;
  background-image: url("/arroe-up.svg");
  background-size: contain;
  background-repeat: no-repeat;
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  -o-transform: translateX(-50%);
}

.second_step_row .reverse_singlemilestone {
  margin-top: 105px;
  gap: 15px;
}

.second_step_row {
  flex-direction: row-reverse;
  justify-content: flex-end;
}

.third_step_row .reverse_singlemilestone {
  gap: 15px;
}

.reverse_singlemilestone .year_box:after {
  top: unset;
  bottom: -27px;
  rotate: 185deg;
  left: 27%;
}

.content_box {
  padding: 20px;
  min-height: 100px;
}

.third_step_row .reverse_singlemilestone {
  margin-top: 0;
  flex-direction: column-reverse;
}

.third_step_row {
  margin-top: 25px;
  justify-content: end;
  margin-right: -10px;
  gap: 50px;
  justify-content: space-between;
}

.fourth_step_row {
  max-width: 920px;
  gap: 110px;
  width: 100%;
  margin: auto;
  margin-top: 8px;
}

.fifth_step_row {
  margin-top: 9px;
  max-width: 950px;
  width: 100%;
  margin-left: auto;
}

@media (max-width: 1100.98px) {
  .third_step_row {
    margin-top: 70px;
    gap: 60px;
    justify-content: space-between;
  }

  .second_step_row .reverse_singlemilestone {
    margin-top: 0;
  }

  .top_spacing {
    margin-top: 0;
  }

  .second_step_row .reverse_singlemilestone .year_box:after {
    top: -30px;
    bottom: unset;
    rotate: unset;
    left: 50%;
  }

  .reverse_singlemilestone {
    flex-direction: column;
  }

  .first_step_row {
    gap: 60px;
    justify-content: space-between;
  }
  .second_step_row {
    margin-top: 70px;
    gap: 60px;
    justify-content: space-between;
  }
  .milestone_mapwrapper::after {
    display: none;
  }
  .milestone_mapwrapper {
    height: auto;
  }

  .third_step_row .reverse_singlemilestone {
    flex-direction: column;
  }

  .third_step_row .reverse_singlemilestone .year_box:after {
    top: -30px;
    bottom: unset;
    rotate: unset;
    left: 50%;
  }

  .milestone {
    padding-top: 30px;
  }

  .fourth_step_row {
    margin-top: 30px;
  }

  .fifth_step_row {
    margin-top: 30px;
  }
}

@media (max-width: 700.98px) {
  .first_step_row {
    gap: 40px;
  }
  .second_step_row {
    gap: 40px;
  }
  .third_step_row {
    gap: 40px;
  }
}

@media (max-width: 600.98px) {
  .first_step_row {
    gap: 20px;
    flex-direction: column;
  }
  .second_step_row {
    gap: 20px;
    flex-direction: column;
  }
  .third_step_row {
    gap: 20px;
    flex-direction: column;
  }

  .single_milestone {
    max-width: 100%;
  }

  .single_milestone .content_box {
    padding: 0 !important;
  }

  .milestone_mapwrapper {
    padding-top: 0;
  }

  .second_step_row {
    margin-top: 20px;
  }

  .milestone {
    padding-bottom: 70px;
  }

  .third_step_row {
    margin-top: 20px;
  }
}

/* Custom styles for react-phone-input-2 */
.react-tel-input .flag-dropdown {
  border-right: none !important;
  background-color: transparent !important;
}

.react-tel-input .selected-flag {
  padding: 0 0 0 12px !important;
  display: flex !important;
  align-items: center !important;
}

.react-tel-input .selected-flag .flag {
  transform: scale(1.2);
}

.react-tel-input .selected-flag .arrow {
  left: 28px !important;
  border-top: 4px solid #6c727f !important;
}

.react-tel-input .country-list {
  margin: 5px 0 !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
}

.react-tel-input .form-control {
  border-radius: 8px !important;
}

.bannervideo_wrapper:after {
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 107px;
  content: "";
  background-image: url(/video_particle.png);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: bottom;
}

.bannervideo_wrapper:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.452);
}

.scrolled_header .search_wrapperbtn {
  display: none;
}
/* 
.mobile_sheet {
  background: linear-gradient(
    0deg,
    rgba(0, 162, 230, 0) -39%,
    rgba(0, 162, 230, 0) 0,
    rgba(0, 162, 230, 0.77) 75.77%,
    #00a2e6
  );
} */

.mobile_sheet {
  background-image: linear-gradient(
    180.36deg,
    #0058a3 -6.5%,
    #008bd2cd 48.18%,
    #008bd225 196.51%,
    #008bd200 114.32%
  ) !important;
}

.mobile_megamenu li {
  text-align: start;
  margin: 0;
  padding: 18px 0;
  border-bottom: 1px solid hsla(0, 0%, 100%, 0.3);
}

.mobile_megamenu ul li:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.mobile_megamenu li svg {
  margin: 0;
  width: 20px;
  height: 20px;
}

.social_icon:hover {
  background-color: #0d86bc !important;
  border-color: #0d86bc;
  transition: all 0.3s ease-in-out;
}

.financial_loanwrapper .financial_box {
  backdrop-filter: blur(20px);
}

.enquiry_wrapper {
  box-shadow: 0px -4px 9.5px 0px #0000001f;
}

.annual_trigger::-webkit-scrollbar {
  height: 6px;
}

.loan_listingwrapper .close_icon {
  position: absolute;
  top: 0;
  right: 0;
  background: #008bd2;
  padding: 4px;
  border-radius: 0;
}

.loan_fixedIcon {
  background: #008bd2;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 6px;
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  -ms-border-radius: 6px;
  -o-border-radius: 6px;
}

.loan_fixedIcon svg {
  width: 24px;
  height: 25px;
}

/* ul li span {
  color: #ffff !important;
} */

.modal_closebtn {
  position: absolute;
  top: -1px;
  right: -1px;
  background: #008bd2;
  padding: 6px;
}

.custom-scrollbar {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
  }

  &::-webkit-scrollbar-thumb {
    background: #0095da;
    border-radius: 10px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #007bb8;
  }

  scrollbar-width: thin;
  scrollbar-color: #0095da #f1f1f1;
}

.about_megamenu .srk_image {
  position: relative;
  overflow: hidden;
}

.about_megamenu {
  height: 100vh;
}

.about_megamenu .srk_image:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: linear-gradient(0.4deg, #0059a3 9.46%, #0763ac00 32.64%);
}

@media (max-width: 630.98px) {
  .contact_quick a:after {
    bottom: -2px;
  }

  .contact_quick li {
    word-break: normal;
  }

  .quick_linkwrapper .quick_links li {
    font-size: 13px;
  }
  .quick_linkwrapper {
    gap: 15px !important;
  }

  .quick_linkwrapper .quick_links {
    width: calc(50% - 8px);
  }

  .mail_area:after {
    bottom: -5px !important;
  }
}

@media (max-width: 340.98px) {
  .contact_quick li {
    word-break: break-all;
  }
}

.employee-slider .swiper-slide {
  height: auto;
}

.employee-slider .swiper-slide .testimonial_box {
  height: 100%;
}

.showcase_imagewrapper button {
  align-self: stretch;
}

.showcase_imagewrapper button .showcase_imagebox {
  height: 100%;
}

.showcase_imagewrapper button .showcase_imagebox .image_wrapper {
  height: 100%;
}

/* @media (min-width: 1279.98px) {
.financial_loanwrapper .financial_box:last-child {
  width: 100%;
}



.financial_loanwrapper .financial_box:last-child .financial_contents {
  width: 38%;
  padding-left: 25px;
}



.financial_loanwrapper .financial_box:last-child .financial_image  {
  max-width: 49%;
  height: 210px;
}
} */

.financial_loanwrapper {
  justify-content: center;
}

.custom-nav-button.banner_prev {
  left: 100px;
  background-image: url(/banner_prev.svg);
  background-size: contain;
  background-repeat: no-repeat;
  width: 60px;
  height: 60px;
}
.custom-nav-button.banner_next {
  right: 100px;
  background-image: url(/banner_next.svg);
  background-size: contain;
  background-repeat: no-repeat;
  width: 60px;
  height: 60px;
}

@media (max-width: 1500.98px) {
  .custom-nav-button.banner_next {
    right: 30px;
  }
  .custom-nav-button.banner_prev {
    left: 30px;
  }
}

@media (min-width: 12024.98px) {
  .custom-nav-button {
    display: none;
  }
}

.custom-nav-button {
  position: absolute;
  top: 50%;
  width: 60px;
  height: 60px;
  z-index: 9;
  transform: translateY(-50%);
  cursor: pointer;
}

@media (max-width: 15000.98px) {
  .loan_fixedIcon {
    top: 65%;
  }
}

.home_bannershade:before {
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 107px;
  content: "";
  background-image: url(/video_particle.png);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: bottom;
}

.title p {
  text-align: justify !important;
}

.description p {
  text-align: justify !important;
}

.title_wrapper p {
  text-align: justify !important;
}

.loan_eligibility_section p {
  text-align: justify !important;
}

.vision_singleitem p {
  text-align: justify !important;
}

.muthoot_pappachansection p {
  text-align: justify !important;
}

.leadership_banner .banner_title {
  margin-top: 200px;
  padding-bottom: 20px;
}
