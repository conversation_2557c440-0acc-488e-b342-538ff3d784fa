import React from "react";
import Image from "next/image";
import { GetHistoryPage, GetHistoryPageSeo } from "@/lib/api/cms";
import { generateMetadata as generateMetadataUtil } from "@/lib/metaData";
import { Metadata } from "next";
import { GetAboutUsPage } from "@/lib/api/general";
import CommonBanner from "@/components/common/CommonBanner";
import CommonText from "@/components/common/CommonText";

export async function generateMetadata(): Promise<Metadata> {
  const { data: SEOData } = await GetHistoryPageSeo();
  return await generateMetadataUtil(
    SEOData?.SEO?.Title,
    SEOData?.SEO?.Description,
    "/history",
    SEOData?.SEO?.keywords,
    {
      openGraph: {
        images: [SEOData?.SEO?.image?.url],
      },
      structured: {
        "@context": "https://schema.org",
        "@type": "AboutPage",
        mainEntity: {
          "@type": "Organization",
          name: "Muthoot Capital Services",
          description: SEOData?.SEO?.Description,
        },
      },
    }
  );
}

export default async function History() {
  const { data } = await GetHistoryPage();
  return (
    <main>
      <div
        data-aos="fade-up"
        className="common_banner w-full min-h-[489px] h-full bg-cover bg-no-repeat relative max-lg:h-full"
      >


        <CommonBanner
          backgroundImage={`${process.env.NEXT_PUBLIC_API_BASE_URL}${
            data?.Banner?.Banner?.url || ""
          }`}
          title={data?.Banner?.Title || ""}
          description={data?.Banner?.Description || ""}
          sideImage={`${process.env.NEXT_PUBLIC_API_BASE_URL}${
            data?.Banner?.SRK_Image?.url || ""
          }`}
          sideImageHeight={2000}
          sideImageWidth={2000}
          sideImageClassName="mt-[.6rem] md:w-[503px] md:h-[463px] object-contain"
          backgroundImageHeight={1000}
          backgroundImageWidth={1000}
          underOverlay={false}
          overlayColor={"#00486ccc"}
          gradient={false}
          breadcrumbs={[
            { label: "About Us", href: "/about" },
            { label: "Our History", href: "/history" },
          ]}
        />
      </div>

      <div className="history_mainpage my-[100px] max-md:my-[60px]">
        <div className="container">
          <div
            data-aos="fade-up"
            className="history_wrapper p-[50px] relative rounded-[21px] max-md:p-[25px]"
            style={{
              backgroundImage: "url('/history_mainsheet.png')",
              backgroundSize: "cover",
              backgroundPosition: "center",
              backgroundRepeat: "no-repeat",
            }}
          >
            <div className="overlay bg-[rgba(0,140,210,0.7)] w-full h-full absolute top-0 left-0 mix-blend-multiply rounded-[21px]"></div>

            <div className="our_journey relative z-[9]">
              <div data-aos="fade-up" className="title_wrapper ">
                <h2 className="text-[44px] font-semibold text-white text-center max-md:text-[38px] max-sm:text-[34px]">
                  <CommonText title={data?.Header?.Title || ""} />
                </h2>
                <p className="text-[16px] font-[400] text-white mt-[18px] text-center max-md:text-[14px]">
                  {data?.Header?.Description}
                </p>
              </div>

            
              <div className="journey_grids flex justify-center items-start flex-wrap gap-[16px] mt-[42px] max-md:flex-wrap max-md:justify-center">
                {data?.LandScapeImages &&
                  data?.LandScapeImages.map((item: any, index: any) => (
                    <div
                      key={index}
                      data-aos="fade-up"
                      className="single_grid w-[calc(100%/2-8px)]  max-sm:max-w-[438px] max-sm:w-full"
                    >
                      <Image
                        className="w-full h-full object-contain object-top rounded-[11px] aspect-[438/370]"
                        src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${item?.Image?.url}`}
                        alt=""
                        width={1000}
                        height={1000}
                      />
                      <h6 className="text-[16px] font-semibold text-white mt-[22px] text-center max-md:text-[14px]">
                        {item?.Title || ""}
                      </h6>
                    </div>
                  ))}
              </div>

              <div className="journey_grids flex justify-center items-start flex-wrap gap-[16px] mt-[42px] max-md:flex-wrap max-md:justify-center">
                {data?.Images &&
                  data?.Images.map((item: any, index: any) => (
                    <div
                      key={index}
                      data-aos="fade-up"
                      className="single_grid w-[calc(100%/3-11px)]  max-sm:max-w-[438px] max-sm:w-full"
                    >
                      <Image
                        className="w-full h-full object-contain object-top rounded-[11px] aspect-[306/435]"
                        src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${item?.Image?.url}`}
                        alt=""
                        width={1000}
                        height={1000}
                      />
                      <h6 className="text-[16px] font-semibold text-white mt-[22px] text-center max-md:text-[14px]">
                        {item?.Title || ""}
                      </h6>
                    </div>
                  ))}
              </div>
            </div>

            <div className="our_pillers relative z-[9] mt-[100px] max-md:mt-[60px]">
              <div
                data-aos="fade-up"
                className="title_wrapper max-w-[582px] w-full mx-auto "
              >
                <h2 className="text-[44px] font-semibold text-white text-center max-md:text-[38px] max-sm:text-[34px]">
                  <CommonText title={data?.Pillars_Section?.Title || ""} />
                </h2>
                <p className="text-[16px] font-[400] text-white mt-[18px] text-center max-md:text-[14px]">
                  {data?.Pillars_Section?.Description || ""}
                </p>
              </div>

              <div className="pillers_grid flex justify-start items-start gap-[20px] flex-wrap mt-[45px]">
                {data?.Pillars_Section?.Folks &&
                  data?.Pillars_Section?.Folks.map((folk: any, index: any) => (
                    <div
                      key={index}
                      data-aos="fade-up"
                      className="single_pillerbox w-[calc(100%/2-10px)] self-stretch p-[50px] max-lg:w-full max-md:p-[25px] max-sm:py-[40px]"
                      style={{
                        backgroundImage: "url('/piller_backgrnd.png')",
                        backgroundSize: "cover",
                        backgroundPosition: "center",
                        backgroundRepeat: "no-repeat",
                      }}
                    >
                      <div className="image_wrapper max-w-[245px] w-full mx-auto max-sm:max-w-[150px]">
                        <Image
                          className="w-full rounded-full h-full aspect-square"
                          src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${folk?.Image?.url}`}
                          alt={folk?.Name || "pillar image"}
                          width={folk?.Image?.width || 245}
                          height={folk?.Image?.height || 245}
                        />
                      </div>
                      <h5 className="text-[22px] font-[600] text-[#141414] text-center mt-[36px] max-sm:text-[20px] max-sm:mt-[20px]">
                        {folk?.Name || ""}
                      </h5>
                      <h6 className="text-[22px] font-medium text-[#141414] mt-[14px] text-center max-sm:text-[20px]">
                        {folk?.Era || ""}
                      </h6>
                      <p className="text-[16px] font-[400] text-[#484848] mt-[15px] text-center max-sm:text-[14px]">
                        {folk?.About || ""}
                      </p>
                    </div>
                  ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
