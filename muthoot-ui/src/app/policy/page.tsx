import CommonBanner from "@/components/common/CommonBanner";
import PolicyBox from "@/components/policy/PolicyBox";
import React from "react";
import { Metadata } from "next";
import { generateMetadata as generateMetadataUtil } from "@/lib/metaData";
import { GetPoliciesPage, GetPoliciesSeo } from "@/lib/api/cms";
import { GetPolicies } from "@/lib/api/general";
export async function generateMetadata(): Promise<Metadata> {
  const { data: SEOData } = await GetPoliciesSeo();

  return await generateMetadataUtil(
    SEOData?.SEO?.Title,
    SEOData?.SEO?.Description,
    "/corparote",
    SEOData?.SEO?.keywords
  );
}

export default async function Policy() {
  const { data } = await GetPoliciesPage();
  const { data: policies } = await GetPolicies();

  return (
    <main>
      <CommonBanner
        backgroundImage={`${process.env.NEXT_PUBLIC_API_BASE_URL}${
          data?.Banner?.Banner?.url || ""
        }`}
        title={data?.Banner?.Title || ""}
        description={data?.Banner?.Description || ""}
        sideImage={`${process.env.NEXT_PUBLIC_API_BASE_URL}${
          data?.Banner?.SRK_Image?.url || ""
        }`}
        sideImageHeight={1000}
        sideImageWidth={1000}
        sideImageClassName="mt-[.7rem] md:w-[336px]"
        backgroundImageHeight={1000}
        backgroundImageWidth={1000}
        breadcrumbs={[{ label: "Policies", href: "/policy" }]}
      />
      <div className="policy_listingpage pt-[50px] overflow-hidden pb-[100px] max-md:pb-[50px] relative">
        <div className=" absolute top-0 left-0 w-full h-full bg-[linear-gradient(#008bd2_6.98%,_#008bd2cd_37.87%,_#008bd27b_53.78%,_#008bd225_200.69%,_#008bd200_121.59%)]"></div>
        <div className="container relative z-[9]">
          <div
            data-aos="fade-up"
            className="title_wrapper max-w-[900px] w-full mx-auto"
          >
            {data?.Header?.Title && (
              <h2 className="text-[44px] text-center leading-[58px] text-[#FFFFFF] font-semibold max-md:text-[35px] max-md:leading-[45px] ">
                <span className="text-[#FECB05]">
                  Policies at Muthoot Capital
                </span>
                : Our Commitment to Financial Excellence
              </h2>
            )}
            {data?.Header?.Description && (
              <p className="text-[16px] leading-[24px] text-center font-normal text-[#FFFFFF] mt-[16px] max-md:text-[14px] max-md:leading-[22px]">
                {data?.Header?.Description}
              </p>
            )}
          </div>
          <div className="flex items-start justify-start gap-[23px] mt-[42px] flex-wrap">
            <PolicyBox policies={policies} />
          </div>
        </div>
      </div>
    </main>
  );
}
