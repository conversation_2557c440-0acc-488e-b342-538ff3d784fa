import BreadCrumb from "@/components/common/BreadCrumb";
import CommonText from "@/components/common/CommonText";
import LoanList from "@/components/common/LoanList";
import { DownloadPolicy } from "@/components/policy/DownloadPolicy";
import PolicyAccordion from "@/components/policy/PolicyAccordion";
import { GetSinglePolicy } from "@/lib/api/general";
import React from "react";

export default async function PolicyDetailPage({
  params,
}: {
  params: Promise<{ slug: string }>;
}) {
  const { slug } = await params;
  const { data } = await GetSinglePolicy(slug);
  return (
    <main>
      <div className="policy_detailspage pt-[60px] pb-[100px] max-md:py-[50px] relative">
        <div className="loan_iconcomponentwrapper">{/* <LoanList /> */}</div>
        <div className="shade_wrapper bg-[linear-gradient(180deg,_#0271B7_6.98%,_#0271B7_22.82%,_rgba(0,139,210,0.480664)_36.82%,_rgba(0,139,210,0.146018)_55.73%,_rgba(0,139,210,0)_80.59%)] absolute top-0 left-0 w-full h-full"></div>
        <div className="container relative z-[9]">
          <div
            data-aos="fade-up"
            className="breadcrumbs_wrapper pt-[36p] pb-[42px]"
          >
            <BreadCrumb
              breadcrumbs={[
                { label: "Policies", href: "/policy" },
                { label: "Details", href: "#" },
              ]}
            />
          </div>
          <div
            data-aos="fade-up"
            className="title_wrapper max-w-[900px] w-full mx-auto"
          >
            <h2 className="text-[44px] text-center leading-[58px] text-[white] font-semibold max-md:text-[35px] max-md:leading-[45px] ">
              <CommonText title={data?.Detail_Title} />
            </h2>
            <p className="text-[16px] leading-[24px] text-center font-normal text-[white] mt-[16px] max-md:text-[14px] max-md:leading-[22px]">
              {data?.Description}
            </p>
          </div>
          <div className="accordion_wrapper mt-[42px]">
            <PolicyAccordion data={data?.Tabs} />
          </div>
          <DownloadPolicy downloads={data?.Downloads} />
        </div>
      </div>
    </main>
  );
}
