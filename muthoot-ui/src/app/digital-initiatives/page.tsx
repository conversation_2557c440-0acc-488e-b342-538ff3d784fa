import CommonBanner from "@/components/common/CommonBanner";
import DigitalTabs from "@/components/digital-initiatives/DigitalTabs";
import { GetDigitalInitiativesPage } from "@/lib/api/cms";
import React from "react";
import { generateMetadata as generateMetadataUtil } from "@/lib/metaData";
import { Metadata } from "next";
export async function generateMetadata(): Promise<Metadata> {
  const { data: SEOData } = await GetDigitalInitiativesPage();

  return await generateMetadataUtil(
    SEOData?.SEO?.Title,
    SEOData?.SEO?.Description,
    "/digital-intiatives",
    SEOData?.SEO?.keywords
  );
}

export default async function DigitalInitiatives() {
  const { data } = await GetDigitalInitiativesPage();
  // const { data: blogs } = await GetBlogs();
  return (
    <main>
      <CommonBanner
        backgroundImage={`${process.env.NEXT_PUBLIC_API_BASE_URL}${data?.Banner?.Banner?.url}`}
        title={data?.Banner?.Title}
        description={data?.Banner?.Description}
        sideImage={`${process.env.NEXT_PUBLIC_API_BASE_URL}${data?.Banner?.SRK_Image?.url}`}
        sideImageHeight={1000}
        sideImageWidth={1000}
        sideImageClassName="mt-[.5rem] md:w-[324px] md:h-[452px]"
        backgroundImageHeight={1000}
        backgroundImageWidth={1000}
        breadcrumbs={[
          { label: "Digital Initiatives", href: "/digital-initiatives" },
        ]}
      />

      <div className="csr_Listingpage py-[100px] max-md:py-[50px] relative">
        <div className=" absolute top-0 left-0 w-full h-full bg-[linear-gradient(180deg,_#008BD2_6.98%,_rgba(0,139,210,0.804112)_37.87%,_rgba(0,139,210,0.480664)_53.78%,_rgba(0,139,210,0.146018)_69.69%,_rgba(0,139,210,0)_80.59%)]"></div>
        <div className="container">
          <div className="title_wrapper max-w-[828px] w-full mx-auto">
            <h2
              data-aos="fade-up"
              className="text-[44px] text-center max-w-[628px] w-full mx-auto leading-[58px] text-[#FECB05] font-semibold max-md:text-[35px] max-md:leading-[45px] "
            >
              {data?.Header?.Title}
            </h2>
            <p
              data-aos="fade-up"
              className="text-[16px] leading-[24px] text-center font-normal text-[white] mt-[16px] max-md:text-[14px] max-md:leading-[22px]"
            >
              {data?.Header?.Description}
            </p>
          </div>

          <div className="csr_tabs mt-[46px] relative z-[9]">
            <DigitalTabs />
          </div>
        </div>
      </div>
    </main>
  );
}
