import SecondaryBanner from "@/components/common/SecondaryBanner";
import Image from "next/image";
import {
  GetDigitalInitiatives,
  GetSingleDigitalInitiative,
} from "@/lib/api/general";

import { generateMetadata as generateMetadataUtil } from "@/lib/metaData";
import { GetDigitalInitiativesPage } from "@/lib/api/cms";
import { Metadata } from "next";
import RecommendedDigital from "@/components/digital-initiatives/RecommendedDigital";
import DigitalInitiativeNextPrev from "@/components/digital-initiatives/Digital-InitiativesNextPrev";
export async function generateMetadata(): Promise<Metadata> {
  const { data: SEOData } = await GetDigitalInitiativesPage();
  return await generateMetadataUtil(
    SEOData?.SEO?.Title,
    SEOData?.SEO?.Description,
    "/digital-initiatives",
    SEOData?.SEO?.keywords
  );
}

const RenderDescriptionContent = ({ item }: any) => {
  switch (item.type) {
    case "paragraph":
      return (
        <p className="text-[16px] font-normal text-[#141414] leading-[22px] mt-[14px] max-md:text-[14px]">
          {item.children.map((child: any, index: number) =>
            child.bold ? (
              <span
                key={index}
                className="text-[22px] font-[600] text-[#141414]"
              >
                {child.text}
              </span>
            ) : (
              <span key={index}>{child.text}</span>
            )
          )}
        </p>
      );
    case "quote":
      return (
        <h4
          data-aos="fade-up"
          className="text-[28px] font-semibold text-[#008BD2] mt-[34px] max-sm:text-[28px]"
        >
          {item.children[0].text}
        </h4>
      );
    case "image":
      return (
        <div
          data-aos="fade-up"
          className="img_wrapper grid grid-cols-2 gap-[21px] mt-[34px] max-sm:grid-cols-1"
        >
          <Image
            className="h-full w-full object-cover rounded-[10px] aspect-[373/267]"
            src={`${item?.image?.url}`}
            alt={item?.image?.alternativeText || "digital image"}
            width={1000}
            height={1000}
          />
        </div>
      );
    default:
      return null;
  }
};

export default async function DigitalInitiativeDetailPage({
  params,
}: {
  params: Promise<{ slug: string }>;
}) {
  const { slug } = await params;
  const { data } = await GetSingleDigitalInitiative(slug);
  const { data: allDigital } = await GetDigitalInitiatives();

  const currentIndex = allDigital.findIndex(
    (digital: any) => digital?.documentId === slug
  );

  const prevDigital =
    currentIndex > 0
      ? allDigital[currentIndex - 1]
      : allDigital[allDigital.length - 1];

  const nextDigital =
    currentIndex < allDigital.length - 1
      ? allDigital[currentIndex + 1]
      : allDigital[0];

  const formatDate = (dateString: any) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  return (
    <main>
      <SecondaryBanner
        backgroundImage={`${process.env.NEXT_PUBLIC_API_BASE_URL}${data?.Banner?.url}`}
        imageHeight={1000}
        imageWidth={1000}
        description=""
        bannerClass="common_banner w-full py-[230px] bg-cover bg-no-repeat relative max-lg:h-full max-md:py-[100px] intiative_detailbanner"
        title={data?.Title}
        breadcrumbs={[
          { label: "Digital Initiatives", href: "/digital-initiatives" },
          { label: "Details", href: "#" },
        ]}
      />
      <div className="digital_intiative_detailpage pt-[32px] pb-[100px] max-md:pb-[50px] relative">
        <div className="shade_wrapper bg-[linear-gradient(180.84deg,_#008BD2_-64.11%,_rgba(0,139,210,0.804112)_-45.11%,_rgba(0,139,210,0.480664)_1.8%,_rgba(0,139,210,0.146018)_35.65%,_rgba(0,139,210,0)_80.15%)] w-full h-[750px] absolute top-0 left-0"></div>
        <div className="container">
          <div className="detailed_wrapper flex justify-between items-start gap-[60px] max-lg:flex-col max-sm:gap-[40px]">
            <div className="main_contents max-w-[767px] w-full max-lg:max-w-full">
              <div
                data-aos="fade-up"
                className="date text-[14px] font-normal text-white py-[7px] px-[15px] bg-[#008BD2] rounded-[4px] w-fit"
              >
                {formatDate(data?.Date)}
              </div>
              <h3
                data-aos="fade-up"
                className="text-[32px] font-semibold text-[#141414] mt-[8px]"
              >
                {data?.DescriptionTitle}
              </h3>
              {data?.Description?.map((item: any, index: any) => (
                <RenderDescriptionContent key={index} item={item} />
              ))}
              <DigitalInitiativeNextPrev
                prevDigital={prevDigital}
                nextDigital={nextDigital}
              />
            </div>
            <RecommendedDigital currentDigital={data} allDigital={allDigital} />
          </div>
        </div>
      </div>
    </main>
  );
}
