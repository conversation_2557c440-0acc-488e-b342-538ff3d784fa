import { GetCareersPage, GetCsrHome } from "@/lib/api/cms";
import { GetProduct } from "@/lib/api/general";
import BannerSlider from "@/components/home/<USER>";
import { GetHome } from "@/lib/api/home";
import HomeForm from "@/components/home/<USER>";
import ScrollToSection from "@/components/products/ScrollToSection";
import LoanRedirector from "@/components/home/<USER>";
import ProductSection from "@/components/home/<USER>";
import AboutUsandCounterSection from "@/components/home/<USER>";
import CalculatorSection from "@/components/home/<USER>";
import BranchSectionShowcase from "@/components/home/<USER>";
import TestimonialSection from "@/components/home/<USER>";
import CsrSection from "@/components/home/<USER>";
import BlogSection from "@/components/home/<USER>";
import DigitalSection from "@/components/home/<USER>";
import BranchHomeSection from "@/components/home/<USER>";

export default async function Home() {
  const { data: productData } = await GetProduct();
  const { data: csrData } = await GetCsrHome();
  const { data: careersData } = await GetCareersPage();
  const { data } = await GetHome();

  return (
    <main>
      <ScrollToSection />
      <section className="banner_section relative">
        <div className="loan_iconcomponentwrapper">{/* <LoanList /> */}</div>
        <div className="wrapper ">
          <BannerSlider data={data?.Home_Banner} careersData={careersData} />
        </div>
        <LoanRedirector data={data?.Loan_Redirector} />
      </section>

      <ProductSection data={data?.Product_Section} productData={productData} />

      <AboutUsandCounterSection
        data={data?.AboutUs_Section}
        counterData={data?.Counter_Section}
      />

      <BranchSectionShowcase data={data?.Showcase_Brand_Section} />

      <CalculatorSection data={data?.Calculators_Section} />

      <TestimonialSection data={data?.Clients_Testimonials_section} />

      <CsrSection data={data?.CSR_Section} csrData={csrData} />

      <BlogSection data={data?.Blog_Section} />

      <DigitalSection data={data?.Digital_Initiative_Section} />

      <BranchHomeSection data={data?.Branches_Section} />
      <HomeForm data={data?.Contact_Section} />
    </main>
  );
}
