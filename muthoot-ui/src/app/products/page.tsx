import CommonBanner from "@/components/common/CommonBanner";
import Link from "next/link";
import React from "react";
import Image from "next/image";
import {
  GetProduct,
  GetProductPage,
  GetProductPageSEO,
} from "@/lib/api/general";

import { Metadata } from "next";
import { generateMetadata as generateMetadataUtil } from "@/lib/metaData";
import CommonText from "@/components/common/CommonText";
import { ProductBoxSkeleton } from "@/components/skeleton/ProductBoxSkeleton";
import CommonBannerText from "@/components/common/CommonBannerText";
import ProductBox from "@/components/products/ProductBox";

export async function generateMetadata(): Promise<Metadata> {
  const { data: SEOData } = await GetProductPageSEO();

  return await generateMetadataUtil(
    SEOData?.SEO?.Title,
    SEOData?.SEO?.Description,
    "/products",
    SEOData?.SEO?.keywords
  );
}

export default async function Products() {
  const { data } = await GetProductPage();
  const { data: productData } = await GetProduct();

  return (
    <main>
      <CommonBanner
        backgroundImage={`${process.env.NEXT_PUBLIC_API_BASE_URL}${
          data?.Banner?.Banner?.url || ""
        }`}
        title={data?.Banner?.Title || ""}
        description={data?.Banner?.Description || ""}
        sideImage={`${process.env.NEXT_PUBLIC_API_BASE_URL}${
          data?.Banner?.SRK_Image?.url || ""
        }`}
        sideImageHeight={1000}
        sideImageWidth={1000}
        sideImageClassName="mt-[.9rem] md:w-[427px]"
        backgroundImageHeight={1000}
        backgroundImageWidth={1000}
        breadcrumbs={[{ label: "Products", href: "/products" }]}
      />

      <div className="categorylisting_page pt-[84px] pb-[100px] max-md:pt-[50px] max-md:pb-[50px] relative">
        <div className=" absolute top-0 left-0 w-full max-h-[1800px] h-full bg-[linear-gradient(180deg,_#008BD2_6.98%,_rgba(0,139,210,0.804112)_37.87%,_rgba(0,139,210,0.480664)_53.78%,_rgba(0,139,210,0.146018)_69.69%,_rgba(0,139,210,0)_80.59%)]"></div>
        <div className="container">
          <section className="financial_needs_section   relative ">
            <div className="wrapper">
              <h2
                data-aos="fade-up"
                className="text-[44px] max-w-[577px] w-full mx-auto text-center font-semibold text-[#141414] max-md:text-[30px] max-md:leading-[40px]"
              >
                <CommonText title={data?.Title || ""} />
              </h2>

              <ProductBox data={productData} />
            </div>
          </section>
        </div>
      </div>
    </main>
  );
}
