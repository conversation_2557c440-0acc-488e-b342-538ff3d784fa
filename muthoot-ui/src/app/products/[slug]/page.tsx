import EnquiryBox from "@/components/category/EnquiryBox";
import Button from "@/components/ui/Button";
import React from "react";
import Image from "next/image";
import BenefitCard from "@/components/category/BenefitCard";
import ContactAccordion from "@/components/contact/ContactAccordion";
import AwardsSlider from "@/components/media/AwardsSlider";
import Link from "next/link";
import Timeline from "@/components/home/<USER>";
import FincorpBox from "@/components/category/FincorpBox";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import OtpField from "@/components/common/OtpField";
import Calculator from "@/components/products/Calculator";
import { GetClientsTestimonial } from "@/lib/api/cms";
import CommonText from "@/components/common/CommonText";
import CommonTextBlue from "@/components/common/CommonTextBlue";
import EligibilityCard from "@/components/products/EligibilityCard";
import DocumentsCard from "@/components/products/DocumentsCard";
import { GetSingleProduct } from "@/lib/api/general";
import BreadCrumb from "@/components/common/BreadCrumb";
import FdCalculator from "@/components/products/FdCalculator";
import DepositTable from "@/components/category/DepositTable";
import ImportantDocument from "@/components/category/ImportantDocument";
import ScrollToSection from "@/components/products/ScrollToSection";
import TestimonialHome from "@/components/home/<USER>";

export default async function ProductDetailPage({
  params,
}: {
  params: Promise<{ slug: string }>;
}) {
  const { slug } = await params;
  const { data } = await GetSingleProduct(slug);
  const { data: clientsTestimonial } = await GetClientsTestimonial();

  const slugToCategoryMap: { [key: string]: string } = {
    "two-wheeler-loan": "two_wheeler_loan",
    "car-loan": "car_loan",
    "loyalty-loan": "loyalty_loan",
    "commercial-vehicle-loan": "commercial_vehicle",
    "fixed-deposit": "fixed_deposit",
  };

  const currentCategory = slugToCategoryMap[slug];
  const filteredTestimonials =
    clientsTestimonial?.filter((testimonial: any) => {
      return testimonial?.Categories === currentCategory;
    }) || [];

  const isFixedDeposit = slug === "fixed-deposit";
  const imageConfig = isFixedDeposit
    ? {
        maxWidth: "max-w-[571px]",
        aspectRatio: "aspect-[571/476]",
        alt: "family image",
      }
    : {
        maxWidth: "max-w-[328px]",
        aspectRatio: "aspect-[328/511]",
        alt: "contact banner",
      };

  const layoutConfig = {
    container: {
      alignItems: isFixedDeposit ? "items-end" : "items-start",
    },
    banner: {
      className: isFixedDeposit
        ? "about_banner relative max-w-[717px] w-full max-sm:pl-0 max-sm:pr-0"
        : "about_banner pl-[120px] relative max-w-[640px] w-full pr-[41px] max-sm:pl-0 max-sm:pr-0",
      image: {
        className: isFixedDeposit
          ? "w-full max-w-[717px] aspect-[717/439] h-full object-contain mx-auto max-sm:max-w-[500px] max-sm:w-[100%]"
          : "w-full max-w-[451px] aspect-[451/624] h-full object-contain mx-auto max-sm:max-w-[351px] max-sm:w-[100%]",
      },
    },
    content: {
      description: {
        className: isFixedDeposit
          ? "text-[16px] leading-[24px] text-justify font-normal text-[#484848] max-md:text-[14px] max-md:leading-[22px]"
          : "text-[16px] leading-[24px] text-justify font-normal text-[#484848] text-justify max-md:text-[14px] max-md:leading-[22px]",
      },
    },
  };

  return (
    <main>
      <ScrollToSection />
      <div
        className="categorydetail_banner  w-full h-auto relative"
        style={{
          backgroundImage: `url(${process.env.NEXT_PUBLIC_API_BASE_URL}${data?.Product_Details?.Banner_Detail?.Banner?.url})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
        }}
      >
        <div className="background_shade absolute top-0 left-0 bg-[rgba(0,61,122,0.85)] w-full h-full "></div>
        <div className="content_wrapper py-[60px] pt-[36px] relative z-[9]">
          <div className="container flex justify-between items-start gap-[50px] max-lg:flex-col max-lg:gap-[60px]">
            <div className="content max-w-[600px] w-full max-lg:max-w-full">
              <BreadCrumb
                breadcrumbs={[
                  { label: "Products", href: "/products" },
                  { label: data?.Breadcrumb_Name, href: "#" },
                ]}
              />
              <h2
                data-aos="fade-up"
                className="text-[44px] mt-[48px] text-white font-semibold max-sm:text-[30px] max-sm:leading-[40px]"
              >
                <CommonText
                  title={data?.Product_Details?.Banner_Detail?.Title}
                />{" "}
              </h2>
              <ul className="mt-[25px]">
                {data?.Product_Details?.Banner_Detail?.Description ? (
                  <div
                    className="text-white "
                    dangerouslySetInnerHTML={{
                      __html:
                        data?.Product_Details?.Banner_Detail.Description.replace(
                          "<ul>",
                          ""
                        )
                          .replace("</ul>", "")
                          .replace(
                            /<li>/g,
                            '<li data-aos="fade-up" class="text-white font-normal text-[16px] mb-[10px] relative pl-[21px] max-sm:text-[14px]"><div class="dot absolute w-[11px] h-[11px] rounded-[50%] bg-[#2193D1] left-0 top-[50%] -translate-y-[50%]"></div>'
                          ),
                    }}
                  />
                ) : (
                  <>
                    <li
                      data-aos="fade-up"
                      className="text-white font-normal text-[16px] mb-[10px] relative pl-[21px] max-sm:text-[14px]"
                    >
                      <div className="dot absolute w-[11px] h-[11px] rounded-[50%] bg-[#2193D1] left-0 top-[50%] -translate-y-[50%]"></div>
                      Interest Rates Starting @ 0.99 %* P.A.{" "}
                    </li>
                  </>
                )}
              </ul>
              <div data-aos="fade-up" className="btn_wrapper mt-[36px]">
                <Dialog>
                  <DialogTrigger>
                    {/* <Button className="bg-white !text-[#008BD2] hover:!text-[white] hover:!bg-[#008bd2]" /> */}
                  </DialogTrigger>
                  <DialogContent className="p-[30px] bg-white rounded-[10px] max-w-[400px] w-full">
                    <DialogHeader>
                      <DialogTitle></DialogTitle>
                      <DialogDescription>
                        <h3 className="text-[24px] font-bold text-[#000000] text-center">
                          Sign in
                        </h3>
                        <p className="text-[16px] leading-[24px] font-normal text-[#505050] mt-[12px] text-center">
                          Please enter your 10 - digit mobile number to get
                          started
                        </p>
                        <div className="input_parent mt-[27px]">
                          <input
                            className="rounded-[8px] w-full text-[#6C727F] text-[14px] font-normal border py-[14px] px-[16px] border-[#90C9E8]"
                            type="text"
                          />
                        </div>
                        <div className="btn_wrapper mt-[32px]">
                          <Dialog>
                            <DialogTrigger className="w-full">
                              {" "}
                              <Button
                                className="w-full max-w-full"
                                children="Get OTP"
                              />
                            </DialogTrigger>
                            <DialogContent className="p-[36px] bg-white rounded-[10px] max-w-[505px] w-full">
                              <DialogHeader>
                                <DialogTitle></DialogTitle>
                                <DialogDescription>
                                  <h3 className="text-[24px] font-bold text-[#000000] text-center">
                                    Enter OTP
                                  </h3>
                                  <p className="text-[16px] leading-[24px] font-normal text-[#505050] mt-[12px] text-center">
                                    Sed risus magna porta elit metus nullam
                                    cursus mattis lectus. Viverra mauris sed
                                    viverra.
                                  </p>

                                  <div className="otp_field mt-[24px] max-w-[298px] w-full mx-auto">
                                    {/* <OtpField /> */}
                                  </div>

                                  <div className="btn_wrapper max-w-full w-full mt-[32px]">
                                    <Link href="/cibil">
                                      <Button
                                        children="Submit"
                                        className="w-full"
                                      />
                                    </Link>
                                  </div>
                                </DialogDescription>
                              </DialogHeader>
                            </DialogContent>
                          </Dialog>
                        </div>
                      </DialogDescription>
                    </DialogHeader>
                  </DialogContent>
                </Dialog>
              </div>
            </div>
            <div className="form_wrapper  max-w-[566px] w-full max-lg:max-w-[100%] max-lg:mr-auto">
              <EnquiryBox />
            </div>
          </div>
        </div>
      </div>

      <div className="categoryDetails_page pt-[100px] max-md:pt-[50px] relative">
        <div className="shade bg-[linear-gradient(180deg,_#024C8F_0%,_rgba(0,139,210,0.95)_27.1%,_rgba(0,139,210,0.1)_36.29%,_rgba(0,139,210,0)_80.59%)] absolute top-0 left-0 w-full h-[1150px]"></div>
        {data?.Product_Details?.Header_Sec ? (
          <div className="container">
            <div
              className={`about_mainwrapper pb-[100px] flex justify-between gap-[100px] max-md:gap-[50px] max-md:pb-[50px] ${layoutConfig.container.alignItems}`}
            >
              <div className="content max-w-[520px] w-full max-sm:!max-w-full">
                {data?.Product_Details?.Header_Sec?.Logo?.url && (
                  <Image
                    data-aos="fade-up"
                    className="w-full max-w-[160px] aspect-[160/117] h-full object-contain max-md:max-w-[100px]"
                    src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${data.Product_Details.Header_Sec.Logo.url}`}
                    alt="logo"
                    width={160}
                    height={117}
                  />
                )}
                <h3
                  data-aos="fade-up"
                  className="text-[54px] font-semibold text-[white] mt-[30px] mb-[18px] leading-[60px] max-md:text-[35px] max-md:leading-[44px]"
                >
                  <CommonText
                    title={data?.Product_Details?.Header_Sec?.Title || ""}
                  />
                </h3>
                <div
                  data-aos="fade-up"
                  className={layoutConfig.content.description.className}
                  dangerouslySetInnerHTML={{
                    __html:
                      data?.Product_Details?.Header_Sec?.Description || "",
                  }}
                />
              </div>

              <div className={layoutConfig.banner.className}>
                <div data-aos="fade-down" className="main_image">
                  {(isFixedDeposit ||
                    data?.Product_Details?.Header_Sec?.SRK_Image?.url) && (
                    <Image
                      className={layoutConfig.banner.image.className}
                      src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${data.Product_Details.Header_Sec.SRK_Image.url}`}
                      alt="banner"
                      width={1000}
                      height={1000}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        ) : null}
        {data?.Product_Details?.Deposit_Table ? (
          <div
            id="interest-rates-table"
            className="fixed_deposite_tablewrapper pb-[100px] max-md:pb-[60px] relative z-[9]"
          >
            <div className="container" data-aos="fade-up">
              <DepositTable data={data?.Product_Details?.Deposit_Table} />
            </div>
          </div>
        ) : null}
        {data?.Product_Details?.Important_Documents_Section ? (
          <div
            className="benefits py-[100px] mt-[50px] mb-[100px] max-lg:py-[50px] w-full h-full relative overflow-hidden"
            style={{
              backgroundImage: "url('/keybenefit_bg.png')",
              backgroundSize: "cover",
              backgroundRepeat: "no-repeat",
              backgroundPosition: "bottom",
            }}
          >
            <div className="shade bg-[rgba(0,0,0,0.2)] w-full h-full absolute top-0 left-0"></div>

            <div className="container relative z-[9]">
              <h3
                data-aos="fade-up"
                className="text-[44px] font-semibold text-white text-center max-md:text-[35px] max-md:leading-[46px]"
              >
                <CommonText
                  title={
                    data?.Product_Details?.Important_Documents_Section?.Title ||
                    ""
                  }
                />
              </h3>

              <div className="grid_wrapper flex justify-start items-start gap-[27px] flex-wrap mt-[36px]">
                <ImportantDocument
                  data={
                    data?.Product_Details?.Important_Documents_Section
                      ?.Important_Document_Box
                  }
                />
              </div>
            </div>
          </div>
        ) : null}
        {data?.Product_Details?.Proccesing_Charges_Sec2 ? (
          <div className="download_appwrapper pt-[100px] -mt-[100px] max-md:pt-[50px]">
            <div className="container">
              <div className="intrest_details max-w-[950px] w-full">
                {data?.Product_Details?.Proccesing_Charges_Sec2?.Title && (
                  <h3
                    data-aos="fade-up"
                    className="text-[32px] font-semibold text-[#141414] "
                  >
                    <CommonTextBlue
                      title={
                        data?.Product_Details?.Proccesing_Charges_Sec2?.Title
                      }
                    />
                  </h3>
                )}

                {data?.Product_Details?.Proccesing_Charges_Sec2?.Points && (
                  <ul>
                    <div
                      dangerouslySetInnerHTML={{
                        __html:
                          data?.Product_Details?.Proccesing_Charges_Sec2?.Points.replace(
                            "<ul>",
                            ""
                          )
                            .replace("</ul>", "")
                            .replace(
                              /<li>/g,
                              '<li data-aos="fade-up" class="text-[16px] mt-[24px] pl-[23px] relative font-normal leading-[24px] text-[#141414] flex"><div class="dot w-[12px] h-[12px] bg-[#2193D1] rounded-full absolute left-0 top-[6px]"></div><span class="text-black">'
                            )
                            .replace(/<\/li>/g, "</span ></li>"),
                      }}
                    />
                  </ul>
                )}

                {data?.Product_Details?.Proccesing_Charges_Sec2?.Card_Points &&
                  data?.Product_Details?.Proccesing_Charges_Sec2?.Card_Points
                    .length > 0 && (
                    <div className="intrest_wrapper flex justify-start items-start gap-[24px] flex-wrap mt-[21px]">
                      <div className="w-full flex justify-start items-start gap-[24px] flex-wrap">
                        {data?.Product_Details?.Proccesing_Charges_Sec2?.Card_Points?.map(
                          (card: any, index: any) => (
                            <div
                              key={index}
                              data-aos="fade-up"
                              className="intrest_box rounded-[20px] bg-[#e8f8ff] p-[30px] max-sm:max-w-full max-sm:w-full self-stretch"
                            >
                              <h3 className="text-[18px] font-medium text-[#141414] max-w-[306px] w-full max-md:text-[18px] ">
                                {card.Title}
                              </h3>
                              <h6 className="text-[16px] font-bold text-[#008BD2] mt-[10px] max-w-[306px] w-full max-md:text-[18px] max-md:mt-[12px]">
                                {card.Value}
                              </h6>
                            </div>
                          )
                        )}
                      </div>
                    </div>
                  )}

                {data?.Product_Details?.Proccesing_Charges_Sec2
                  ?.Instruction && (
                  <div
                    data-aos="fade-up"
                    className="disclaimer text-[16px] font-normal text-[#484848] mt-[12px]"
                  >
                    <span className="text-[#FF0000]">*</span>{" "}
                    {
                      data?.Product_Details?.Proccesing_Charges_Sec2
                        ?.Instruction
                    }
                  </div>
                )}
              </div>

              <div className="contact_faq faq_wrapper mt-[10px]">
                <ContactAccordion />
              </div>
            </div>
          </div>
        ) : null}
        {data?.Product_Details?.DigitalPlatform ? (
          <div className="mb-[100px] max:md-mb-[60px] ">
            <div className="documents_listing py-[100px] mt-[100px] max-md:py-[60px] bg-[#008BD2] relative overflow-hidden">
              <div className="container">
                <div className="title max-w-[624px] w-full mx-auto">
                  <h2
                    data-aos="fade-up"
                    className="text-[44px] font-semibold text-[white] leading-[53px] text-center max-md:text-[35px] max-md:leading-[43px] "
                  >
                    <div className="">
                      <CommonText
                        title={
                          data?.Product_Details?.DigitalPlatform?.Title || ""
                        }
                      />
                    </div>
                  </h2>
                  <p
                    data-aos="fade-up"
                    className="text-[16px] leading-[24px] text-[white] font-normal mt-[22px] text-center max-md:text-[14px] max-md:leading-[22px] "
                  >
                    {data?.Product_Details?.DigitalPlatform?.Description || ""}
                  </p>
                </div>
                <div className="digital_boxwrapper flex justify-start items-start gap-y-[30px] gap-x-[20px] flex-wrap mt-[30px] max-w-[900px] w-full mx-auto">
                  {data?.Product_Details?.DigitalPlatform?.DigitalPlatformCard?.map(
                    (item: any, index: any) => (
                      <a
                        key={index}
                        href={item?.RedirectionLink || "#"}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="single_box w-[calc(100%/2-10px)] bg-[#c1ecff] p-[25px] rounded-[10px] self-stretch max-sm:w-full cursor-pointer hover:bg-[#a8e2ff] transition-colors duration-300 block no-underline"
                      >
                        <div className="logo_Image max-w-[132px] w-full mx-auto">
                          <Image
                            className="w-full h-full object-cover aspect-square rounded-full"
                            src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${item.Icon.url}`}
                            alt={item?.Text || "Platform logo"}
                            width={132}
                            height={132}
                          />
                        </div>
                        <h6 className="text-black text-[20px] font-[600] text-center mt-[15px] max-md:text-[16px]">
                          {item?.Text || ""}{" "}
                        </h6>
                      </a>
                    )
                  )}
                </div>
              </div>
            </div>
          </div>
        ) : null}
        {data?.Product_Details?.Products_Offered_Section ? (
          <div
            className="benefits py-[100px] mb-[100px] max-lg:py-[50px] w-full h-full relative overflow-hidden"
            style={{
              backgroundImage: "url('/keybenefit_bg.png')",
              backgroundSize: "cover",
              backgroundRepeat: "no-repeat",
              backgroundPosition: "bottom",
            }}
          >
            <div className="shade bg-[rgba(0,0,0,0.2)] w-full h-full absolute top-0 left-0"></div>

            <div className="container relative z-[9]">
              <h3
                data-aos="fade-up"
                className="text-[44px] font-semibold text-white text-center max-md:text-[35px] max-md:leading-[46px]"
              >
                <CommonText
                  title={
                    data?.Product_Details?.Products_Offered_Section?.Title || ""
                  }
                />
              </h3>

              <div className="grid_wrapper flex justify-start items-start gap-[27px] flex-wrap mt-[36px]">
                <BenefitCard
                  data={
                    data?.Product_Details?.Products_Offered_Section
                      ?.Products_Offered_Box
                  }
                />
              </div>
            </div>
          </div>
        ) : null}
        {data?.Product_Details?.Benefits_Sec ? (
          <div
            className="tenture_section bg-no-repeat bg-cover bg-center w-full h-full"
            style={{
              backgroundImage: "url('/tenture_backgrnd.png')",
            }}
          >
            <div className="container flex justify-between items-center gap-[30px] max-lg:flex-col max-lg:gap-[60px]">
              <div className="content py-[66px] max-w-[431px] w-full max-lg:pb-0">
                <h6
                  data-aos="fade-up"
                  className="text-[16px] font-normal text-[#FFFFFF] max-sm:text-[14px]"
                >
                  {data.Product_Details.Benefits_Sec.Hashtag_Text || ""}
                </h6>
                <h4
                  data-aos="fade-up"
                  className="text-[26px] font-normal text-white mt-[11px] max-sm:text-[22px]"
                >
                  {data.Product_Details.Benefits_Sec.Text || ""}{" "}
                </h4>
                <h4
                  data-aos="fade-up"
                  className="text-[55px] leading-[65px] font-bold text-[white] mt-[1px] max-sm:text-[50px]  max-sm:leading-[60px]"
                >
                  {data.Product_Details.Benefits_Sec.Percentage_Text || ""}{" "}
                </h4>
                <p
                  data-aos="fade-up"
                  className="text-[16px] leading-[24px] text-white font-normal mt-[15px] mb-[18px] max-sm:text-[14px]"
                >
                  {data.Product_Details.Benefits_Sec.Description || ""}{" "}
                </p>
                <div
                  data-aos="fade-up"
                  className="employee_label rounded-[23px] text-[16px] font-semibold text-white py-[13px] px-[24px] bg-gradient-to-r from-[#004C99] to-transparent to-80% w-fit max-sm:text-[14px]"
                >
                  {data.Product_Details.Benefits_Sec.Highlited_Text1 || ""}{" "}
                </div>

                {/* <div data-aos="fade-up" className="btn_wrapper mt-[33px]">
                  <Button
                    children="Apply Now"
                    className="bg-white !text-[#2193D1] hover:!text-white hover:bg-[#008bd2] "
                  />
                </div> */}
              </div>

              <div className="tenture_imagewrapper max-w-[800px] w-full relative self-stretch flex items-end max-lg:mx-auto">
                <div
                  data-aos="fade-down"
                  className="funding_label absolute top-[140px] z-[9] left-[-5px] text-[25px] rounded-[33px] font-semibold  text-white py-[14px] pl-[24px] pr-[39px] w-fit bg-gradient-to-r from-[#0080E7] to-transparent to-[82%] max-md:text-[25px]  max-md:top-[20px] max-sm:text-[16px] max-sm:top-[-20px]"
                >
                  {data.Product_Details.Benefits_Sec.Highlited_Text2 || ""}{" "}
                </div>

                <div
                  data-aos="fade-up"
                  className={`tenture_srk ${imageConfig.maxWidth} w-full mx-auto ml-[130px] max-sm:ml-[30px]`}
                >
                  <Image
                    className={`w-full h-full object-contain ${imageConfig.aspectRatio} object-bottom`}
                    src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${data.Product_Details.Benefits_Sec.SRK_Image.url}`}
                    alt={imageConfig.alt}
                    width={1000}
                    height={1000}
                  />
                </div>

                <div
                  data-aos="fade-down"
                  className="flexible_wrapperbox absolute bottom-[74px] right-0 pl-[150px] pr-[75px] w-fit rounded-[60px] py-[22px] bg-gradient-to-l from-[#084D97] from-[17%] to-transparent to-[99%] flex justify-center items-center gap-[16px] max-md:pl-[30px] max-md:pr-[30px] max-md:bottom-[30px] max-sm:py-[12px]"
                >
                  <h6 className="text-[20px] font-normal text-white max-w-[77px] w-full max-md:text-[18px]">
                    {data.Product_Details.Benefits_Sec.Flexible_Tenure_Texts
                      ?.Text1 || ""}{" "}
                  </h6>
                  <div className="tenture_months">
                    <h5 className="text-[20px] font-semibold text-white max-md:text-[18px]">
                      {data.Product_Details.Benefits_Sec.Flexible_Tenure_Texts
                        ?.Text2 || ""}{" "}
                    </h5>
                    <h2 className="text-[40px] font-semibold text-white max-md:text-[28px]">
                      {data.Product_Details.Benefits_Sec.Flexible_Tenure_Texts
                        ?.Value || ""}{" "}
                      <span className="text-[30px] font-semibold max-md:text-[22px]">
                        {data.Product_Details.Benefits_Sec.Flexible_Tenure_Texts
                          ?.Text || ""}{" "}
                      </span>
                    </h2>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : null}{" "}
        <section
          id="calculator"
          className={`emi_calculator ${
            slug !== ""
              ? "pb-[100px] max-md:pb-[60px] mt-[100px] max:md-mt-[60px]"
              : ""
          } relative z-[9]`}
        >
          <div className="container">
            {slug === "fixed-deposit" ? (
              <div className="title_wrapper">
                <h2
                  data-aos="fade-up"
                  className="text-[44px] max-w-[705px] w-full mx-auto font-semibold text-[#141414] text-center max-md:text-[35px] max-md:leading-[43px]"
                >
                  <CommonTextBlue
                    title={data?.Product_Details?.Calculator_Sec?.Header?.Title}
                  />{" "}
                </h2>
                <p
                  data-aos="fade-up"
                  className="text-16px] leading-[24px] text-[#484848] max-w-[948px] w-full mx-auto text-center mt-[22px] max-md:text-[14px] max-md:leading-[22px] max-md:mt-[16px]"
                >
                  {data?.Product_Details?.Calculator_Sec?.Header?.Description ||
                    ""}{" "}
                </p>
              </div>
            ) : (
              <div className="title_wrapper">
                <h2
                  data-aos="fade-up"
                  className="text-[44px] max-w-[705px] w-full mx-auto font-semibold text-[#141414] text-center max-md:text-[35px] max-md:leading-[43px]"
                >
                  <CommonTextBlue
                    title={data?.Product_Details?.Calculator_Sec?.Header?.Title}
                  />
                </h2>
                <p
                  data-aos="fade-up"
                  className="text-16px] leading-[24px] text-[#484848] max-w-[948px] w-full mx-auto text-center mt-[22px] max-md:text-[14px] max-md:leading-[22px] max-md:mt-[16px]"
                >
                  {data?.Product_Details?.Calculator_Sec?.Header?.Description ||
                    ""}{" "}
                </p>
              </div>
            )}

            <div
              className={`emi_calculatorbox ${
                slug === "" ? "mt-[0px]" : "mt-[36px]"
              }`}
            >
              {slug === "fixed-deposit" ? (
                <FdCalculator />
              ) : slug === "two-wheeler-loan" ||
                slug === "car-loan" ||
                slug === "loyalty-loan" ||
                slug === "commercial-vehicle-loan" ? (
                <Calculator />
              ) : null}
            </div>
          </div>
        </section>
        {data?.Product_Details?.Key_Benefits_Sec ? (
          <div
            className="benefits py-[100px] max-lg:py-[50px] w-full h-full relative overflow-hidden"
            style={{
              backgroundImage: "url('/keybenefit_bg.png')",
              backgroundSize: "cover",
              backgroundRepeat: "no-repeat",
              backgroundPosition: "bottom",
            }}
          >
            <div className="srk_mask max-w-[720px] w-full absolute bottom-0 right-0">
              <Image
                className="w-full h-full object-contain"
                src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${data?.Product_Details?.Key_Benefits_Sec?.SRK_Image?.url}`}
                alt="side image"
                width={1000}
                height={1000}
              />
            </div>

            <div className="shade bg-[rgba(0,0,0,0.2)] w-full h-full absolute top-0 left-0"></div>

            <div className="container relative z-[9]">
              <h3
                data-aos="fade-up"
                className="text-[44px] font-semibold text-white text-center max-md:text-[35px] max-md:leading-[46px]"
              >
                <CommonText
                  title={data?.Product_Details?.Key_Benefits_Sec?.Title || ""}
                />
              </h3>

              <div className="grid_wrapper flex justify-center items-start gap-[27px] flex-wrap mt-[36px]">
                <BenefitCard
                  data={
                    data?.Product_Details?.Key_Benefits_Sec?.Key_Benefits_Card
                  }
                />
              </div>
            </div>
          </div>
        ) : null}
        {data?.Product_Details?.Eligibility_Sec ? (
          <div className="loan_eligibility_section py-[100px] max-md:py-[50px]">
            <div className="container">
              <h2
                data-aos="fade-up"
                className="text-[44px] font-semibold text-[#141414] text-center max-md:text-[35px] max-md:leading-[43px]"
              >
                <CommonTextBlue
                  title={data?.Product_Details?.Eligibility_Sec?.Title}
                />
              </h2>
              <p
                data-aos="fade-up"
                className="text-16px] leading-[24px] text-[#484848] max-w-[655px] w-full mx-auto text-center mt-[22px] max-md:text-[14px] max-md:leading-[22px] max-md:mt-[16px]"
              >
                {data?.Product_Details?.Eligibility_Sec?.Description || ""}{" "}
              </p>

              <EligibilityCard
                data={data?.Product_Details?.Eligibility_Sec?.Criterias}
              />

              {/* <div
              data-aos="fade-up"
              className="btn_wrapper flex justify-center mt-[17px] max-lg:mt-[30px]"
            >
              <Button children="Apply Now" />
            </div> */}
            </div>
          </div>
        ) : null}
        {data?.Product_Details?.Documents_Sec ? (
          <div className="documents_listing py-[100px] max-md:py-[60px] bg-[#008BD2] relative overflow-hidden">
            {data?.Product_Details?.Documents_Sec?.SRK_Image?.url && (
              <div className="srk_shade absolute bottom-[-200px] right-0 max-w-[500px] w-full">
                <Image
                  className="w-full h-full object-contain"
                  src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${data?.Product_Details?.Documents_Sec?.SRK_Image?.url}`}
                  alt="avatar"
                  width={1000}
                  height={1000}
                />
              </div>
            )}
            <div className="container">
              <div className="title max-w-[624px] w-full mx-auto">
                <h2
                  data-aos="fade-up"
                  className="text-[44px] font-semibold text-[white] leading-[53px] text-center max-md:text-[35px] max-md:leading-[43px]"
                >
                  <CommonText
                    title={data?.Product_Details?.Documents_Sec?.Title}
                  />
                </h2>
                <p
                  data-aos="fade-up"
                  className="text-[16px] leading-[24px] text-[white] font-normal mt-[22px] text-center  max-md:text-[14px] max-md:leading-[22px]"
                >
                  {data?.Product_Details?.Documents_Sec?.Description || ""}{" "}
                </p>
              </div>

              <DocumentsCard
                data={data?.Product_Details?.Documents_Sec?.Documents_Box}
              />
            </div>
          </div>
        ) : null}
        {data?.Product_Details?.Proccesing_Charges_Sec ? (
          <div className="download_appwrapper pt-[100px] mb-[100px] md:mb-[50px]">
            <div className="container">
              <div className="intrest_details max-w-[950px] w-full">
                {data?.Product_Details?.Proccesing_Charges_Sec?.Title && (
                  <h3
                    data-aos="fade-up"
                    className="text-[32px] font-semibold text-[#141414] "
                  >
                    <CommonTextBlue
                      title={
                        data?.Product_Details?.Proccesing_Charges_Sec?.Title
                      }
                    />
                  </h3>
                )}

                {data?.Product_Details?.Proccesing_Charges_Sec?.Points && (
                  <ul>
                    <div
                      dangerouslySetInnerHTML={{
                        __html:
                          data?.Product_Details?.Proccesing_Charges_Sec?.Points.replace(
                            "<ul>",
                            ""
                          )
                            .replace("</ul>", "")
                            .replace(
                              /<li>/g,
                              '<li data-aos="fade-up" class="text-[16px] mt-[24px] pl-[23px] relative font-normal leading-[24px] text-[#141414] flex"><div class="dot w-[12px] h-[12px] bg-[#2193D1] rounded-full absolute left-0 top-[6px]"></div><span class="text-black">'
                            )
                            .replace(/<\/li>/g, "</span ></li>"),
                      }}
                    />
                  </ul>
                )}

                {data?.Product_Details?.Proccesing_Charges_Sec?.Card_Points &&
                  data?.Product_Details?.Proccesing_Charges_Sec?.Card_Points
                    .length > 0 && (
                    <div className="intrest_wrapper flex justify-start items-start gap-[24px] flex-wrap mt-[21px]">
                      <div className="w-full flex justify-start items-start gap-[24px] flex-wrap">
                        {data?.Product_Details?.Proccesing_Charges_Sec?.Card_Points?.map(
                          (card: any, index: any) => (
                            <div
                              key={index}
                              data-aos="fade-up"
                              className="intrest_box rounded-[20px] bg-[#e8f8ff] p-[30px] max-sm:max-w-full max-sm:w-full self-stretch"
                            >
                              <h3 className="text-[24px] font-medium text-[#141414] max-w-[306px] w-full max-md:text-[18px] ">
                                {card.Title}
                              </h3>
                              <h6 className="text-[24px] font-bold text-[#008BD2] mt-[20px] max-w-[306px] w-full max-md:text-[18px] max-md:mt-[12px]">
                                {card.Value}
                              </h6>
                            </div>
                          )
                        )}
                      </div>
                    </div>
                  )}

                {data?.Product_Details?.Proccesing_Charges_Sec?.Instruction && (
                  <div
                    data-aos="fade-up"
                    className="disclaimer text-[16px] font-normal text-[#484848] mt-[12px]"
                  >
                    <span className="text-[#FF0000]">*</span>{" "}
                    {data?.Product_Details?.Proccesing_Charges_Sec?.Instruction}
                  </div>
                )}
              </div>

              <div className="contact_faq faq_wrapper mt-[10px]">
                <ContactAccordion />
              </div>
            </div>
          </div>
        ) : null}
        {data?.Product_Details?.Branches_Sec ? (
          <div
            className="fincorp_section relative mt-[100px] max-md:mt-[50px]"
            style={{
              backgroundImage: `url(${process.env.NEXT_PUBLIC_API_BASE_URL}${data?.Product_Details?.Branches_Sec?.Bg_Image?.url})`,
              backgroundSize: "cover",
              backgroundRepeat: "no-repeat",
              backgroundPosition: "center",
            }}
          >
            <div className="shade bg-[rgba(0,0,0,0.7)] absolute top-0 left-0 w-full h-full"></div>

            <div className="container relative z-[9] !py-[100px] max-md:!py-[50px]">
              <div className="title_wrapper max-w-[670px] w-full mx-auto">
                <h2
                  data-aos="fade-up"
                  className="text-[44px] text-center leading-[58px] text-[white] font-semibold max-md:text-[35px] max-md:leading-[45px] "
                >
                  <CommonText
                    title={data?.Product_Details?.Branches_Sec?.Title}
                  />
                </h2>
                <p
                  data-aos="fade-up"
                  className="text-[16px] leading-[24px] text-center font-normal text-[white] mt-[16px] max-md:text-[14px] max-md:leading-[22px]"
                >
                  {data?.Product_Details?.Branches_Sec?.Description}
                </p>
              </div>

              <div className="fincorpbox_wrapper mt-[28px] flex-wrap flex justify-start items-start gap-[30px] max-xl:justify-center">
                <FincorpBox
                  data={data?.Product_Details?.Branches_Sec?.Branch_Box}
                />
              </div>
              <div
                data-aos="fade-up"
                className="locate text-[16px] leading-[24px] text-center font-normal text-[white] mt-[53px] max-md:text-[14px] max-md:leading-[22px] max-sm:mt-[30px]"
              >
                {data?.Product_Details?.Branches_Sec?.Instruction}
                <Link
                  className="font-bold underline text-[18px]"
                  href={data?.Product_Details?.Branches_Sec?.Button?.Link}
                >
                  {data?.Product_Details?.Branches_Sec?.Button?.Label}
                </Link>
              </div>
            </div>
          </div>
        ) : null}
        {data?.Product_Details?.Faq_Sec ? (
          <section className="faq_wrapper pb-[70px] pt-[100px] max-md:pb-[40px] max-md:pt-[50px] ">
            <div className="container">
              <div className="title_wrapper max-w-[650px] w-full mx-auto">
                <h2
                  data-aos="fade-up"
                  className="text-[44px] text-center leading-[58px] text-[#0F0F0F] font-semibold max-md:text-[35px] max-md:leading-[45px] "
                >
                  {data?.Product_Details?.Faq_Sec?.Title}
                </h2>
                <p
                  data-aos="fade-up"
                  className="text-[16px] leading-[24px] text-center font-normal text-[#505050] mt-[16px] max-md:text-[14px] max-md:leading-[22px]"
                >
                  {data?.Product_Details?.Faq_Sec?.Description}
                </p>
              </div>
              <div className="contact_faq mt-[8px]">
                <ContactAccordion data={data?.Product_Details?.Faq_Sec?.FAQ} />
              </div>
            </div>
          </section>
        ) : null}
        {filteredTestimonials && filteredTestimonials.length > 0 ? (
          <section className="timeline_section    bg-[#E7F7FF] py-[100px] max-md:py-[50px] ">
            <div className="container">
              <div className="media_head flex justify-between items-end mb-[48px] gap-[30px] max-lg:flex-col max-lg:items-start max-lg:gap-[30px]">
                <div className="title max-w-[860px] w-full">
                  <h3
                    data-aos="fade-up"
                    className="text-[44px] text-[#141414] font-semibold max-md:text-[35px] max-md:leading-[45px]"
                  >
                    {/* {data?.Product_Details?.Client_Testimonial_sec?.Title} */}
                    <CommonTextBlue
                      title={
                        data?.Product_Details?.Client_Testimonial_sec?.Title
                      }
                    />{" "}
                    <span className="text-[#008BD2]"></span>
                  </h3>
                  <p
                    data-aos="fade-up"
                    className="text-[16px] font-normal text-[#484848] leading-[24px] mt-[16px] text-justify max-sm:text-[14px] max-sm:leading-[22px]"
                  >
                    {data?.Product_Details?.Client_Testimonial_sec?.Description}
                  </p>
                </div>
                <div
                  data-aos="fade-up"
                  className="view_wrapper max-w-[120px] w-full max-lg:max-w-full  max-lg:w-full max-lg:flex max-lg:justify-end"
                >
                  <Link
                    className="text-[16px] font-bold text-[#008BD2] flex justify-end items-center gap-[13px]"
                    href="/testimonials"
                  >
                    View All{""}
                    <svg
                      width="8"
                      height="14"
                      viewBox="0 0 8 14"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M1 13L7 7L1 1"
                        stroke="#008BD2"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>{" "}
                  </Link>
                </div>
              </div>

              <div className="timeline_wrapper">
                <TestimonialHome data={filteredTestimonials} />
              </div>
            </div>
          </section>
        ) : null}
      </div>
    </main>
  );
}
