import CommonBanner from "@/components/common/CommonBanner";
import React from "react";
import {
  GetClientsTestimonial,
  GetTestimonilasPage,
  GetTestimonilasSeo,
} from "@/lib/api/cms";
import { Metadata } from "next";
import { generateMetadata as generateMetadataUtil } from "@/lib/metaData";
import CommonText from "@/components/common/CommonText";
import TestimonialBox from "@/components/testimonials/TestimonialBox";

export async function generateMetadata(): Promise<Metadata> {
  const { data: SEOData } = await GetTestimonilasSeo();

  return await generateMetadataUtil(
    SEOData?.SEO?.Title,
    SEOData?.SEO?.Description,
    "/corparote",
    SEOData?.SEO?.keywords
  );
}

export default async function Testimonials() {
  const { data } = await GetTestimonilasPage();
  const { data: clientsTestimonial } = await GetClientsTestimonial();

  const sortedTestimonials = clientsTestimonial
    ? [...clientsTestimonial].sort((a, b) => {
        const orderA = a?.TestimonialOrder || 999;
        const orderB = b?.TestimonialOrder || 999;
        return orderA - orderB;
      })
    : [];

  return (
    <main>
      <CommonBanner
      titleClassName="text-[40px] font-semibold text-[#FECB05] line-clamp-4 max-sm:text-[35px]"
        backgroundImage={`${process.env.NEXT_PUBLIC_API_BASE_URL}${
          data?.Banner?.Banner?.url || ""
        }`}
        title={data?.Banner?.Title || ""}
        description={data?.Banner?.Description || ""}
        sideImage={`${process.env.NEXT_PUBLIC_API_BASE_URL}${
          data?.Banner?.SRK_Image?.url || ""
        }`}
        sideImageHeight={1000}
        sideImageWidth={1000}
        sideImageClassName="mt-[.9rem] md:w-[480px]"
        backgroundImageHeight={1000}
        backgroundImageWidth={1000}
        breadcrumbs={[
          { label: "Media", href: "/media" },
          { label: "Testimonials", href: "/testimonials" },
        ]}
      />
      <div className="testimonials_page py-[100px] max-md:py-[50px] relative">
        <div className=" absolute top-0 left-0 w-full h-full bg-[linear-gradient(180deg,_#008BD2_6.98%,_rgba(0,139,210,0.804112)_37.87%,_rgba(0,139,210,0.480664)_53.78%,_rgba(0,139,210,0.146018)_69.69%,_rgba(0,139,210,0)_80.59%)]"></div>
        <div className="container">
          <div
            data-aos="fade-up"
            className="title_wrapper max-w-[828px] w-full mx-auto"
          >
            <h2 className="text-[44px] text-center max-w-[611px] w-full mx-auto leading-[58px] text-[#0F0F0F] font-semibold max-md:text-[35px] max-md:leading-[45px] ">
              {data?.Header?.Title && (
                <h2 className="text-[#141414] text-[44px] font-semibold text-center max-md:text-[35px] max-md:leading-[44px]">
                  <CommonText title={data?.Header?.Title} />
                </h2>
              )}{" "}
            </h2>
            <p className="text-[16px] leading-[24px] text-center font-normal text-[#FFFFFF] mt-[16px] max-md:text-[14px] max-md:leading-[22px]">
              {data?.Header?.Description}{" "}
            </p>
          </div>

          {/* testimonial section  */}
          <div className="testimonial_wrapper flex justify-start items-start gap-[30px] flex-wrap mt-[42px]">
            <TestimonialBox data={sortedTestimonials} />
          </div>
        </div>
      </div>
    </main>
  );
}
