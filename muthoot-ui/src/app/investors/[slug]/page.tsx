import CommonBanner from "@/components/common/CommonBanner";
import ReportBox from "@/components/investers/ReportBox";
import React from "react";
import { Metadata } from "next";
import { generateMetadata as generateMetadataUtil } from "@/lib/metaData";
import { GetInvestersPage, GetInvestorsSeo } from "@/lib/api/cms";
import { GetSingleInvestor } from "@/lib/api/general";
import CommonText from "@/components/common/CommonText";
import ReportTab from "@/components/investers/ReportTab";
export async function generateMetadata(): Promise<Metadata> {
  const { data: SEOData } = await GetInvestorsSeo();
  return await generateMetadataUtil(
    SEOData?.SEO?.Title,
    SEOData?.SEO?.Description,
    "/blogs",
    SEOData?.SEO?.keywords
  );
}

export default async function InvestorDetailPage({
  params,
}: {
  params: Promise<{ slug: string }>;
}) {
  const { slug } = await params;
  const { data } = await GetSingleInvestor(slug);
  const { data: investorsData } = await GetInvestersPage();
  {
    return (
      <main>
        <CommonBanner
          backgroundImage={
            data?.Banner_slug?.Banner?.url
              ? `${process.env.NEXT_PUBLIC_API_BASE_URL}${data?.Banner_slug?.Banner?.url}`
              : "/investordetailbg.svg"
          }
          title={data?.Banner_slug?.Title || ""}
          description={data?.Banner_slug?.Description || ""}
          sideImage={
            data?.Banner_slug?.SRK_Image?.url
              ? `${process.env.NEXT_PUBLIC_API_BASE_URL}${data?.Banner_slug?.SRK_Image?.url}`
              : "/investorslugsrk.svg"
          }
          sideImageHeight={1000}
          sideImageWidth={1000}
          sideImageClassName="mt-[.7rem] md:w-[391px] md:h-[427px]"
          backgroundImageHeight={1000}
          backgroundImageWidth={1000}
          breadcrumbs={[
            { label: "Investors", href: "/investors" },
            { label: data?.Breadcrumb_Name || "", href: "#" },
          ]}
        />

        <div className="investers_detailpage pb-[100px] pt-[50px] max-md:py-[50px] relative">
          <div className=" absolute top-0 left-0 w-full max-h-[1800px] h-full bg-[linear-gradient(180deg,_#008BD2_6.98%,_rgba(0,139,210,0.804112)_37.87%,_rgba(0,139,210,0.480664)_53.78%,_rgba(0,139,210,0.146018)_69.69%,_rgba(0,139,210,0)_80.59%)]"></div>
          <div className="container relative z-[9]">
            <div
              data-aos="fade-up"
              className="title_wrapper max-w-[712px] w-full mx-auto"
            >
              {data?.Header?.Title && (
                <h2 className="text-[44px] text-center leading-[58px] text-[white] font-semibold max-md:text-[35px] max-md:leading-[45px] ">
                  <CommonText title={data?.Header?.Title} />
                </h2>
              )}
              {data?.Header?.Description && (
                <p className="text-[16px] leading-[24px] text-center font-normal text-[white] mt-[16px] max-md:text-[14px] max-md:leading-[22px]">
                  {data?.Header?.Description}
                </p>
              )}
            </div>

            <div className="annual_reportTabwrapper mt-[60px]">
              <ReportTab reportData={data} />
            </div>
          </div>
        </div>
      </main>
    );
  }
}
