import CommonBanner from "@/components/common/CommonBanner";
import InvesterListBox from "@/components/investers/investerBox";
import React from "react";
import { Metadata } from "next";
import { generateMetadata as generateMetadataUtil } from "@/lib/metaData";
import { GetInvestersPage, GetInvestorsSeo } from "@/lib/api/cms";
import CommonText from "@/components/common/CommonText";
import { GetInvestors } from "@/lib/api/general";
export async function generateMetadata(): Promise<Metadata> {
  const { data: SEOData } = await GetInvestorsSeo();

  return await generateMetadataUtil(
    SEOData?.SEO?.Title,
    SEOData?.SEO?.Description,
    "/corparote",
    SEOData?.SEO?.keywords
  );
}

export default async function Investors() {
  const { data } = await GetInvestersPage();
  const { data: data1 } = await GetInvestors();

  return (
    <main>
      <CommonBanner
        backgroundImage={`${process.env.NEXT_PUBLIC_API_BASE_URL}${
          data?.Banner?.Banner?.url || ""
        }`}
        title={data?.Banner?.Title || ""}
        description={data?.Banner?.Description || ""}
        sideImage={`${process.env.NEXT_PUBLIC_API_BASE_URL}${
          data?.Banner?.SRK_Image?.url || ""
        }`}
        sideImageHeight={1000}
        sideImageWidth={1000}
        sideImageClassName="mt-[.6rem] md:w-[412px]"
        backgroundImageHeight={1000}
        backgroundImageWidth={1000}
        breadcrumbs={[{ label: "Investors", href: "#" }]}
      />
      <div className="awards_page py-[100px] max-md:py-[50px] relative">
        <div className=" absolute top-0 left-0 w-full  h-full bg-[linear-gradient(#008bd2_6.98%,_#008bd2cd_37.87%,_#008bd27b_53.78%,_#008bd225_200.69%,_#008bd200_121.59%)]"></div>
        <div className="container relative z-[9]">
          <div
            data-aos="fade-up"
            className="title_wrapper max-w-[712px] w-full mx-auto"
          >
            {data?.Header?.Title && (
              <h2 className="text-[44px] text-center leading-[58px] text-[white] font-semibold max-md:text-[35px] max-md:leading-[45px] ">
                <CommonText title={data?.Header?.Title} />
              </h2>
            )}
            {data?.Header?.Description && (
              <p className="text-[16px] leading-[24px] text-center font-normal text-[white] mt-[16px] max-md:text-[14px] max-md:leading-[22px]">
                {data?.Header?.Description}
              </p>
            )}
          </div>
          <div className="awards_listing flex justify-start items-start gap-[32px] flex-wrap mt-[60px]">
            {/* <InvesterListBox data={data1} /> */}
            <div className="list_wrapper max-sm:w-full">
              <div className="single_list">
                {/* <h3 className="text-[30px] font-semibold text-[#141414] max-w-[400px] w-full">
                  Financial Disclosures and other related information
                </h3> */}
                <InvesterListBox data={data1} />
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
