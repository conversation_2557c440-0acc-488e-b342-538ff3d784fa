import CommonBanner from "@/components/common/CommonBanner";
import { GetNewsEventPage } from "@/lib/api/cms";
import { GetNewsEvents, GetNewsList } from "@/lib/api/general";
import { Metadata } from "next";
import React from "react";
import { generateMetadata as generateMetadataUtil } from "@/lib/metaData";
import NewsEventsBox from "@/components/news/NewsBox";
export async function generateMetadata(): Promise<Metadata> {
  const { data: SEOData } = await GetNewsEventPage();

  return await generateMetadataUtil(
    SEOData?.SEO?.Title,
    SEOData?.SEO?.Description,
    "/news",
    SEOData?.SEO?.keywords
  );
}

export default async function NewsEvents() {
  const { data } = await GetNewsEventPage();
  const { data: newsEvents } = await GetNewsEvents();

  return (
    <main>
      <CommonBanner
        backgroundImage={`${process.env.NEXT_PUBLIC_API_BASE_URL}${data?.Banner?.Banner?.url}`}
        title={data?.Banner?.Title}
        description={data?.Banner?.Description}
        sideImage={`${process.env.NEXT_PUBLIC_API_BASE_URL}${data?.Banner?.SRK_Image?.url}`}
        sideImageHeight={1000}
        sideImageWidth={1000}
        sideImageClassName="w-[360px] h-[439px] mt-2.5 md:ml-9 "
        backgroundImageHeight={1000}
        backgroundImageWidth={1000}
        breadcrumbs={[
          { label: "Media", href: "/media" },
          { label: "News & Events", href: "/news" },
        ]}
      />

      <div className="news_listpage pb-[100px] pt-[50px] max-sm:py-[50px] relative">
        <div className=" absolute top-0 left-0 w-full h-[1070px] bg-[linear-gradient(180deg,_#008BD2_6.98%,_rgba(0,139,210,0.804112)_37.87%,_rgba(0,139,210,0.480664)_53.78%,_rgba(0,139,210,0.146018)_69.69%,_rgba(0,139,210,0)_80.59%)]"></div>
        <div className="container relative z-[9]">
          <div className="title_wrapper max-w-[900px] w-full mx-auto">
            <h2
              data-aos="fade-up"
              className="text-[44px] text-center leading-[58px] text-[white] font-semibold max-md:text-[35px] max-md:leading-[45px] "
            >
              {data?.Header?.Title}
            </h2>
            <p
              data-aos="fade-up"
              className="text-[16px] leading-[24px] text-center font-normal text-[white] mt-[16px] max-md:text-[14px] max-md:leading-[22px]"
            >
              {data?.Header?.Description}
            </p>
          </div>

          <div className="news_listing flex justify-start gap-[36px] flex-wrap items-start mt-[36px]">
            <NewsEventsBox data={newsEvents} />
          </div>
        </div>
      </div>
    </main>
  );
}
