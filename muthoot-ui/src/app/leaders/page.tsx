import YouthDirectors from "@/components/about/YouthDirectors";
import CommonBanner from "@/components/common/CommonBanner";
import { Metadata } from "next";
import CommonText from "@/components/common/CommonText";
import { generateMetadata as generateMetadataUtil } from "@/lib/metaData";
import {
  GetLeadership,
  GetLeadershipPage,
  GetLeadershipSeo,
  GetNextGenerationPage,
  GetNextGenerationSeo,
} from "@/lib/api/cms";
import Leadership from "@/components/about/Leadership";

export async function generateMetadata(): Promise<Metadata> {
  const { data: SEOData } = await GetLeadershipSeo();

  return await generateMetadataUtil(
    SEOData?.SEO?.Title,
    SEOData?.SEO?.Description,
    "/corparote",
    SEOData?.SEO?.keywords
  );
}

export default async function LeadershipPage() {
  const { data } = await GetLeadershipPage();
  const { data: data2 } = await GetLeadership();
  return (
    <main>
      <CommonBanner
        backgroundImage={
          data?.Banner?.Banner?.url
            ? `${process.env.NEXT_PUBLIC_API_BASE_URL}${data.Banner.Banner.url}`
            : ""
        }
        title={data?.Banner?.Title || ""}
        description={data?.Banner?.Description || ""}
        sideImage={
          data?.Banner?.SRK_Image?.url
            ? `${process.env.NEXT_PUBLIC_API_BASE_URL}${data.Banner.SRK_Image.url}`
            : ""
        }
        sideImageHeight={1000}
        underOverlay={false}
        gradient={false}
        sideImageWidth={1000}
        overlayColor="rgb(25 29 33 / 60%)"
        backgroundImageClassName="common_banner leadership_banner w-full h-full min-h-[489px]  bg-cover bg-no-repeat relative max-lg:h-full"
        sideImageClassName="mt-[.9rem] md:w-[340px]"
        backgroundImageHeight={1000}
        backgroundImageWidth={1000}
        breadcrumbs={[
          { label: "About Us", href: "/about" },
          { label: "Leadership", href: "#" },
        ]}
      />
      <div className="youth_directors py-[100px] max-md:py-[50px] relative">
        <div className=" absolute top-0 left-0 w-full h-full bg-[linear-gradient(180deg,_#008BD2_6.98%,_rgba(0,139,210,0.804112)_37.87%,_rgba(0,139,210,0.480664)_53.78%,_rgba(0,139,210,0.146018)_69.69%,_rgba(0,139,210,0)_80.59%)]"></div>
        <div className="container">
          <div
            data-aos="fade-up"
            className="title max-w-[630px] w-full mx-auto"
          >
            {data?.Header?.Title && (
              <h2 className="text-[white] text-[44px] font-semibold text-center max-md:text-[35px] max-md:leading-[44px]">
                The <span className="text-[#FECB05]">Leadership Team</span>
              </h2>
            )}
            {data?.Header?.Description && (
              <p className="text-[16px] leading-[24px] fontnormal text-[white] text-center mt-[22px] max-md:text-[14px] max-md:leading-[22px] max-md:mt-[14px]">
                {data?.Header?.Description}
              </p>
            )}
          </div>

          <div className="grid_wrapper flex flex-wrap justify-start items-start gap-[16px] mt-[46px] max-xl:justify-center">
            <Leadership Leaders={data2} />
          </div>
        </div>
      </div>
    </main>
  );
}
