import CommonBanner from "@/components/common/CommonBanner";
import React from "react";
import Image from "next/image";
import Link from "next/link";
import LeadersSwiper from "@/components/about/LeadersSwiper";
import AwardsSlider from "@/components/media/AwardsSlider";
import FounderBox from "@/components/about/FounderBox";
import { generateMetadata as generateMetadataUtil } from "@/lib/metaData";
import { Metadata } from "next";
import {
  GetAboutUsPage,
  GetAboutUsSeo,
  GetAwards,
  GetLeaders,
  GetLeaderShipTeams,
} from "@/lib/api/general";
import CommonText from "@/components/common/CommonText";
import Leaders1Swiper from "@/components/about/Leaders1Swiper";
import Button from "@/components/ui/Button";
import RenderDescriptionContent from "@/components/common/RenderDescriptionContent";
import MilestoneCard from "@/components/about/MilestoneCard";
export async function generateMetadata(): Promise<Metadata> {
  const { data: SEOData } = await GetAboutUsSeo();
  return await generateMetadataUtil(
    SEOData?.SEO?.Title,
    SEOData?.SEO?.Description,
    "/about",
    SEOData?.SEO?.keywords,
    {
      openGraph: {
        images: [SEOData?.SEO?.image?.url],
      },
      structured: {
        "@context": "https://schema.org",
        "@type": "AboutPage",
        mainEntity: {
          "@type": "Organization",
          name: "Muthoot Capital Services",
          description: SEOData?.SEO?.Description,
        },
      },
    }
  );
}

export default async function About() {
  const { data } = await GetAboutUsPage();
  return (
    <main>
      <div className="about_bannermainwrapper">
        <CommonBanner
          backgroundImage="/bg.svg"
          title={data?.Banner?.Title || ""}
          description={data?.Banner?.Description || ""}
          sideImage={`${process.env.NEXT_PUBLIC_API_BASE_URL}${
            data?.Banner?.SRK_Image?.url || ""
          }`}
          sideImageHeight={2000}
          sideImageWidth={2000}
          sideImageClassName="mt-[.6rem] md:w-[503px] md:h-[463px]"
          backgroundImageHeight={1000}
          backgroundImageWidth={1000}
          overlayColor=""
          breadcrumbs={[{ label: "About Company", href: "/about" }]}
        />
      </div>

      <div className="about_mainpage py-[100px] max-md:pt-[50px] max-md:pb-[80px] relative">
        <div className="about_shade  max-sm:max-h-[100%] bg-[linear-gradient(180.36deg,_#0058A3_2.5%,_rgba(0,139,210,0.804112)_48.18%,_rgba(0,139,210,0.146018)_74.51%,_rgba(0,139,210,0)_82.32%)] w-full h-full absolute top-0 left-0"></div>
        <div className="container">
          <div
            data-aos="fade-up"
            className="title flex justify-start mb-[100px] items-start flex-col gap-[26px] max-lg:flex-col max-lg:gap-[30px] max-md:gap-[20px]"
          >
            {data?.Header?.Title && (
              <h2 className="text-[44px] font-semibold leading-[50px] text-center text-[white] max-w-[1034px] mx-auto w-full max-lg:max-w-full max-md:text-[35px] max-md:leading-[43px] ">
                <CommonText title={data?.Header?.Title} />
              </h2>
            )}
            {data?.Header?.Description && (
              <p className="text-[16px] leading-[24px] text-[white] text-center font-normal max-w-[890px] mx-auto w-full max-lg:max-w-full max-md:text-[14px]  max-md:leading-[22px]">
                {data?.Header?.Description}
              </p>
            )}
          </div>
          {/* {data?.Memorial_Card && (
            <div data-aos="fade-up" className="founder_mainwrapper">
              <FounderBox data={data?.Memorial_Card} />
            </div>
          )} */}

          <div className="title flex justify-between items-start gap-[100px] mb-[64px] max-lg:flex-col max-lg:gap-[30px] max-md:gap-[20px]">
            {data?.Operating_Philosophy?.Title && (
              <h2
                data-aos="fade-up"
                className="text-[44px] font-semibold leading-[50px] text-[#141414] max-w-[505px] w-full max-lg:max-w-full max-md:text-[35px] max-md:leading-[43px] "
              >
                <CommonText title={data?.Operating_Philosophy?.Title} />
              </h2>
            )}
            {data?.Operating_Philosophy?.Description && (
              <p
                data-aos="fade-down"
                className="text-[16px] leading-[24px] text-[#ffffff] font-normal max-w-[600px] w-full max-lg:max-w-full max-md:text-[14px] max-md:leading-[22px]"
              >
                {data?.Operating_Philosophy?.Description}
              </p>
            )}
          </div>

          <div className="vision_wrapper flex justify-between items-center gap-[100px] mt-[42px] mb-[100px] max-lg:flex-col max-lg:gap-[60px] max-md:gap-[35px] max-md:mb-[50px]">
            {data?.Operating_Philosophy?.SRK_Image && (
              <div
                data-aos="fade-up"
                className="vision_banner max-w-[450px] w-full"
              >
                <Image
                  className=" max-w-full w-full  object-contain"
                  src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${data?.Operating_Philosophy?.SRK_Image?.url}`}
                  alt="vison image"
                  width={1000}
                  height={1000}
                />
              </div>
            )}
            {data?.Operating_Philosophy?.FrameWorks &&
              data?.Operating_Philosophy?.FrameWorks.length > 0 && (
                <div className="vision_contents max-w-[calc(100%-550px)] w-full max-lg:max-w-full">
                  {data?.Operating_Philosophy?.FrameWorks.map(
                    (framework: any) => (
                      <div
                        data-aos="fade-up"
                        key={framework.id}
                        className="vision_singleitem py-[30px] border-b border-[#BAE7FE] max-md:py-[20px]"
                      >
                        {framework.Title && (
                          <h3 className="text-[32px] font-semibold text-[#141414] max-md:text-[28px]">
                            <CommonText title={framework.Title} />
                          </h3>
                        )}
                        {framework.Description && (
                          <p className="text-[16px] font-normal leading-[24px] text-[white] mt-[16px] max-md:text-[14px] max-md:leading-[22px] max-md:mt-[10px]">
                            {framework.Description}
                          </p>
                        )}
                      </div>
                    )
                  )}
                </div>
              )}
          </div>
        </div>

        <section className="milestone relative z-[9] py-[100px] pt-[20px]">
          <div className="container">
            <h3 className="text-[44px] font-[600] text-white text-center mb-[30px] max-sm:text-[35px]">
              {data?.MilestonesCard?.Title}
            </h3>
            {data?.MilestonesCard?.Milestones && (
              <MilestoneCard data={data?.MilestonesCard?.Milestones} />
            )}
            {/* <div className="milestone_mapwrapper h-[720px] relative pt-[40px] max-w-[1200px] w-full">
              <div className="first_step_row relative z-[9] top-[8px] flex justify-start gap-[240px] items-start">
                <div className="single_milestone flex justify-center items-center gap-[10px] flex-col max-w-[200px] w-full">
                  <div className="content_box rounded-[10px] bg-[#003E7D] p-[40px]  w-full min-h-[126px] flex justify-center items-center ">
                    <h4 className="text-[14px] font-[600] text-white text-center line-clamp-3">
                      Started Operations
                    </h4>
                  </div>

                  <div className="year_box w-[66px] h-[66px] flex justify-center items-center rounded-full  border-[5px] border-[#F8B900] text-[18px] font-[600] text-[#141414] bg-white">
                    1994
                  </div>
                </div>
                <div className="single_milestone flex justify-center items-center gap-[10px] flex-col max-w-[200px] w-full">
                  <div className="content_box rounded-[10px] bg-[#003E7D] p-[40px]  w-full min-h-[126px] flex justify-center items-center   ">
                    <h4 className="text-[14px] font-[600] text-white text-center line-clamp-3">
                      Listed on BSE
                    </h4>
                  </div>

                  <div className="year_box w-[66px] h-[66px] flex justify-center items-center rounded-full  border-[5px] border-[#F8B900] text-[18px] font-[600] text-[#141414] bg-white">
                    1995
                  </div>
                </div>
                <div className="single_milestone flex justify-center items-center gap-[10px] flex-col max-w-[200px] w-full">
                  <div className="content_box rounded-[10px] bg-[#003E7D] p-[40px]  w-full min-h-[126px] flex justify-center items-center   ">
                    {" "}
                    <h4 className="text-[14px] font-[600] text-white text-center line-clamp-3">
                      Entered the Two-Wheeler financing segment
                    </h4>
                  </div>

                  <div className="year_box w-[66px] h-[66px] flex justify-center items-center rounded-full  border-[5px] border-[#F8B900] text-[18px] font-[600] text-[#141414] bg-white">
                    2008
                  </div>
                </div>
              </div>
              <div className="second_step_row relative z-[9] top-[20px] flex justify-start gap-[80px] max-w-[800px]  mx-auto  items-start">
                <div className="single_milestone flex justify-center items-center gap-[10px] flex-col max-w-[200px] w-full">
                  <div className="content_box rounded-[10px] bg-[#003E7D] p-[40px]  w-full min-h-[126px] flex justify-center items-center ">
                    <h4 className="text-[14px] font-[600] text-white text-center line-clamp-3">
                      MCSL ranks among Top 50 NBFC across India
                    </h4>
                  </div>

                  <div className="year_box w-[66px] h-[66px] flex justify-center items-center rounded-full  border-[5px] border-[#F8B900] text-[18px] font-[600] text-[#141414] bg-white">
                    2014
                  </div>
                </div>
                <div
                  className="single_milestone reverse_singlemilestone flex justify-center items-center gap-[10px]  max-w-[200px] w-full mt-[-134px] flex-col-reverse
                "
                >
                  <div className="content_box rounded-[10px] bg-[#003E7D] p-[40px]  w-full min-h-[126px] flex justify-center items-center   ">
                    <h4 className="text-[14px] font-[600] text-white text-center line-clamp-3">
                      AUM crosses 1000 Cr
                    </h4>
                  </div>

                  <div className="year_box w-[66px] h-[66px] flex justify-center items-center rounded-full  border-[5px] border-[#F8B900] text-[18px] font-[600] text-[#141414] bg-white">
                    2016
                  </div>
                </div>
                <div className="single_milestone flex justify-center items-center gap-[10px] flex-col max-w-[200px] w-full">
                  <div className="content_box rounded-[10px] bg-[#003E7D] p-[40px]  w-full min-h-[126px] flex justify-center items-center   ">
                    {" "}
                    <h4 className="text-[14px] font-[600] text-white text-center line-clamp-3">
                      AUM crosses 500 Cr
                    </h4>
                  </div>

                  <div className="year_box w-[66px] h-[66px] flex justify-center items-center rounded-full  border-[5px] border-[#F8B900] text-[18px] font-[600] text-[#141414] bg-white">
                    2018
                  </div>
                </div>
              </div>
              <div className="third_step_row relative z-[9] top-[8px] flex justify-start gap-[240px] items-start">
                <div className="single_milestone reverse_singlemilestone flex justify-center items-center gap-[10px] flex-col max-w-[200px] w-full">
                  <div className="content_box rounded-[10px] bg-[#003E7D] p-[40px]  w-full min-h-[126px] flex justify-center items-center ">
                    <h4 className="text-[14px] font-[600] text-white text-center line-clamp-3">
                      AUM crosses 2000 Cr
                    </h4>
                  </div>

                  <div className="year_box w-[66px] h-[66px] flex justify-center items-center rounded-full  border-[5px] border-[#F8B900] text-[18px] font-[600] text-[#141414] bg-white">
                    2019
                  </div>
                </div>
                <div className="single_milestone reverse_singlemilestone flex justify-center items-center gap-[10px] flex-col max-w-[200px] w-full">
                  <div className="content_box rounded-[10px] bg-[#003E7D] p-[40px]  w-full min-h-[126px] flex justify-center items-center   ">
                    <h4 className="text-[14px] font-[600] text-white text-center line-clamp-3">
                      Listed on BSE
                    </h4>
                  </div>

                  <div className="year_box w-[66px] h-[66px] flex justify-center items-center rounded-full  border-[5px] border-[#F8B900] text-[18px] font-[600] text-[#141414] bg-white">
                    2022
                  </div>
                </div>
                <div className="single_milestone reverse_singlemilestone flex justify-center items-center gap-[10px] flex-col max-w-[200px] w-full">
                  <div className="content_box rounded-[10px] bg-[#003E7D] p-[40px]  w-full min-h-[126px] flex justify-center items-center   ">
                    {" "}
                    <h4 className="text-[14px] font-[600] text-white text-center line-clamp-3">
                      AUM crosses 2500 Cr
                    </h4>
                  </div>

                  <div className="year_box w-[66px] h-[66px] flex justify-center items-center rounded-full  border-[5px] border-[#F8B900] text-[18px] font-[600] text-[#141414] bg-white">
                    2024
                  </div>
                </div>
              </div>
            </div> */}
          </div>
        </section>

        <section className="muthoot_pappachansection relative z-[9]">
          <div className="container">
            <div
              data-aos="fade-up"
              className="box_wrapper flex gap-[70px] justify-start items-start p-[50px] rounded-[20px] bg-[#1AADF0] max-lg:flex-col max-lg:items-start max-lg:gap-[30px] max-sm:p-[20px]"
            >
              <div className="logo max-w-[315px] w-full max-lg:max-w-[250px] max-sm:max-w-[150px]">
                <Image
                  className="  w-full  object-contain"
                  src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${data?.Section_6Aboutus?.Image?.url}`}
                  alt="logo"
                  width={1000}
                  height={1000}
                />
              </div>
              <div className="content max-w-[calc(100%-381px)] w-full max-lg:max-w-full">
                <h3 className="text-[36px] font-semibold text-[#FFFFFF] max-sm:text-[28px]">
                  {data?.Section_6Aboutus?.Title}
                </h3>
                <p className="text-[16px] font-[400] text-[#FFFFFF] mt-[18px] max-sm:text-[14px]">
                  {data?.Section_6Aboutus?.Description}
                </p>
              </div>
            </div>
          </div>
        </section>

        <section className="bluesoch_section pt-[100px] max-md:pt-[60px] relative z-[9]">
          <div className="container">
            <div
              data-aos="fade-up"
              className="wrapper flex justify-start items-start gap-[90px] max-lg:gap-[40px] max-md:flex-col max-md:gap-[30px]"
            >
              <div className="title">
                <h4 className="text-[36px] font-bold text-[#008BD2]">
                  {data?.Section7_About?.Title}
                </h4>
                <h5 className="text-[16px] font-bold text-[#484848] mt-[24px] text-justify">
                  {data?.Section7_About?.Description}
                </h5>
              </div>
              <div className="description">
                {data?.Section7_About?.Description_2?.map(
                  (item: any, index: any) => (
                    <RenderDescriptionContent key={index} item={item} />
                  )
                )}
              </div>
            </div>
          </div>
        </section>

        {/* <section className="financial_needs_section bg-[#008BD2] py-[100px] relative max-md:py-[50px]">
          <div className="container">
            {data?.Product_Section?.Title && (
              <h2
                data-aos="fade-up"
                className="text-[44px] max-w-[577px] w-full mx-auto text-center font-semibold text-white max-md:text-[30px] max-md:leading-[40px]"
              >
                <CommonText
                  data-aos="fade-up"
                  title={data?.Product_Section?.Title}
                  highlightColor="#FECB05"
                  defaultColor="#ffffff"
                />
              </h2>
            )}

            <div className="masked_srk absolute bottom-0 right-0">
              <Image
                className=" max-w-full w-full  object-contain"
                src="/srk_mask.png"
                alt="srk"
                width={756}
                height={1081}
              />
            </div>

            <div className="financial_loanwrapper flex justify-start items-start gap-[16px] flex-wrap mt-[36px] relative z-[9]">
              <div
                data-aos="fade-up"
                className="financial_box self-stretch flex justify-start items-center gap-[16px] p-[14px] rounded-[14px] border bg-[rgba(255,255,255,0.2)] border-[rgba(255,255,255,0.3)] w-[calc(100%/2-8px)] max-xl:max-w-[800px] max-xl:mx-auto max-xl:w-full hover:bg-[#5b3d3d33] max-sm:items-start "
              >
                <div className="financial_image max-w-[210px] w-full max-sm:max-w-[100px]">
                  <Image
                    className="rounded-[6px] w-full h-full aspect-square object-cover"
                    src="/financial_loans1.png"
                    alt="founder"
                    width={200}
                    height={210}
                  />
                </div>

                <div className="financial_contents max-w-[calc(100%-210px)] w-full max-sm:max-w-[calc(100%-100px)]">
                  <h4 className="text-[20px] leading-[26px] text-white font-semibold line-clamp-2 max-md:text-[18px] ma-md:leading-[25px]">
                    Two Wheeler Loan
                  </h4>
                  <p className="text-[16px] leading-[22px] text-white font-normal mt-[12px] line-clamp-4 max-md:text-[14px] max-md:leading-[22px] max-md:mt-[6px]">
                    Getting a two-wheeler loan is easy and seamless with Muthoot
                    Capital. Avail bike finance to zip through the streets and
                    be on time every time.
                  </p>

                  <Link
                    href=""
                    className="view_all text-[16px] font-bold text-white mt-[14px] flex justify-start items-center gap-[4px] max-md:text-[14px] max-md:mt-[8px]"
                  >
                    View All{" "}
                    <svg
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M9 18L15 12L9 6"
                        stroke="white"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </Link>
                </div>
              </div>
              <div
                data-aos="fade-up"
                className="financial_box flex self-stretch justify-start items-center gap-[16px] p-[14px] rounded-[14px] border bg-[rgba(255,255,255,0.2)] border-[rgba(255,255,255,0.3)] w-[calc(100%/2-8px)] max-xl:max-w-[800px] max-xl:mx-auto max-xl:w-full hover:bg-[#5b3d3d33] max-sm:items-start"
              >
                <div className="financial_image max-w-[210px] w-full max-sm:max-w-[100px]">
                  <Image
                    className="rounded-[6px] w-full h-full aspect-square object-cover"
                    src="/financial_loans1.png"
                    alt="founder"
                    width={200}
                    height={210}
                  />
                </div>

                <div className="financial_contents max-w-[calc(100%-210px)] w-full max-sm:max-w-[calc(100%-100px)]">
                  <h4 className="text-[20px] leading-[26px] text-white font-semibold line-clamp-2 max-md:text-[18px] ma-md:leading-[25px]">
                    Two Wheeler Loan
                  </h4>
                  <p className="text-[16px] leading-[22px] text-white font-normal mt-[12px] line-clamp-4 max-md:text-[14px] max-md:leading-[22px] max-md:mt-[6px]">
                    Getting a two-wheeler loan is easy and seamless with Muthoot
                    Capital. Avail bike finance to zip through the streets and
                    be on time every time.
                  </p>

                  <Link
                    href=""
                    className="view_all text-[16px] font-bold text-white mt-[14px] flex justify-start items-center gap-[4px] max-md:text-[14px] max-md:mt-[8px]"
                  >
                    View All{" "}
                    <svg
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M9 18L15 12L9 6"
                        stroke="white"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </Link>
                </div>
              </div>
              <div
                data-aos="fade-up"
                className="financial_box flex self-stretch justify-start items-center gap-[16px] p-[14px] rounded-[14px] border bg-[rgba(255,255,255,0.2)] border-[rgba(255,255,255,0.3)] w-[calc(100%/2-8px)] max-xl:max-w-[800px] max-xl:mx-auto max-xl:w-full hover:bg-[#5b3d3d33] max-sm:items-start"
              >
                <div className="financial_image max-w-[210px] w-full max-sm:max-w-[100px]">
                  <Image
                    className="rounded-[6px] w-full h-full aspect-square object-cover"
                    src="/financial_loans1.png"
                    alt="founder"
                    width={200}
                    height={210}
                  />
                </div>

                <div className="financial_contents max-w-[calc(100%-210px)] w-full max-sm:max-w-[calc(100%-100px)]">
                  <h4 className="text-[20px] leading-[26px] text-white font-semibold line-clamp-2 max-md:text-[18px] ma-md:leading-[25px]">
                    Two Wheeler Loan
                  </h4>
                  <p className="text-[16px] leading-[22px] text-white font-normal mt-[12px] line-clamp-4 max-md:text-[14px] max-md:leading-[22px] max-md:mt-[6px]">
                    Getting a two-wheeler loan is easy and seamless with Muthoot
                    Capital. Avail bike finance to zip through the streets and
                    be on time every time.
                  </p>

                  <Link
                    href=""
                    className="view_all text-[16px] font-bold text-white mt-[14px] flex justify-start items-center gap-[4px] max-md:text-[14px] max-md:mt-[8px]"
                  >
                    View All{" "}
                    <svg
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M9 18L15 12L9 6"
                        stroke="white"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </Link>
                </div>
              </div>
              <div
                data-aos="fade-up"
                className="financial_box flex self-stretch justify-start items-center gap-[16px] p-[14px] rounded-[14px] border bg-[rgba(255,255,255,0.2)] border-[rgba(255,255,255,0.3)] w-[calc(100%/2-8px)] max-xl:max-w-[800px] max-xl:mx-auto max-xl:w-full hover:bg-[#5b3d3d33] max-sm:items-start"
              >
                <div className="financial_image max-w-[210px] w-full max-sm:max-w-[100px]">
                  <Image
                    className="rounded-[6px] w-full h-full aspect-square object-cover"
                    src="/financial_loans1.png"
                    alt="founder"
                    width={200}
                    height={210}
                  />
                </div>

                <div className="financial_contents max-w-[calc(100%-210px)] w-full max-sm:max-w-[calc(100%-100px)]">
                  <h4 className="text-[20px] leading-[26px] text-white font-semibold line-clamp-2 max-md:text-[18px] ma-md:leading-[25px]">
                    Two Wheeler Loan
                  </h4>
                  <p className="text-[16px] leading-[22px] text-white font-normal mt-[12px] line-clamp-4 max-md:text-[14px] max-md:leading-[22px] max-md:mt-[6px]">
                    Getting a two-wheeler loan is easy and seamless with Muthoot
                    Capital. Avail bike finance to zip through the streets and
                    be on time every time.
                  </p>

                  <Link
                    href=""
                    className="view_all text-[16px] font-bold text-white mt-[14px] flex justify-start items-center gap-[4px] max-md:text-[14px] max-md:mt-[8px]"
                  >
                    View All{" "}
                    <svg
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M9 18L15 12L9 6"
                        stroke="white"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </Link>
                </div>
              </div>
              <div
                data-aos="fade-up"
                className="financial_box flex self-stretch justify-start items-center gap-[16px] p-[14px] rounded-[14px] border bg-[rgba(255,255,255,0.2)] border-[rgba(255,255,255,0.3)] w-[calc(100%/2-8px)] max-xl:max-w-[800px] max-xl:mx-auto max-xl:w-full hover:bg-[#5b3d3d33] max-sm:items-start"
              >
                <div className="financial_image max-w-[210px] w-full max-sm:max-w-[100px]">
                  <Image
                    className="rounded-[6px] w-full h-full aspect-square object-cover"
                    src="/financial_loans1.png"
                    alt="founder"
                    width={200}
                    height={210}
                  />
                </div>

                <div className="financial_contents max-w-[calc(100%-210px)] w-full max-sm:max-w-[calc(100%-100px)]">
                  <h4 className="text-[20px] leading-[26px] text-white font-semibold line-clamp-2 max-md:text-[18px] ma-md:leading-[25px]">
                    Two Wheeler Loan
                  </h4>
                  <p className="text-[16px] leading-[22px] text-white font-normal mt-[12px] line-clamp-4 max-md:text-[14px] max-md:leading-[22px] max-md:mt-[6px]">
                    Getting a two-wheeler loan is easy and seamless with Muthoot
                    Capital. Avail bike finance to zip through the streets and
                    be on time every time.
                  </p>

                  <Link
                    href=""
                    className="view_all text-[16px] font-bold text-white mt-[14px] flex justify-start items-center gap-[4px] max-md:text-[14px] max-md:mt-[8px]"
                  >
                    View All{" "}
                    <svg
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M9 18L15 12L9 6"
                        stroke="white"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </Link>
                </div>
              </div>
              <div
                data-aos="fade-up"
                className="financial_box self-stretch flex justify-start items-center gap-[16px] p-[14px] rounded-[14px] border bg-[rgba(255,255,255,0.2)] border-[rgba(255,255,255,0.3)] w-[calc(100%/2-8px)] max-xl:max-w-[800px] max-xl:mx-auto max-xl:w-full hover:bg-[#5b3d3d33] max-sm:items-start"
              >
                <div className="financial_image max-w-[210px] w-full max-sm:max-w-[100px]">
                  <Image
                    className="rounded-[6px] w-full h-full aspect-square object-cover"
                    src="/financial_loans1.png"
                    alt="founder"
                    width={200}
                    height={210}
                  />
                </div>

                <div className="financial_contents max-w-[calc(100%-210px)] w-full max-sm:max-w-[calc(100%-100px)]">
                  <h4 className="text-[20px] leading-[26px] text-white font-semibold line-clamp-2 max-md:text-[18px] ma-md:leading-[25px]">
                    Two Wheeler Loan
                  </h4>
                  <p className="text-[16px] leading-[22px] text-white font-normal mt-[12px] line-clamp-4 max-md:text-[14px] max-md:leading-[22px] max-md:mt-[6px]">
                    Getting a two-wheeler loan is easy and seamless with Muthoot
                    Capital. Avail bike finance to zip through the streets and
                    be on time every time.
                  </p>

                  <Link
                    href=""
                    className="view_all text-[16px] font-bold text-white mt-[14px] flex justify-start items-center gap-[4px] max-md:text-[14px] max-md:mt-[8px]"
                  >
                    View All{" "}
                    <svg
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M9 18L15 12L9 6"
                        stroke="white"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </Link>
                </div>
              </div>
            </div>

            <div
              data-aos="fade-up"
              className="link_wrapper btn_wrapper mt-[42px] flex justify-center"
            >
              <Link href="/category" className=" relative z-[60]">
                <Button
                  className="bg-white !text-[#008BD2] hover:!text-[white]"
                  children="View All"
                />
              </Link>
            </div>
          </div>
        </section> */}

        {/* <section className="managing_director py-[100px] max-md:py-[50px] ">
          <div className="container">
            <div className="wrapper flex justify-center items-center gap-[120px] max-lg:flex-col max-lg:gap-[50px] max-md:gap-[40px]">
              <div className="content max-w-[calc(100%-520px)] w-full max-lg:max-w-full">
                <h3
                  data-aos="fade-up"
                  className="text-[38px] font-semibold text-[#141414] max-md:text-[30px] max-md:leading-[40px]"
                >
                  {data?.Chairman?.Name}
                </h3>
                <h6
                  data-aos="fade-up"
                  className="text-[22px] font-medium text-[#008BD2] mt-[12px] max-md:text-[18px]"
                >
                  {data?.Chairman?.Designation}
                </h6>
                <p
                  data-aos="fade-up"
                  className="leading-[26px] text-[16px] text-[#484848] font-normal mt-[32px] max-md:text-[14px] max-md:leading-[24px] max-md:mt-[20px]"
                >
                  {data?.Chairman?.About}
                </p>
              </div>
              <div className="video_parentwrapper max-w-[521px] w-full">
                <Image
                  data-aos="fade-down"
                  className=" max-w-[521px] w-full object-cover rounded-[20px] aspect-[521/540]"
                  src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${data?.Chairman?.Image?.url}`}
                  alt="chairman image"
                  width={1000}
                  height={1000}
                />
              </div>
            </div>
          </div>
        </section> */}

        {/* <section className="leadership_team">
          <div className="container">
            <div className="title">
              {data?.Team_Section?.Title && (
                <h2
                  data-aos="fade-up"
                  className="text-[44px] font-semibold text-[#141414] text-center max-md:text-[35px] max-md:leading-[43px]"
                >
                  {data?.Team_Section?.Title}
                </h2>
              )}
              {data?.Team_Section?.Description && (
                <p
                  data-aos="fade-up"
                  className="mt-[22px] text-[16px] leading-[24px] text-[#484848] max-w-[606px] w-full mx-auto text-center max-md:text-[14px] max-md:leading-[22px] max-md:mt-[12px]"
                >
                  {data?.Team_Section?.Description}
                </p>
              )}
            </div>
          </div>
          <div className="awards_sliderwrapper ">
            <LeadersSwiper leaders={leadersData} />
          </div>
        </section> */}

        {/* <section className="leaders_groupsection py-[100px] bg-[#008BD2] mt-[100px] max-md:py-[50px] max-md:mt-[50px]">
          <div className="container">
            <div className="title">
              {data?.Leaders_Section?.Title && (
                <h2
                  data-aos="fade-up"
                  className="text-[44px] font-semibold text-white text-center max-md:text-[35px] max-md:leading-[43px]"
                >
                  {data?.Leaders_Section?.Title}
                </h2>
              )}
              {data?.Leaders_Section?.Description && (
                <p
                  data-aos="fade-up"
                  className="mt-[22px] text-[16px] leading-[24px] text-white max-w-[606px] w-full mx-auto text-center max-md:text-[14px] max-md:mt-[14px] "
                >
                  {data?.Leaders_Section?.Description}
                </p>
              )}
            </div>
          </div>
          <div className="awards_sliderwrapper">
            <Leaders1Swiper leaders={leaders} />
          </div>
        </section> */}

        {/* <section className="awards_section mt-[100px] max-md:mt-[50px]">
          <div className="container">
            <div className="title">
              {data?.Award_Section?.Title && (
                <h2
                  data-aos="fade-up"
                  className="text-[44px] font-semibold text-[#141414] text-center max-md:text-[35px] max-md:leading-[43px]"
                >
                  {data?.Award_Section?.Title}
                </h2>
              )}
              {data?.Award_Section?.Description && (
                <p
                  data-aos="fade-up"
                  className="mt-[22px] text-[16px] leading-[24px] text-[#484848] max-w-[606px] w-full mx-auto text-center max-md:text-[14px] max-md:mt-[14px]"
                >
                  {data?.Award_Section?.Description}
                </p>
              )}
            </div>

            <div data-aos="fade-up" className="awards_sliderwrapper">
              <AwardsSlider data={awards} />
            </div>
          </div>
        </section> */}
      </div>
    </main>
  );
}
