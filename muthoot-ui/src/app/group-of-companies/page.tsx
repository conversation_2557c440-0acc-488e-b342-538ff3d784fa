import CommonText from "@/components/common/CommonText";
import { GroupOfCompanies } from "@/lib/api/cms";
import Image from "next/image";
import React from "react";

export default async function GroupCompanies() {
  const { data } = await GroupOfCompanies();
  const sortedCompanies = data?.CompaniesBox
    ? [...data.CompaniesBox].sort((a, b) => {
        const orderA = a?.Company_Order || 999;
        const orderB = b?.Company_Order || 999;
        return orderA - orderB;
      })
    : [];

  const fallbackImage = "/fallbacklogo.svg";

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#008BD2] via-[#0099E6] to-[#0066B3] relative">
      <div className="absolute inset-0 opacity-10">
        <div
          className="absolute top-0 left-0 w-full h-full"
          style={{
            backgroundImage: `radial-gradient(circle at 25% 25%, white 2px, transparent 2px),
                                radial-gradient(circle at 75% 75%, white 2px, transparent 2px)`,
            backgroundSize: "50px 50px",
          }}
        ></div>
      </div>

      <div className="relative z-10">
        <div className="documents_listing py-[100px] max-md:py-[60px] relative overflow-hidden">
          <div className="container max-w-[1200px] mx-auto px-4">
            <div className="title max-w-[624px] w-full mx-auto mb-[60px]">
              <h2
                data-aos="fade-up"
                className="text-[44px] font-semibold text-white leading-[53px] text-center max-md:text-[35px] max-md:leading-[43px] mb-6"
              >
                <CommonText
                  title={data?.Header?.Title || "Our Group of Companies"}
                  highlightColor="#FECB05"
                  defaultColor="#ffffff"
                />
              </h2>

              <p
                data-aos="fade-up"
                className="text-[18px] leading-[28px] text-white/90 font-normal text-center max-md:text-[16px] max-md:leading-[24px]"
              >
                {data?.Header?.Description ||
                  "Discover our diverse portfolio of innovative companies driving excellence across multiple industries"}
              </p>
            </div>

            <div className="companies_grid grid grid-cols-3 gap-[40px] mt-[50px] max-lg:grid-cols-2 max-md:grid-cols-1 max-lg:gap-[30px] max-md:gap-[25px]">
              {sortedCompanies?.map((company: any, index: number) => (
                <div
                  key={company.id || index}
                  data-aos="fade-up"
                  data-aos-delay={index * 100}
                  className="company_card_wrapper"
                >
                  <a
                    href={company.RedirectionLink || "#"}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="company_card bg-white/10 backdrop-blur-md border border-white/20 p-[35px] rounded-[20px] cursor-pointer hover:bg-white/20 hover:border-white/40 hover:transform hover:scale-105 hover:-translate-y-2 transition-all duration-500 block no-underline group shadow-xl hover:shadow-2xl"
                  >
                    <div className="logo_container flex justify-center mb-[25px]">
                      <div className="logo_wrapper w-[120px] h-[120px] bg-gradient-to-br from-white/20 to-white/5 backdrop-blur-sm rounded-full flex items-center justify-center shadow-2xl group-hover:shadow-3xl transition-all duration-500 border border-white/30 group-hover:border-white/50 relative overflow-hidden">
                        <div className="absolute inset-0 bg-gradient-to-br from-[#FECB05]/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-full"></div>

                        <div className="w-[100px] h-[100px] bg-[#005aa5] rounded-full p-3 shadow-lg">
                          <Image
                            className="w-full h-full object-contain"
                            src={
                              company?.Icon?.url
                                ? `${process.env.NEXT_PUBLIC_API_BASE_URL}${company?.Icon?.url}`
                                : fallbackImage
                            }
                            alt={`${company.Company_Name} Icon`}
                            width={80}
                            height={80}
                          />
                        </div>

                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 rounded-full"></div>
                      </div>
                    </div>

                    <h3 className="text-white text-[20px] font-[700] text-center leading-[26px] group-hover:text-[#FECB05] transition-colors duration-300 mb-4 min-h-[52px] flex items-center justify-center">
                      {company.Company_Name || "Company Name"}
                    </h3>

                    <div className="mt-[20px] text-center">
                      <div className="inline-flex items-center justify-center bg-white/10 backdrop-blur-sm border border-white/20 rounded-full px-6 py-3 text-white/90 text-[14px] font-medium group-hover:bg-[#FECB05] group-hover:text-[#051066] group-hover:border-[#FECB05] transition-all duration-300 min-w-[120px]">
                        <span className="mr-2">Learn More</span>
                        <svg
                          className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 5l7 7-7 7"
                          />
                        </svg>
                      </div>
                    </div>
                  </a>
                </div>
              ))}
            </div>
          </div>

          {/* <div className="absolute top-0 left-0 w-full h-full pointer-events-none overflow-hidden">
            <div className="absolute top-[15%] left-[8%] w-[120px] h-[120px] bg-white/5 rounded-full animate-pulse"></div>
            <div
              className="absolute bottom-[25%] right-[12%] w-[180px] h-[180px] bg-white/5 rounded-full animate-pulse"
              style={{ animationDelay: "1s" }}
            ></div>
            <div
              className="absolute top-[45%] left-[85%] w-[100px] h-[100px] bg-[#FECB05]/10 rounded-full animate-pulse"
              style={{ animationDelay: "2s" }}
            ></div>
            <div
              className="absolute bottom-[60%] left-[5%] w-[60px] h-[60px] bg-white/8 rounded-full animate-pulse"
              style={{ animationDelay: "0.5s" }}
            ></div>
          </div> */}

          {/* <div className="absolute top-0 left-0 w-full h-32 bg-gradient-to-b from-black/10 to-transparent pointer-events-none"></div>
          <div className="absolute bottom-0 left-0 w-full h-32 bg-gradient-to-t from-black/10 to-transparent pointer-events-none"></div> */}
        </div>
      </div>
    </div>
  );
}
