import CommonBanner from "@/components/common/CommonBanner";
import DigitalTabs from "@/components/digital-initiatives/DigitalTabs";
import { SecuredChargesPage } from "@/lib/api/cms";
import React from "react";
import { generateMetadata as generateMetadataUtil } from "@/lib/metaData";
import { Metadata } from "next";
import DepositTable from "@/components/category/DepositTable";
export async function generateMetadata(): Promise<Metadata> {
  const { data: SEOData } = await SecuredChargesPage();

  return await generateMetadataUtil(
    SEOData?.SEO?.Title,
    SEOData?.SEO?.Description,
    "/digital-intiatives",
    SEOData?.SEO?.keywords
  );
}

export default async function ScheduledCharges() {
  const { data } = await SecuredChargesPage();
  return (
    <main>
      <CommonBanner
        backgroundImage={
          `${process.env.NEXT_PUBLIC_API_BASE_URL}${data?.Banner?.Banner?.url}` ||
          ""
        }
        title={data?.Banner?.Title}
        description={data?.Banner?.Description}
        sideImage={`${process.env.NEXT_PUBLIC_API_BASE_URL}${data?.Banner?.SRK_Image?.url}`}
        sideImageHeight={1000}
        sideImageWidth={1000}
        sideImageClassName="mt-[.5rem] md:w-[324px] md:h-[449px]"
        backgroundImageHeight={1000}
        backgroundImageWidth={1000}
        breadcrumbs={[
          { label: "Scheduled Charges", href: "/scheduled-charges" },
        ]}
      />
      <div className="csr_Listingpage">
        {data?.Table ? (
          <div className="fixed_deposite_tablewrapper pt-[80px] max-md:pt-[50px] relative z-[9] pb-[100px] max-md:pb-[100px] ">
            <div className="container" data-aos="fade-up">
              <DepositTable data={data?.Table} />
            </div>
          </div>
        ) : null}
      </div>
    </main>
  );
}
