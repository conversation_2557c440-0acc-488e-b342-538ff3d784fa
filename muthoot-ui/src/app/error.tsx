"use client"; // Error boundaries must be Client Components

import { useEffect } from "react";
import Image from "next/image";
import Link from "next/link";

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    console.error(error);
  }, [error]);

  return (
    <div className="flex flex-col items-center justify-center text-center pb-[125px] pt-[100px]">
      <div className="w-[357px] h-[237px]">
        <Image
          src="/500.png"
          alt="500"
          width={1000}
          height={1000}
          className="-mt-6"
        />
      </div>
      <p className="md:text-[40px] text-[26px] font-bold text-[#0F0F0F]">
        Internal Server Error
      </p>
      <p className="mt-2 md:max-w-[500px] max-w-[300px] text-center md:text-[16px] text-[12px] font-light text-[#3C3C3C]">
        Oops! Something went wrong on our server. We're working to fix the
        issue.
      </p>
    </div>
  );
}
