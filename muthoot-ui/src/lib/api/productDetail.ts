import endpoints from "@/endpoints";

// async function GetProBanner(id: string) {
//   const response = await fetch(
//     process.env.NEXT_PUBLIC_API_URL +
//       endpoints.getSingleProduct +
//       id +
//       "?product-details?populate=Banner_Detail.Banner",
//     {
//       method: "GET",
//     }
//   );
//   return response.json();
// }
async function GetProBanner(id: string) {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getProBanner
  );
  return await response.json();
}
async function GetProHeader() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getProHeader
  );
  return await response.json();
}
async function GetBenefits() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getBenefits
  );
  return await response.json();
}
async function GetProCalculatorSec() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getProCalculatorSec
  );
  return await response.json();
}
async function GetKeyBenefit() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getKeyBenefit
  );
  return await response.json();
}
async function GetEligibility() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getEligibility
  );
  return await response.json();
}
async function GetDocumentSec() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getDocumentSec
  );
  return await response.json();
}
async function GetProccesing() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getProccesing
  );
  return await response.json();
}
async function GetBranchesSec() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getBranchesSec
  );
  return await response.json();
}
async function GetProFaq() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getProFaq
  );
  return await response.json();
}
async function GetProClientsTestimonial() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getProClientsTestimonial
  );
  return await response.json();
}

export {
  GetProBanner,
  GetProHeader,
  GetBenefits,
  GetProCalculatorSec,
  GetKeyBenefit,
  GetEligibility,
  GetDocumentSec,
  GetProccesing,
  GetBranchesSec,
  GetProFaq,
  GetProClientsTestimonial,
};
