import endpoints from "@/endpoints";
//BLOG
async function GetBlogPage() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getBlogPage
  );
  return await response.json();
}
//NEWS-EVENTS
async function GetNewsEventPage() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getNewsEventPage
  );
  return await response.json();
}
//DIGITAL INITIATIVES
async function GetDigitalInitiativesPage() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getDigitalInitiativesPage
  );
  return await response.json();
}
//AWARDS & RECOGNITION
async function getAwardsPage() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getAwardsPage
  );
  return await response.json();
}
async function getAwardsSeo() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getAwardsSeo
  );
  return await response.json();
}
//IN MEMMORIUM
async function getInMemoriumSeo() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getInMemoriumSeo
  );
  return await response.json();
}
//CORPORATE ETHOS
async function GetCorporateEthosPage() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getCorporateEthosPage
  );
  return await response.json();
}
async function GetCorporateEthosSeo() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getCorporateEthosSeo
  );
  return await response.json();
}

//NEXT GENERATION

async function GetNextGenerationPage() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getNextGenerationPage
  );
  return await response.json();
}
async function GetNextGenerationSeo() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getNextGenerationSeo
  );
  return await response.json();
}

//OUR DIRECTORS
async function GetDirectorsPage() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getDirectorsPage
  );
  return await response.json();
}
async function GetDirectorsSeo() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getDirectorsSeo
  );
  return await response.json();
}

//HISTORY PAGE
async function GetHistoryPage() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getHistoryPage
  );
  return await response.json();
}
async function GetHistoryPageSeo() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getHistoryPageSeo
  );
  return await response.json();
}

//INVESTORS
async function GetInvestersPage() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getInvestersPage
  );
  return await response.json();
}
async function GetInvestorsSeo() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getInvestorsSeo
  );
  return await response.json();
}

//CSR
async function GetCsrPage() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getCsrPage
  );
  return await response.json();
}

//POLICIES
async function GetPoliciesPage() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getPoliciesPage
  );
  return await response.json();
}
async function GetPoliciesSeo() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getPoliciesSeo
  );
  return await response.json();
}

// HOMEPAGE
async function GetHomePage() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getHomePage
  );
  return await response.json();
}
async function GetHomePageSeo() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getHomePageSeo
  );
  return await response.json();
}

async function GetContactPage() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getContactPage
  );
  return await response.json();
}
async function GetContactPageSeo() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getContactSeo
  );
  return await response.json();
}

async function GetTestimonilasPage() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getTestimonialPage
  );
  return await response.json();
}
async function GetTestimonilasSeo() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getTestimonialSeo
  );
  return await response.json();
}

async function GetMediaPage() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getMediaPage
  );
  return await response.json();
}
async function GetMediaSeo() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getMediaSeo
  );
  return await response.json();
}

async function GetTncPage() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getTncPage
  );
  return await response.json();
}
async function GetTncSeo() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getTncSeo
  );
  return await response.json();
}

async function GetPcpPage() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getPrivacyPage
  );
  return await response.json();
}
async function GetPcpSeo() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getPrivacySeo
  );
  return await response.json();
}

// LAEDERSHIP PAGE
async function GetLeadershipPage() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getLeadershipPage
  );
  return await response.json();
}
async function GetLeadershipSeo() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getLeadershipSeo
  );
  return await response.json();
}
async function GetLeadership() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getLeadership
  );
  return await response.json();
}

//CAREERS PAGE
async function GetCareersPage() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getCareersPage
  );
  return await response.json();
}

async function GetCareerMuthootees() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getCareerMuthootees
  );
  return await response.json();
}

async function GetTestimonialCareer() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getTestimonialCareer
  );
  return await response.json();
}

async function GetCareersSeo() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getCareersSeo
  );
  return await response.json();
}

//BRANCH PAGE
async function GetBranchPage() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getBranchePage
  );
  return await response.json();
}

async function GetBranches() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getBranches
  );
  return await response.json();
}

async function GetBranchSeo() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getBranchSeo
  );
  return await response.json();
}

async function GetClientsTestimonial() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getClientsTestimonial
  );
  return await response.json();
}
async function GetClientsTestimonial4() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getClientsTestimonial4
  );
  return await response.json();
}
async function GetCsrHome() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getCsrHome
  );
  return await response.json();
}
async function NotFoundPage() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.notFoundPage
  );
  return await response.json();
}

async function SecuredChargesPage() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.securedChargesPage
  );
  return await response.json();
}
async function GroupOfCompanies() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.groupOfCompanies
  );
  return await response.json();
}

export {
  GetPcpPage,
  GetPcpSeo,
  GetTncPage,
  GetTncSeo,
  GetMediaPage,
  GetMediaSeo,
  GetTestimonilasPage,
  GetTestimonilasSeo,
  GetContactPage,
  GetContactPageSeo,
  GetBlogPage,
  GetNewsEventPage,
  GetDigitalInitiativesPage,
  getAwardsPage,
  getAwardsSeo,
  getInMemoriumSeo,
  GetCorporateEthosPage,
  GetCorporateEthosSeo,
  GetNextGenerationPage,
  GetNextGenerationSeo,
  GetDirectorsPage,
  GetDirectorsSeo,
  GetHistoryPage,
  GetHistoryPageSeo,
  GetInvestersPage,
  GetInvestorsSeo,
  GetCsrPage,
  GetPoliciesPage,
  GetPoliciesSeo,
  GetHomePage,
  GetHomePageSeo,
  GetLeadershipPage,
  GetLeadershipSeo,
  GetLeadership,
  GetCareersPage,
  GetCareerMuthootees,
  GetTestimonialCareer,
  GetCareersSeo,
  GetBranchPage,
  GetBranches,
  GetBranchSeo,
  GetClientsTestimonial,
  GetCsrHome,
  NotFoundPage,
  SecuredChargesPage,
  GetClientsTestimonial4,
  GroupOfCompanies,
};
