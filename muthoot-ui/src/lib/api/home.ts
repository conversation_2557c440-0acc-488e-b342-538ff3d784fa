import endpoints from "@/endpoints";

async function GetHome() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getHome,
    { cache: "no-store" }
  );

  return await response.json();
}
async function GetHomeLoanRedirector() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getHomeLoanRedirector,
    { cache: "no-store" }
  );

  return await response.json();
}
async function GetHomeProductSec() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getHomeProductSec
  );
  return await response.json();
}
async function GetHomeAboutSec() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getHomeAboutSec
  );
  return await response.json();
}
async function GetHomeCounterSec() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getHomeCounterSec
  );
  return await response.json();
}
async function GetShowcaseSec() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getShowcaseSec
  );
  return await response.json();
}
async function GetCalculatorSec() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getCalculatorSec
  );
  return await response.json();
}
async function GetTestimonial() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getTestimonial
  );
  return await response.json();
}
async function GetCsrSec() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getCsrSec
  );
  return await response.json();
}
async function GetBlogHome() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getBlogHome
  );
  return await response.json();
}
async function GetDigital() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getDigital
  );
  return await response.json();
}
async function GetBranchHome() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getBranchHome
  );
  return await response.json();
}
async function GetBranchHome3() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getBranchHome3
  );
  return await response.json();
}
async function GetBranchHome12() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getBranchHome12
  );
  return await response.json();
}
async function GetHomeBanner() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getHomeBanner
  );
  return await response.json();
}
async function GetHomeForm() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getHomeForm
  );
  return await response.json();
}

export {
  GetHomeLoanRedirector,
  GetHomeProductSec,
  GetHomeAboutSec,
  GetHomeCounterSec,
  GetShowcaseSec,
  GetCalculatorSec,
  GetTestimonial,
  GetCsrSec,
  GetBlogHome,
  GetDigital,
  GetBranchHome,
  GetBranchHome3,
  GetHomeBanner,
  GetHomeForm,
  GetBranchHome12,
  GetHome,
};
