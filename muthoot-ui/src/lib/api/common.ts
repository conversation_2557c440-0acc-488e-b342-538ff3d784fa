import endpoints from "@/endpoints";

async function GetFooter() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getFooter
  );
  return await response.json();
}

async function GetHeader() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getHeader
  );
  return await response.json();
}
async function GetSideBar() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getSideBar
  );
  return await response.json();
}

export { GetFooter, GetHeader, GetSideBar };
