import endpoints from "@/endpoints";

//BLOGS
async function GetBlogs() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getBlogs
  );
  return await response.json();
}
// async function GetBlogsHome() {
//   const response = await fetch(
//     process.env.NEXT_PUBLIC_API_URL + endpoints.getBlogsHome
//   );
//   return await response.json();
// }

async function GetBlogsHome() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getBlogsHome,
    {
      method: "GET",
    }
  );
  return await response.json();
}
async function GetBlogsList() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.geBlogsList,
    {
      method: "GET",
    }
  );
  return await response.json();
}

async function GetSingleBlog(id: string) {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL +
      endpoints.blog +
      id +
      "?populate=Thumbnail&populate=Banner",
    {
      method: "GET",
    }
  );
  return response.json();
}
async function GetSingleProduct(slug: string) {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL +
      endpoints.getSingleProduct +
      slug +
      "?populate=Product_Details.Banner_Detail.Banner&populate=Product_Details.Header_Sec.Logo&populate=Product_Details.Header_Sec.SRK_Image&populate=Product_Details.Benefits_Sec.SRK_Image&populate=Product_Details.Benefits_Sec.Flexible_Tenure_Texts&populate=Product_Details.Calculator_Sec.Header&populate=Product_Details.Key_Benefits_Sec.Key_Benefits_Card&populate=Product_Details.Key_Benefits_Sec.SRK_Image&populate=Product_Details.Eligibility_Sec.Criterias.Hover_Image&populate=Product_Details.Documents_Sec.Documents_Box.Icon&populate=Product_Details.Documents_Sec.SRK_Image&populate=Product_Details.Proccesing_Charges_Sec.Card_Points&populate=Product_Details.Branches_Sec.Branch_Box&populate=Product_Details.Branches_Sec.Button&populate=Product_Details.Branches_Sec.Bg_Image&populate=Product_Details.Faq_Sec.FAQ&populate=Product_Details.Client_Testimonial_sec&populate=Product_Details.Deposit_Table.Table_Content.Scheme_Tab.Scheme_Content&populate=Product_Details.Deposit_Table.Table_Content.Scheme_Tab.Column.Column_Options&populate=Product_Details.Products_Offered_Section.Products_Offered_Box&populate=Product_Details.Important_Documents_Section.Important_Document_Box.Document&populate=Product_Details.DigitalPlatform.DigitalPlatformCard.Icon&populate=Product_Details.Proccesing_Charges_Sec2.Card_Points",
    {
      method: "GET",
    }
  );
  return response.json();
}

//NEWS & EVENTS
async function GetNewsEvents() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getNews
  );
  return await response.json();
}

async function GetNewsList() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getNewsList,
    {
      method: "GET",
    }
  );
  return await response.json();
}

async function GetSingleNewsEvents(id: string) {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL +
      endpoints.newsEvents +
      id +
      "?populate=Thumbnail&populate=Banner",
    {
      method: "GET",
    }
  );
  return response.json();
}
async function GetDigitalInitiativeCategories() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getDigitalInitiativeCategories
  );
  return await response.json();
}
async function GetDigitalInitiatives() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getDigitalInitiatives
  );
  return await response.json();
}

async function GetDigitalHome() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getDigitalHome
  );
  return await response.json();
}

async function GetSingleDigitalInitiative(id: string) {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL +
      endpoints.getSingleDigitalInitiative +
      id +
      "?populate=Thumbnail&populate=Digital_initiative_categories&field=Title,DialogTitle",
    {
      method: "GET",
    }
  );
  return response.json();
}

//AWARDS & RECOGNITION
async function GetAwards() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getAwards
  );
  return await response.json();
}
async function GetSingleAward(id: string) {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL +
      endpoints.getSingleAward +
      id +
      "?populate=Thumbnail&populate=Awards.Image",
    {
      method: "GET",
    }
  );
  return response.json();
}

// IN MEMMORIUM
async function GetInMemoriumPage() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getInMemoriumPage
  );
  return await response.json();
}

//ABOUT US
async function GetAboutUsPage() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getAboutUsPage
  );
  return await response.json();
}
async function GetAboutUsSeo() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getAboutUsSeo
  );
  return await response.json();
}
async function GetLeaderShipTeams() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getLeaderShipTeams
  );
  return await response.json();
}
async function GetLeaders() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getLeaders
  );
  return await response.json();
}

// INVESTORS
async function GetInvestors() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getInvestors
  );
  return await response.json();
}
async function GetSingleInvestor(slug: string) {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL +
      endpoints.getSingleInvestor +
      slug +
      "?inverstors-listings/hriex3a65pauvt19tcglz56w?populate=Thumbnail&populate=Attachments.File&populate=Attachments.investors_tabs&populate=Banner_slug.Banner&populate=Banner_slug.SRK_Image&populate=Header&populate=Attachments.File&populate=Attachments.investors_tabs",
    {
      method: "GET",
    }
  );
  return response.json();
}
//CSR

async function GetCsrCategories() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getCsrCategories
  );
  return await response.json();
}

//POLICIES
async function GetPolicies() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getPolicies
  );
  return await response.json();
}
async function GetSinglePolicy(slug: string) {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL +
      endpoints.getSinglePolicy +
      slug +
      "?populate=Tabs&populate=Downloads.File",
    {
      method: "GET",
    }
  );
  return response.json();
}

async function GetProductPage() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getProductsPage,
    {
      method: "GET",
    }
  );
  return response.json();
}

async function GetProductPageSEO() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getProductsSeo,
    {
      method: "GET",
    }
  );
  return response.json();
}

async function GetProduct() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getProducts
  );
  return await response.json();
}
async function GetProductTitle() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getProductTitle
  );
  return await response.json();
}

// TESTIMONIAL
async function GetTestimonialListings() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.getTestimonialListings,
    {
      method: "GET",
    }
  );
  return response.json();
}
async function GetServerErrorPage() {
  const response = await fetch(
    process.env.NEXT_PUBLIC_API_URL + endpoints.serverErrorPage,
    {
      method: "GET",
    }
  );
  return response.json();
}

export {
  GetBlogsList,
  GetBlogs,
  GetSingleBlog,
  GetNewsEvents,
  GetNewsList,
  GetSingleNewsEvents,
  GetDigitalInitiativeCategories,
  GetSingleDigitalInitiative,
  GetDigitalInitiatives,
  GetAwards,
  GetSingleAward,
  GetInMemoriumPage,
  GetAboutUsPage,
  GetAboutUsSeo,
  GetLeaderShipTeams,
  GetLeaders,
  GetInvestors,
  GetSingleInvestor,
  GetCsrCategories,
  GetPolicies,
  GetSinglePolicy,
  GetProductPage,
  GetProductPageSEO,
  GetProduct,
  GetTestimonialListings,
  GetSingleProduct,
  GetBlogsHome,
  GetDigitalHome,
  GetProductTitle,
  GetServerErrorPage,
};
