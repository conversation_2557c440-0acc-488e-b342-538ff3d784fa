import CryptoJS from 'crypto-js';
export const encryptOtpData = (data: string, key: string = "MCStrlPApL112018") => {
  try {
    const keyBytes = CryptoJS.enc.Utf8.parse(key);
    
    const encrypted = CryptoJS.AES.encrypt(data, keyBytes, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7
    });
    
    return encrypted.toString();
  } catch (error) {
    console.error('Encryption error:', error);
    throw new Error('Failed to encrypt data');
  }
};

export const decryptOtpData = (encryptedData: string, key: string = "MCStrlPApL112018") => {
  try {
    const keyBytes = CryptoJS.enc.Utf8.parse(key);
    
    const decrypted = CryptoJS.AES.decrypt(encryptedData, keyBytes, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7
    });
    
    return decrypted.toString(CryptoJS.enc.Utf8);
  } catch (error) {
    console.error('Decryption error:', error);
    throw new Error('Failed to decrypt data');
  }
};