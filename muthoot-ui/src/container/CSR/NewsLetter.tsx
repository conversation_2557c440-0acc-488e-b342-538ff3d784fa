"use client";
import endpoints from "@/endpoints";
import React, { useEffect, useState } from "react";
import axios from "axios";
import toast from "react-hot-toast";
import CommonTextBlue from "@/components/common/CommonTextBlue";

const NewsLetterForm = ({ data }: any) => {
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const validateEmail = (email: string) => {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email);
  };

  const handleSubscribe = async () => {
    if (!email.trim()) {
      setError("Email is required");
      toast.error("Please enter your email address");
      return;
    }

    if (!validateEmail(email)) {
      setError("Invalid email format");
      toast.error("Please enter a valid email address");
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_API_URL}${endpoints.submitNewsLetter}`,
        { data: { Email: email } }
      );

      if (response.status === 201) {
        toast.success("Thank you for subscribing to our newsletter!");
        setEmail(""); // Reset input after successful subscription
      } else {
        toast.error("Subscription failed. Please try again.");
      }
    } catch (error) {
      console.error("Error:", error);
      toast.error("Something went wrong. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container">
      <h2 className="text-[36px] max-w-[707px] w-full mx-auto text-white font-semibold text-center leading-[43px] max-md:text-[30px] max-md:leading-[38px]">
        {data?.Title}
      </h2>
      <p className="text-[16px] font-light text-white mt-[22px] max-w-[707px] w-full mx-auto text-center max-md:text-[14px]">
        {data?.Description}
      </p>

      <div className="relative max-w-[487px] w-full mx-auto">
        <div
          className={`news_letterwrapper mt-[28px] rounded-[12px] w-full flex justify-between items-center gap-[10px] bg-white ${
            error ? "border-2 border-red-500" : ""
          }`}
        >
          <input
            className="w-full pl-[16px] h-[52px] focus:outline-none text-[14px] font-normal placeholder:font-normal text-[#484848] placeholder:opacity-[1] placeholder:text-[#484848] rounded-l-[12px]"
            placeholder="Enter your email"
            type="email"
            value={email}
            onChange={(e) => {
              setEmail(e.target.value);
              setError(null);
            }}
          />
          <button
            className="text-[16px] py-[13px] px-[33px] rounded-[7px] bg-[#008BD2] font-medium text-white max-md:text-[14px] max-md:px-[20px] hover:bg-[#004a91] transition-colors duration-300"
            onClick={handleSubscribe}
            disabled={loading}
          >
            {loading ? "Subscribing..." : "Subscribe"}
          </button>
        </div>
        {/* {error && (
          <p className="text-red-500 absolute -bottom-6 left-0 text-[14px] font-medium">
            {error}
          </p>
        )} */}
      </div>
    </div>
  );
};

export default NewsLetterForm;
