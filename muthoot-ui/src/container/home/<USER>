import Image from "next/image";
import Link from "next/link";
import React from "react";

const DigitalSection = ({ data }: any) => {
  return (
    <section className="digital_intiatives py-[100px] bg-[#E7F7FF] max-md:py-[50px]">
      <div className="container">
        <div className="media_head flex justify-between items-end mb-[36px] max-lg:flex-col max-lg:items-start max-lg:gap-[30px]">
          <div className="title max-w-[600px] w-full">
            <h3 className="text-[44px] text-[#141414] font-semibold max-md:text-[35px] max-md:leading-[45px]">
              {data?.Title}
            </h3>
            <p className="text-[16px] font-normal text-[#484848] leading-[24px] mt-[16px] text-justify max-sm:text-[14px] max-sm:leading-[22px]">
              {data?.Description}
            </p>
          </div>
          <div className="view_wrapper  max-lg:w-full max-lg:flex max-lg:justify-end">
            <Link
              className="text-[16px] font-bold text-[#008BD2] flex justify-start items-center gap-[13px]"
              href={data?.Button?.Link}
            >
              {data?.Button?.Label}
              <svg
                width="8"
                height="14"
                viewBox="0 0 8 14"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M1 13L7 7L1 1"
                  stroke="#008BD2"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>{" "}
            </Link>
          </div>
        </div>

        {/* list section  */}
        <div className="grid_wrapper flex justify-start items-start gap-[21px] flex-wrap max-md:justify-center ">
          <div className="grid_box w-[calc(100%/3-14px)] max-md:w-[calc(100%/2-11px)] max-sm:max-w-[420px] max-sm:w-full max-sm:mx-auto ">
            <div className="grid_image  overflow-hidden rounded-[20px]">
              <Image
                className=" w-full object-cover aspect-square rounded-[20px] "
                src="/digital_1.png"
                alt="csr image"
                width={411}
                height={411}
              />
            </div>
            <h6 className="text-[16px] leading-[24px] text-[##141414] font-semibold mt-[14px] max-md:text-[14px] max-md:leading-[22px]">
              Donation of Shoes & Socks to 100 Needy & Underprivileged Children
              at Munirka, New Delhi
            </h6>
          </div>
          <div className="grid_box w-[calc(100%/3-14px)] max-md:w-[calc(100%/2-11px)] max-sm:max-w-[420px] max-sm:w-full max-sm:mx-auto ">
            <div className="grid_image overflow-hidden rounded-[20px]">
              <Image
                className=" w-full object-cover aspect-square rounded-[20px]"
                src="/digital_2.png"
                alt="csr image"
                width={411}
                height={411}
              />
            </div>
            <h6 className="text-[16px] leading-[24px] text-[##141414] font-semibold mt-[14px] max-md:text-[14px] max-md:leading-[22px]">
              Donation of Shoes & Socks to 100 Needy & Underprivileged Children
              at Munirka, New Delhi
            </h6>
          </div>
          <div className="grid_box w-[calc(100%/3-14px)] max-md:w-[calc(100%/2-11px)] max-sm:max-w-[420px] max-sm:w-full max-sm:mx-auto ">
            <div className="grid_image overflow-hidden rounded-[20px]">
              <Image
                className=" w-full object-cover aspect-square rounded-[20px]"
                src="/digital_3.png"
                alt="csr image"
                width={411}
                height={411}
              />
            </div>
            <h6 className="text-[16px] leading-[24px] text-[##141414] font-semibold mt-[14px] max-md:text-[14px] max-md:leading-[22px]">
              Donation of Shoes & Socks to 100 Needy & Underprivileged Children
              at Munirka, New Delhi
            </h6>
          </div>
        </div>
      </div>
    </section>
  );
};

export default DigitalSection;
