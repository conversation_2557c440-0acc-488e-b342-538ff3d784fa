import BranchBox from "@/components/contact/BranchBox";
import Link from "next/link";
import React from "react";

const BranchSection = ({ data }: any) => {
  const branches = [
    {
      name: "Vennikulam",
      address:
        "First floor Maliakal Building, St. Bahanans high school, junction Vennikulam - 689544",
      time: "09:30 AM - 05:00 PM",
      phone: "9289063059",
      description: "Muthoot FinCorp Gold Loan",
    },
    {
      name: "Ko<PERSON><PERSON>",
      address: "2nd Floor, XYZ Building, Main Road, Kottayam - 686001",
      time: "09:00 AM - 06:00 PM",
      phone: "9876543210",
      description: "Gold Loan Services",
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      address: "2nd Floor, XYZ Building, Main Road, Kottayam - 686001",
      time: "09:00 AM - 06:00 PM",
      phone: "9876543210",
      description: "Gold Loan Services",
    },
  ];
  return (
    <section className="branch_section pb-[100px] max-md:pb-[50px]">
      <div className="container">
        <div className="media_head flex justify-between items-end mb-[36px] max-lg:flex-col max-lg:items-start max-lg:gap-[30px]">
          <div className="title max-w-[616px] w-full">
            <h3 className="text-[44px] text-[#141414] font-semibold max-md:text-[35px] max-md:leading-[45px]">
              {data?.Title}{" "}
            </h3>
            <p className="text-[16px] font-normal text-[#484848] leading-[24px] mt-[16px] text-justify max-sm:text-[14px] max-sm:leading-[22px]">
              {data?.Description}
            </p>
          </div>
          <div className="view_wrapper  max-lg:w-full max-lg:flex max-lg:justify-end">
            <Link
              className="text-[16px] font-bold text-[#008BD2] flex justify-start items-center gap-[13px]"
              href={data?.Button?.Link || "/"}
            >
              {data?.Button?.Label}
              <svg
                width="8"
                height="14"
                viewBox="0 0 8 14"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M1 13L7 7L1 1"
                  stroke="#008BD2"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>{" "}
            </Link>
          </div>
        </div>

        <div className="branch_listwrapper  flex justify-start items-start gap-[28px] mt-[36px] flex-wrap">
          <BranchBox branches={branches} />
        </div>
      </div>
    </section>
  );
};

export default BranchSection;
