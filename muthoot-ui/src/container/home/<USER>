import Button from "@/components/ui/Button";
import { Checkbox } from "@/components/ui/checkbox";
import Image from "next/image";
import React from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const ContactSection = ({ data }: any) => {
  return (
    <section className="getin_touch pt-[100px] bg-[#E7F7FF] pb-[100px] relative max-xl:pb-0 max-md:pt-[60px]">
      <div className="container">
        <div className="get_wrapper flex justify-start items-start max-xl:flex-col">
          <div className="content max-w-[367px] w-full max-xl:max-w-full">
            <h3 className="text-[44px] font-semibold text-[#141414] leading-normal max-md:text-[35px] max-md:leading-[45px] ">
              {data?.Title}
            </h3>
            <p className="text-[16px] leading-[24px] text-[#484848] font-normal mt-[26px] text-justify max-xl:mt-[12px] max-md:text-[14px]">
              {data?.Description}
            </p>
            <h4 className="mt-[42px] text-[24px] leading-[33px] text-[#141414] font-normal max-xl:mt-[15px] max-sm:text-[20px]">
              Or just wanna{" "}
              <span className="text-[#008BD2] font-semibold">Say Hi</span>?
              {data?.Mail}
            </h4>
          </div>

          <div className="form_wrapper pl-[42px] max-w-[536px] w-full max-xl:flex max-xl:justify-center max-xl:gap-[40px] max-xl:max-w-full max-xl:pl-0 max-xl:pt-[70px] max-md:flex-col max-md:items-center max-md:pt-[40px]">
            <form className="max-xl:pb-[60px] max-md:pb-0" action="">
              <div className="single_field">
                <div className="input_parent">
                  <input
                    placeholder="Your Name"
                    className="bg-white w-full border border-[rgba(33,147,209,0.5)] rounded-[8px] p-[16px] h-[46px] text-[14px] font-normal text-[#6C727F] placeholder:text-[#6C727F] "
                    type="text"
                  />
                </div>
              </div>

              <div className="multi_input flex justify-start items-start gap-[25px] mt-[25px] max-sm:flex-col">
                <div className="input_parent w-[calc(100%/2-12px)] relative max-sm:w-full">
                  <div className="mail_icon absolute right-[12px] top-[50%] -translate-y-[50%]">
                    <svg
                      width="18"
                      height="18"
                      viewBox="0 0 18 18"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        opacity="0.2"
                        d="M15.75 3.9375L9 10.125L2.25 3.9375H15.75Z"
                        fill="#6C727F"
                      />
                      <path
                        fillRule="evenodd"
                        clipRule="evenodd"
                        d="M1.83535 3.55741C2.04527 3.3284 2.40109 3.31293 2.6301 3.52285L9 9.36193L15.3699 3.52285C15.5989 3.31293 15.9547 3.3284 16.1647 3.55741C16.3746 3.78641 16.3591 4.14223 16.1301 4.35215L9.3801 10.5397C9.16504 10.7368 8.83496 10.7368 8.61991 10.5397L1.86991 4.35215C1.6409 4.14223 1.62543 3.78641 1.83535 3.55741Z"
                        fill="#6C727F"
                      />
                      <path
                        fillRule="evenodd"
                        clipRule="evenodd"
                        d="M1.6875 3.9375C1.6875 3.62684 1.93934 3.375 2.25 3.375H15.75C16.0607 3.375 16.3125 3.62684 16.3125 3.9375V13.5C16.3125 13.7984 16.194 14.0845 15.983 14.2955C15.772 14.5065 15.4859 14.625 15.1875 14.625H2.8125C2.51413 14.625 2.22798 14.5065 2.017 14.2955C1.80603 14.0845 1.6875 13.7984 1.6875 13.5V3.9375ZM2.8125 4.5V13.5H15.1875V4.5H2.8125Z"
                        fill="#6C727F"
                      />
                      <path
                        fillRule="evenodd"
                        clipRule="evenodd"
                        d="M8.18409 8.61981C8.39407 8.84876 8.37868 9.20459 8.14973 9.41456L2.80598 14.3153C2.57702 14.5253 2.2212 14.5099 2.01123 14.281C1.80125 14.052 1.81664 13.6962 2.04559 13.4862L7.38934 8.58544C7.61829 8.37547 7.97412 8.39085 8.18409 8.61981Z"
                        fill="#6C727F"
                      />
                      <path
                        fillRule="evenodd"
                        clipRule="evenodd"
                        d="M9.81591 8.61981C10.0259 8.39085 10.3817 8.37547 10.6107 8.58544L15.9544 13.4862C16.1834 13.6962 16.1988 14.052 15.9888 14.281C15.7788 14.5099 15.423 14.5253 15.194 14.3153L9.85028 9.41456C9.62132 9.20459 9.60594 8.84876 9.81591 8.61981Z"
                        fill="#6C727F"
                      />
                    </svg>
                  </div>
                  <input
                    placeholder="<EMAIL>"
                    className="bg-white w-full border pr-[33px] border-[rgba(33,147,209,0.5)] rounded-[8px] p-[16px] h-[46px] text-[14px] font-normal text-[#6C727F] placeholder:text-[#6C727F] "
                    type="text"
                  />
                </div>
                <div className="input_parent w-[calc(100%/2-12px)] max-sm:w-full ">
                  <input
                    placeholder="<EMAIL>"
                    className="bg-white w-full border border-[rgba(33,147,209,0.5)] rounded-[8px] p-[16px] h-[46px] text-[14px] font-normal text-[#6C727F] placeholder:text-[#6C727F] "
                    type="text"
                  />
                </div>
              </div>
              <div className="multi_input flex justify-start items-start gap-[25px] mt-[25px] max-sm:flex-col">
                <div className="input_parent w-[calc(100%/2-12px)] max-sm:w-full">
                  <Select>
                    <SelectTrigger className="bg-white rounded-[8px] border border-[rgba(33,147,209,0.5)] py-[22px] px-[16px] h-[46px] text-[14px] font-normal text-[#6C727F] placeholder:text-[#6C727F] max-md:h-[49px]">
                      <SelectValue placeholder="Select State" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="light">Kerala</SelectItem>
                      <SelectItem value="dark">Tamilnadu</SelectItem>
                      <SelectItem value="system">Karnataka</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="input_parent w-[calc(100%/2-12px)] max-sm:w-full ">
                  <Select>
                    <SelectTrigger className="bg-white rounded-[8px] border border-[rgba(33,147,209,0.5)] py-[22px] px-[16px] h-[46px] text-[14px] font-normal text-[#6C727F] placeholder:text-[#6C727F] max-md:h-[49px]">
                      <SelectValue placeholder="Select City" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="light">Tirur</SelectItem>
                      <SelectItem value="dark">Valanchery</SelectItem>
                      <SelectItem value="system">Kottakal</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="single_field mt-[25px]">
                <div className="input_parent">
                  <textarea
                    placeholder="Write message"
                    name=""
                    id=""
                    className="bg-white w-full border border-[rgba(33,147,209,0.5)] rounded-[8px] p-[16px] h-[46px] text-[14px] font-normal text-[#6C727F] placeholder:text-[#6C727F] min-h-[100px] "
                  ></textarea>
                </div>
              </div>

              <div className="terms_wrapper flex justify-start items-start gap-[8px] mt-[16px]">
                <div className="check_boxwrapper">
                  <Checkbox />
                </div>
                <p className="text-[12px] leading-[16px] text-[#484848] font-normal">
                  I authorize Muthoot Capital & other Muthoot Pappachan Group
                  companies (including its Agents/representatives) to
                  call/communicate with me on their product offerings/promotions
                  through Telephone/Mobile/SMS/email ID/WhatsApp.
                </p>
              </div>

              <div className="btn_wrapper mt-[26px]">
                <Button children="Send Message" />
              </div>
            </form>
            <div className="contact_banner relative hidden bottom-0 right-0 max-w-[389px] w-full max-xl:relative max-xl:block max-md:max-w-[350px] max-md:w-full">
              <Image
                className=" w-full h-full object-contain aspect-[389/605] max-xl:object-bottom "
                src="/gettouch_srkbanner.png"
                alt="contact banner"
                width={389}
                height={605}
              />
            </div>
          </div>
        </div>
      </div>
      <div className="contact_banner absolute bottom-0 right-0 max-w-[389px] w-full max-xl:relative max-xl:hidden">
        <Image
          className=" w-full h-full object-contain aspect-[389/605] "
          src="/gettouch_srkbanner.png"
          alt="contact banner"
          width={389}
          height={605}
        />
      </div>
    </section>
  );
};

export default ContactSection;
