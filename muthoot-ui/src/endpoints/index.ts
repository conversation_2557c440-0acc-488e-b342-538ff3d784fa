const endpoints = {
  //Blog
  getBlogPage:
    "blog-page?populate=SEO&populate=Banner.Banner&populate=Banner.SRK_Image&populate=Header",
  getBlogs: "blogs?populate=Thumbnail&populate=Banner",
  geBlogsList:
    "blogs?populate=Thumbnail&fields=Title,Description,Date&sort=createdAt:desc&pagination[pageSize]=6",
  blog: "blogs/",
  getBlogsHome:
    "blogs?populate=Thumbnail&fields=Title,Description,Date&sort=createdAt:asc&pagination[pageSize]=4",

  //NEWS-EVENTS
  getNewsEventPage:
    "news-and-events-page?populate=Banner.Banner&populate=Banner.SRK_Image&populate=Header",
  getNews: "news-events?populate=Thumbnail&populate=Banner",
  getNewsList:
    "news-events?fields=Title,Description,Date&sort=createdAt:desc&pagination[pageSize]=6&populate=Thumbnail",
  newsEvents: "news-events/",

  //DIGITAL INITIATIVES
  getDigitalInitiativesPage:
    "digital-initiative-page?populate=SEO&populate=Banner.Banner&populate=Banner.SRK_Image&populate=Header&populate=Digital_Initiative_Categories",
  getDigitalInitiativeCategories:
    "digital-initiative-categories?populate=digital_initiatives.Thumbnail&populate=digital_initiatives.Banner",
  getSingleDigitalInitiative: "digital-initiatives/",
  getDigitalInitiatives:
    "digital-initiatives/?populate=Thumbnail&populate=Digital_initiative_categories",
  getDigitalHome:
    "digital-initiatives/?populate=Thumbnail&field=Title&sort=createdAt:asc&pagination[pageSize]=3",

  //AWARDS & RECOGNITION
  getAwardsPage:
    "awards-and-recognition-page?populate=SEO&populate=Banner.Banner&populate=Banner.SRK_Image&populate=Header",
  getAwardsSeo: "awards-and-recognition-page?populate=SEO",
  getAwards: "awards?populate=Thumbnail",
  getSingleAward: "awards/",

  //FOOTER
  getFooter:
    "footer?populate=Logo&populate=socialMedia.Icon&populate=StockLogos.Logo&populate=StockLinks&populate=LinkSection1.Links&populate=LinkSection2.Links&populate=LinkSection3.Links&populate=Address.LegalNumbers&populate=Contact&populate=TermsPrivacy",

  // HEADER
  getHeader:
    "header?populate=Header.Sub_Menu.SRK_Image&populate=Header.Sub_Menu.Submenu_Navigators.Icon&populate=Header.SubMenu_Type_2_Srk_Image&populate=Header.SubMenu_Type_2.SubMenu.Icon&populate=Download_Button.QR_Image&populate=Logo.Logo&populate=HeaderTop.Icon",
  //IN MEMMORIUM
  getInMemoriumPage: "in-memoriam-page?populate=Header&populate=Folks.Image",
  getInMemoriumSeo: "in-memoriam-page?populate=SEO",

  //CORPORATE ETHOS
  getCorporateEthosPage:
    "corporate-ethos-page?populate=Banner.Banner&populate=Banner.SRK_Image&populate=Corporate_Ethos_Card.SRK_Image&populate=Corporate_Ethos_Card.Image&populate=Corporate_Ethos_Card.Values&populate=Fundamental_Principle_Cards.SRK_Image&populate=Fundamental_Principle_Cards.Principle_Cards&populate=Fundamental_Principle_Cards.Principle_Cards.Image",
  getCorporateEthosSeo: "corporate-ethos-page?populate=SEO",

  //NEXT GENERATION
  getNextGenerationPage:
    "next-generation-page?populate=Banner.Banner&populate=Banner.SRK_Image&populate=Header&populate=Nextgen_Card.Image&populate=Header2&populate=Independent_Directors_Card.Image",
  getNextGenerationSeo: "next-generation-page?populate=SEO",

  //OUR DIRECTORS
  getDirectorsPage:
    "our-director-page?populate=Banner.Banner&populate=Banner.SRK_Image&populate=Directors_Section&populate=Directors.Image&populate=Directors.About",
  getDirectorsSeo: "our-director-page?populate=SEO",

  //HISTORY
  getHistoryPage:
    "our-history-page?populate=Banner.Banner&populate=Banner.SRK_Image&populate=Header&populate=Images.Image&populate=Pillars_Section.Folks.Image&populate=LandScapeImages.Image",
  getHistoryPageSeo: "our-history-page?populate=SEO",

  //ABOUT US
  getAboutUsPage:
    "about-us-page?populate=Banner.SRK_Image&populate=Header&populate=Operating_Philosophy.SRK_Image&populate=Operating_Philosophy.FrameWorks&populate=Section_6Aboutus.Image&populate=Section7_About&populate=MilestonesCard.Milestones",
  getAboutUsSeo: "about-us-page?populate=SEO",

  //LEADERSHIP TEAMS
  getLeaderShipTeams: "leadership-teams?populate=*",

  //LEADERS
  getLeaders: "leaders?populate=*",

  //INVESTORS
  getInvestersPage:
    "inverstors-page?populate=Banner.Banner&populate=Banner.SRK_Image&populate=Header&populate=Download_Button_Icon&populate=Download_Pdf_Icon&populate=Banner_slug.Banner&populate=Banner_slug.SRK_Image",
  getInvestors:
    "inverstors-listings?populate=Attachments.File&populate=investors_section_title",
  getInvestorsSeo: "inverstors-page?populate=SEO",
  getSingleInvestor: "inverstors-listings/",

  //CSR
  getCsrPage:
    "csr-page?populate=Banner.Banner&populate=Banner.SRK_Image&populate=SEO&populate=Header&populate=Csr_Newsletter",
  getCsrCategories: "csr-categories?fields=Title",
  getCsrCollection:
    "csrs?fields=Title,Description&populate[Thumbnail][fields]=url,name&populate[CSR_categories][fields]=Title",
  getCsrSeo: "csr-page?populate=SEO",
  getSingleCsr: "csrs/",
  submitNewsLetter: "csr-news-letters",
  getCsrHome:
    "csrs?fields=Title,Description&populate[Thumbnail][fields]=url,name",

  //Policies
  getPoliciesPage:
    "policies-page?populate=Banner.Banner&populate=Banner.SRK_Image&populate=Header",
  getPoliciesSeo: "policies-page?populate=SEO",
  getPolicies:
    "policies?populate=Tabs&populate=Downloads.File&pagination[pageSize]=1000",
  getSinglePolicy: "policies/",

  // Home page
  getHomePage:
    "home-page?populate=AboutUs_Section.Logo&populate=AboutUs_Section.SRK_Image&populate=AboutUs_Section.Counts_Bottom.Icon&populate=AboutUs_Section.Count_Top.Image&populate=Product_Section.SRK_Image&populate=Loan_Section.SRK_Image&populate=Loan_Section.Button&populate=Calculator_Section&populate=CIBIL_Section.SRK_Image&populate=CIBIL_Section.Button&populate=Showcase_Section.Button&populate=Blog_Section.Button&populate=Digital_Initiative_Section.Button&populate=CSR_Section.Button&populate=Branch_Section.Button&populate=FAQ_Section&populate=Contact_Section.Button&populate=Banner.SRK_Image&populate=AboutUs_Section.Button",
  getHomePageSeo: "home-page?populate=SEO",

  // PRODUCTS

  getProductsPage:
    "product-listing-page?populate=Banner.Banner&populate=Banner.SRK_Image",
  getProductsSeo: "product-listing-page?populate=SEO",
  getProducts:
    "products?populate=Thumbnail&fields=Title&fields=Short_Description&fields=slug&fields=Breadcrumb_Name&fields=Product_Order",
  getSingleProduct: "products/",
  getProductTitle: "products?fields=Title&fields=slug",

  // CONTACT PAGE

  getContactPage:
    "contact-us-page?populate=Banner.Banner&populate=Banner.SRK_Image&populate=Header&populate=Enquiry.Image&populate=OfficeAddress.Icon&populate=Branch&populate=Conncet_Section.SRK_Image&populate=Conncet_Section.Button.Image",
  getContactSeo: "contact-us-page?populate=SEO",

  // TESTIMONILA PAGE

  getTestimonialPage:
    "testimonial?populate=Banner.Banner&populate=Banner.SRK_Image&populate=Header",
  getTestimonialSeo: "testimonial?populate=SEO",
  getTestimonialListings: "testimonials-listings?populate=*",

  // MEDIA PAGE

  getMediaPage:
    "media-page?populate=Banner.Banner&populate=Banner.SRK_Image&populate=News_Section.Header&populate=Blog_Section.Header&populate=Award_Section.Header&populate=Testimonial_Section.Header&populate=News_Section.Button&populate=Blog_Section.Button&populate=Award_Section.Button&populate=Testimonial_Section.Button",
  getMediaSeo: "media-page?populate=SEO",

  // TERMS & CONDITIONS
  getTncPage:
    "terms-and-condition-page?populate=Banner.Banner&populate=Banner.SRK_Image&populate=Header",
  getTncSeo: "terms-and-conditions-page?populate=SEO",

  // PRIVACY POLICY
  getPrivacyPage:
    "privacy-policy-page?populate=Banner.Banner&populate=Banner.SRK_Image&populate=Header",
  getPrivacySeo: "privacy-policy-page?populate=SEO",

  // LEADERSHIP PAGE
  getLeadershipPage:
    "Leadership-page?populate=Banner.Banner&populate=Banner.SRK_Image&populate=Header",
  getLeadershipSeo: "Leadership-page?populate=SEO",
  getLeadership: "leadership-teams?populate=Image",

  // CAREERS PAGE
  getCareersPage:
    "career-page?populate=Banner&populate=Banner.Banner&populate=Header.Button&populate=Certified_Label_Image&populate=Banner.SRK_Image&populate=Header.SRK_Image",
  getCareerMuthootees:
    "career-page?populate=Muthootees.Header&populate=Muthootees.Image1&populate=Muthootees.Image2&populate=Muthootees.Image3&populate=Muthootees.Image4&populate=Muthootees.Image5",
  getTestimonialCareer: "career-page?populate=TestimonialSection.User_Image",
  getCareersSeo: "careers-page?populate=SEO",

  // BRANCH
  getBranchePage:
    "branch-page?populate=Banner.Banner&populate=Banner.SRK_Image&populate=Header",
  getBranches: "branches?pagination[pageSize]=1000",
  getBranchSeo: "branch-page?populate=SEO",
  getBranchHome3: "branches?sort=createdAt:asc&pagination[pageSize]=3",
  getBranchHome12: "branches?sort=createdAt:asc&pagination[pageSize]=12",

  // HOME PAGE
  getHomeLoanRedirector: "home-page?populate=Loan_Redirector.Icon",
  getHomeProductSec: "home-page?populate=Product_Section.SRK_Image",
  getHomeAboutSec:
    "home-page?populate=AboutUs_Section.Logo&populate=AboutUs_Section.SRK_Image&populate=AboutUs_Section.Counts_Bottom.Icon&populate=AboutUs_Section.Counts_Top.Icon&populate=AboutUs_Section.Button",
  getHomeCounterSec: "home-page?populate=Counter_Section.Icon",
  getShowcaseSec:
    "home-page?populate=Showcase_Brand_Section.ShowcaseVideos.Video&populate=Showcase_Brand_Section.ShowcaseVideos.Thumbnail",
  getCalculatorSec: "home-page?populate=Calculators_Section.Calculators.Button",
  getTestimonial: "home-page?populate=Clients_Testimonials_section.Button",
  getCsrSec: "home-page?populate=CSR_Section.Button",
  getBlogHome: "home-page?populate=Blog_Section.Button",
  getDigital: "home-page?populate=Digital_Initiative_Section.Button",
  getBranchHome: "home-page?populate=Branches_Section.Button",
  getHomeBanner:
    "home-page?populate=Home_Banner.ImageOrVideo&populate=Home_Banner.mobileBanner",
  getHomeForm:
    "home-page?populate=Contact_Section.Button&populate=Contact_Section.SRK_Image",
  getHome:
    "home-page?populate=Loan_Redirector.Icon&populate=Product_Section.SRK_Image&populate=AboutUs_Section.Logo&populate=AboutUs_Section.SRK_Image&populate=AboutUs_Section.Counts_Bottom.Icon&populate=AboutUs_Section.Counts_Top.Icon&populate=AboutUs_Section.Button&populate=Counter_Section.Icon&populate=Showcase_Brand_Section.ShowcaseVideos.Video&populate=Showcase_Brand_Section.ShowcaseVideos.Thumbnail&populate=Calculators_Section.Calculators.Button&populate=Clients_Testimonials_section.Button&populate=CSR_Section.Button&populate=Blog_Section.Button&populate=Digital_Initiative_Section.Button&populate=Branches_Section.Button&populate=Home_Banner.ImageOrVideo&populate=Home_Banner.mobileBanner&populate=Contact_Section.Button&populate=Contact_Section.SRK_Image",

  //CLIENTS TESTIMONIALS
  getClientsTestimonial:
    "clients-testimonials?populate=User_Image&pagination[pageSize]=1000",
  getClientsTestimonial4:
    "clients-testimonials?populate=User_Image&pagination[pageSize]=4",

  // PRODUCT DETAIL PAGE
  getProBanner: "product-details?populate=Banner_Detail.Banner",
  getProHeader:
    "product-details?populate=Product_Details.Header_Sec.Logo&populate=Product_Details.Header_Sec.SRK_Image",
  getBenefits:
    "product-details?populate=Product_Details.Benefits_Sec.SRK_Image&populate=Product_Details.Benefits_Sec.Flexible_Tenure_Texts",
  getProCalculatorSec:
    "product-details?populate=Product_Details.Calculator_Sec.Header",
  getKeyBenefit:
    "product-details?populate=Product_Details.Key_Benefits_Sec.Key_Benefits_Card&populate=Product_Details.Key_Benefits_Sec.SRK_Image",
  getEligibility:
    "product-details?populate=Product_Details.Eligibility_Sec.Criterias.Hover_Image",
  getDocumentSec:
    "product-details?populate=Product_Details.Documents_Sec.Documents_Box.Icon&populate=Product_Details.Documents_Sec.SRK_Image",
  getProccesing:
    "product-details?populate=Product_Details.Proccesing_Charges_Sec.Card_Points",
  getBranchesSec:
    "product-details?populate=Product_Details.Branches_Sec.Branch_Box&populate=Product_Details.Branches_Sec.Button&populate=Product_Details.Branches_Sec.Bg_Image",
  getProFaq: "product-details?populate=Product_Details.Faq_Sec.FAQ",
  getProClientsTestimonial:
    "product-details?populate=Product_Details.Client_Testimonial_sec",
  postContactForm: "contact-us-forms",
  postGetinTouchForm: "get-in-touch-forms",
  getSideBar: "sidebars?populate=*",
  notFoundPage: "not-found-page?populate=Section.Image",
  serverErrorPage: "server-error-page?populate=Section.Image",
  securedChargesPage:
    "secured-charges-page?populate=Table.Table_Content.Scheme_Tab.Scheme_Content&populate=Table.Table_Content.Scheme_Tab.Column.Column_Options&populate=Banner.Banner&populate=Banner.SRK_Image",
  groupOfCompanies:
    "group-of-company?populate=Header&populate=CompaniesBox.Icon",
};

export default endpoints;
