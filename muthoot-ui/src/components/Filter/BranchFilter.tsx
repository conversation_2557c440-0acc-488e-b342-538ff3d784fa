"use client";
import React, { useState, useEffect, useRef } from "react";
import Button1 from "../ui/Button1";
import useFetch from "@/hooks/useFetch";
import endpoints from "@/endpoints";
import { ChevronDown } from "lucide-react";

export default function BranchFilter({ data, onFilter }: any) {
  const [states, setStates] = useState<string[]>([]);
  const [districts, setDistricts] = useState<string[]>([]);
  const [locations, setLocations] = useState<string[]>([]);

  const [selectedState, setSelectedState] = useState<string>("");
  const [selectedDistrict, setSelectedDistrict] = useState<string>("");
  const [selectedLocation, setSelectedLocation] = useState<string>("");
  const [selectedService, setSelectedService] = useState<string>("");

  const [openDropdown, setOpenDropdown] = useState<string | null>(null);

  // Refs for dropdown containers
  const stateDropdownRef = useRef<HTMLDivElement>(null);
  const districtDropdownRef = useRef<HTMLDivElement>(null);
  const locationDropdownRef = useRef<HTMLDivElement>(null);
  const serviceDropdownRef = useRef<HTMLDivElement>(null);

  // Check if any filter is selected
  const isAnyFilterSelected =
    selectedState || selectedDistrict || selectedLocation || selectedService;

  const { data: serviceData } = useFetch<any>(
    `${process.env.NEXT_PUBLIC_API_URL}${endpoints.getProductTitle}`,
    { cache: false }
  );

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        openDropdown === "state" &&
        stateDropdownRef.current &&
        !stateDropdownRef.current.contains(event.target as Node)
      ) {
        setOpenDropdown(null);
      } else if (
        openDropdown === "district" &&
        districtDropdownRef.current &&
        !districtDropdownRef.current.contains(event.target as Node)
      ) {
        setOpenDropdown(null);
      } else if (
        openDropdown === "location" &&
        locationDropdownRef.current &&
        !locationDropdownRef.current.contains(event.target as Node)
      ) {
        setOpenDropdown(null);
      } else if (
        openDropdown === "service" &&
        serviceDropdownRef.current &&
        !serviceDropdownRef.current.contains(event.target as Node)
      ) {
        setOpenDropdown(null);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [openDropdown]);

  useEffect(() => {
    if (data && data.data && data.data.length > 0) {
      // Get unique states from data.data array
      const uniqueStates = [
        ...new Set(data.data.map((branch: any) => branch.State)),
      ];
      setStates(uniqueStates as string[]);
    }
  }, [data]);

  // Update districts when state changes
  useEffect(() => {
    if (selectedState && data && data.data) {
      const filteredBranches = data.data.filter(
        (branch: any) => branch.State === selectedState
      );
      const uniqueDistricts = [
        ...new Set(filteredBranches.map((branch: any) => branch.District)),
      ];
      setDistricts(uniqueDistricts as string[]);
      setSelectedDistrict("");
      setLocations([]);
      setSelectedLocation("");
    }
  }, [selectedState, data]);

  // Update locations when district changes
  useEffect(() => {
    if (selectedDistrict && data && data.data) {
      const filteredBranches = data.data.filter(
        (branch: any) =>
          branch.State === selectedState && branch.District === selectedDistrict
      );
      const uniqueLocations = [
        ...new Set(
          filteredBranches.map((branch: any) => branch.Filter_Location)
        ),
      ];
      setLocations(uniqueLocations as string[]);
      setSelectedLocation("");
    }
  }, [selectedDistrict, selectedState, data]);

  const handleSearch = () => {
    const filters = {
      state: selectedState,
      district: selectedDistrict,
      location: selectedLocation,
      service: selectedService,
    };

    if (onFilter) {
      onFilter(filters);
    }
  };

  const handleClearFilters = () => {
    setSelectedState("");
    setSelectedDistrict("");
    setSelectedLocation("");
    setSelectedService("");

    // Call onFilter with empty filters to reset the results
    if (onFilter) {
      onFilter({
        state: "",
        district: "",
        location: "",
        service: "",
      });
    }
  };

  const toggleDropdown = (dropdown: string) => {
    setOpenDropdown(openDropdown === dropdown ? null : dropdown);
  };

  return (
    <div className="find_branchwrapper mt-[41px] flex justify-between gap-[15px] bg-[#ffffff33] rounded-[14px] py-[17px] px-[20px] max-lg:flex-col relative z-[9]">
      <div className="selectbox_wrapper flex justify-start items-center gap-[15px] max-w-[calc(100%-219px)] w-full max-lg:max-w-full max-md:flex-wrap">
        <div className="select_outer max-w-[calc(100%/4)] w-full max-md:max-w-[calc(100%/2-8px)] max-sm:max-w-full">
          <div ref={stateDropdownRef} className="relative w-full">
            <button
              type="button"
              onClick={() => toggleDropdown("state")}
              className="flex items-center justify-between w-full bg-white rounded-[12px] border border-[#B7DFF4] py-[22px] px-[16px] h-[58px] text-[14px] font-normal text-[#484848] max-md:h-[49px]"
            >
              <span
                className={selectedState ? "text-[#141414]" : "text-[#484848]"}
              >
                {selectedState || "State or union territory"}
              </span>
              <ChevronDown className="w-4 h-4 text-[#484848]" />
            </button>

            {openDropdown === "state" && (
              <div className="absolute z-10 w-full mt-1 bg-white border border-[#B7DFF4] rounded-[8px] shadow-lg max-h-[240px] overflow-y-auto custom-scrollbar">
                {states.length > 0 ? (
                  states.map((state) => (
                    <div
                      key={state}
                      className="p-[12px] hover:bg-[#F3F9FD] cursor-pointer"
                      onClick={() => {
                        setSelectedState(state);
                        setOpenDropdown(null);
                      }}
                    >
                      {state}
                    </div>
                  ))
                ) : (
                  <div className="p-[12px] text-[#6C727F]">
                    No states available
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        <div className="select_outer max-w-[calc(100%/4)] w-full max-md:max-w-[calc(100%/2-8px)] max-sm:max-w-full">
          <div ref={districtDropdownRef} className="relative w-full">
            <button
              type="button"
              onClick={() => selectedState && toggleDropdown("district")}
              className={`flex items-center justify-between w-full bg-white rounded-[12px] border border-[#B7DFF4] py-[22px] px-[16px] h-[58px] text-[14px] font-normal text-[#484848] max-md:h-[49px] ${
                !selectedState
                  ? "opacity-75 cursor-not-allowed"
                  : "cursor-pointer"
              }`}
              disabled={!selectedState}
            >
              <span
                className={
                  selectedDistrict ? "text-[#141414]" : "text-[#484848]"
                }
              >
                {selectedDistrict || "District"}
              </span>
              <ChevronDown className="w-4 h-4 text-[#484848]" />
            </button>

            {openDropdown === "district" && (
              <div className="absolute z-10 w-full mt-1 bg-white border border-[#B7DFF4] rounded-[8px] shadow-lg max-h-[240px] overflow-y-auto custom-scrollbar">
                {districts.length > 0 ? (
                  districts.map((district) => (
                    <div
                      key={district}
                      className="p-[12px] hover:bg-[#F3F9FD] cursor-pointer"
                      onClick={() => {
                        setSelectedDistrict(district);
                        setOpenDropdown(null);
                      }}
                    >
                      {district}
                    </div>
                  ))
                ) : (
                  <div className="p-[12px] text-[#6C727F]">
                    No districts available
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        <div className="select_outer max-w-[calc(100%/4)] w-full max-md:max-w-[calc(100%/2-8px)] max-sm:max-w-full">
          <div ref={locationDropdownRef} className="relative w-full">
            <button
              type="button"
              onClick={() => selectedDistrict && toggleDropdown("location")}
              className={`flex items-center justify-between w-full bg-white rounded-[12px] border border-[#B7DFF4] py-[22px] px-[16px] h-[58px] text-[14px] font-normal text-[#484848] max-md:h-[49px] ${
                !selectedDistrict
                  ? "opacity-75 cursor-not-allowed"
                  : "cursor-pointer"
              }`}
              disabled={!selectedDistrict}
            >
              <span
                className={
                  selectedLocation ? "text-[#141414]" : "text-[#484848]"
                }
              >
                {selectedLocation || "Location"}
              </span>
              <ChevronDown className="w-4 h-4 text-[#484848]" />
            </button>

            {openDropdown === "location" && (
              <div className="absolute z-10 w-full mt-1 bg-white border border-[#B7DFF4] rounded-[8px] shadow-lg max-h-[240px] overflow-y-auto custom-scrollbar">
                {locations.length > 0 ? (
                  locations.map((location) => (
                    <div
                      key={location}
                      className="p-[12px] hover:bg-[#F3F9FD] cursor-pointer"
                      onClick={() => {
                        setSelectedLocation(location);
                        setOpenDropdown(null);
                      }}
                    >
                      {location}
                    </div>
                  ))
                ) : (
                  <div className="p-[12px] text-[#6C727F]">
                    No locations available
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        <div className="select_outer max-w-[calc(100%/4)] w-full max-md:max-w-[calc(100%/2-8px)] max-sm:max-w-full">
          <div ref={serviceDropdownRef} className="relative w-full">
            <button
              type="button"
              onClick={() => toggleDropdown("service")}
              className="flex items-center justify-between w-full bg-white rounded-[12px] border border-[#B7DFF4] py-[22px] px-[16px] h-[58px] text-[14px] font-normal text-[#484848] max-md:h-[49px]"
            >
              <span
                className={
                  selectedService ? "text-[#141414]" : "text-[#484848]"
                }
              >
                {selectedService || "Service"}
              </span>
              <ChevronDown className="w-4 h-4 text-[#484848]" />
            </button>

            {openDropdown === "service" && (
              <div className="absolute z-10 w-full mt-1 bg-white border border-[#B7DFF4] rounded-[8px] shadow-lg max-h-[240px] overflow-y-auto custom-scrollbar">
                {serviceData && serviceData.length > 0 ? (
                  serviceData.map((service: any, index: number) => (
                    <div
                      key={index}
                      className="p-[12px] hover:bg-[#F3F9FD] cursor-pointer"
                      onClick={() => {
                        setSelectedService(service.Title.toLowerCase());
                        setOpenDropdown(null);
                      }}
                    >
                      {service.Title}
                    </div>
                  ))
                ) : (
                  <div className="p-[12px] text-[#6C727F]">
                    No services available
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="btn_wrapper max-lg:max-w-full max-lg:w-full max-lg:flex max-lg:justify-center max-sm:justify-end">
        <Button1
          onClick={handleSearch}
          name="Search"
          className="px-[56px] py-[17px] max-lg:max-w-[250px] max-lg:w-full max-md:max-w-[200px] font-medium"
          icon={false}
        />
      </div>

      {isAnyFilterSelected && (
        <div
          onClick={handleClearFilters}
          className="absolute right-5 bottom-[-28px] text-center text-[#000000] text-[14px] font-medium cursor-pointer hover:text-[#000000] hover:underline transition-colors duration-200 flex items-center justify-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4 mr-1"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
          Clear Filters
        </div>
      )}
    </div>
  );
}
