import React, { useState } from "react";
import Link from "next/link";
import Image from "next/image";

interface InvesterTabsProps {
  investorData: any[];
}

export default function InvestorsTabs({ investorData }: any) {
  const [activeTab, setActiveTab] = useState(investorData[0]?.id || 0);

  return (
    <div className="poilcy_tabwrapper flex justify-start items-start gap-[50px]">
      <div className="policy_hoveredtabs bg-[#058DD2] px-[15px] max-w-[233px] w-full">
        <ul>
          {investorData.map((policy: any) => (
            <li
              key={policy.id}
              className={`border-b border-[#32ADE5] py-[25px] cursor-pointer ${
                activeTab === policy.id ? "active_tab1" : ""
              }`}
              onMouseEnter={() => setActiveTab(policy.id)}
            >
              <h4 className="text-[16px] font-semibold text-white">
                {policy.Title}
              </h4>
              <p className="text-[14px] font-normal mt-[6px] text-white">
                {policy.Description || ""}
              </p>
            </li>
          ))}
        </ul>
      </div>
      {investorData.map((policy: any) => (
        <div
          key={policy.id}
          className={`megamenu_items pt-[60px] max-w-[700px] w-full pb-[105px] ${
            activeTab === policy.id ? "block active_tab1" : "hidden"
          }`}
        >
          <ul className="flex justify-start items-start gap-x-[35px] gap-y-[22px] max-xl:gap-x-[15px] flex-wrap max-h-[370px] overflow-y-auto pr-[10px]">
            {policy.SubMenu?.map((item: any, idx: any) => (
              <li
                key={idx}
                className="bg-[#D8EFFB] border border-[#ADDBF3] rounded-[10px] w-[calc(100%/2-18px)] hover:bg-[#bbe7ff] max-xl:w-[calc(100%/2-8px)]"
              >
                <Link
                  className="flex justify-start items-center gap-[12px] py-[15px] px-[12px] max-xl:flex-col max-xl:items-start"
                  href={item.Link || "#"}
                >
                  <Image
                    className="max-w-[41px] w-full object-contain"
                    src={
                      item.Icon?.url
                        ? `${process.env.NEXT_PUBLIC_API_BASE_URL}${item.Icon.url}`
                        : "/about_company.svg"
                    }
                    alt={item.Icon?.name || "icon"}
                    width={41}
                    height={41}
                  />
                  <div className="title">
                    <h6 className="text-[16px] font-semibold text-[#000000] line-clamp-1">
                      {item.Title}
                    </h6>
                  </div>
                </Link>
              </li>
            ))}
          </ul>
        </div>
      ))}
    </div>
  );
}
