"use client";
import React, { useState } from "react";
import StockSwiper from "./StockSwiper";
import LanguageSelector from "../common/LanguageSelector";
import Image from "next/image";
import Link from "next/link";

export default function Miniheader({ data }: any) {
  const [isLanguageOpen, setIsLanguageOpen] = useState(false);
  const [isCurrencyOpen, setIsCurrencyOpen] = useState(false);
  const toggleLanguageDropdown = (isOpen: boolean) => {
    setIsLanguageOpen(isOpen);
    if (isOpen) setIsCurrencyOpen(false);
  };
  return (
    <div className="mini_header bg-[#1C1C1C] py-[0px]">
      <div className="container flex justify-between items-center gap-[40px] max-sm:flex-col max-sm:items-start max-sm:gap-[8px] max-sm:pb-[10px] max-sm:!pt-[10px]">
        <div className="stocks max-w-[743px] w-[100%] max-lg:max-w-[360px] max-sm:max-w-full">
          {/* <StockSwiper /> */}
          {/* <div className="bse_stocks flex justify-start itme-center relative">
            <div className="dot w-[10px] h-[10px] bg-white rounded-[50%] absolute top-1/2 -translate-y-[50%]  left-[-15px]"></div>
            <h6 className="text-[12px] font-normal text-white pr-[6px] ">
              <span className="font-semibold">BSE</span> Stock Rate:{" "}
              <span className="font-semibold">₹283</span>
            </h6>
            <h6 className="pl-[6px] border-l border-white text-[12px] font-normal text-white">
              Stock Percentage :<span className="font-semibold "> ₹14,</span>
            </h6>
            <h6 className="text-[12px] font-bold text-[#FE5656] pl-[10px] gap-[4px] flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="14"
                height="15"
                viewBox="0 0 14 15"
                fill="none"
              >
                <path
                  d="M12 10.5L7.68182 5.75L5.40909 8.25L2 4.5"
                  stroke="#FE5656"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M9 10.5H12V7.5"
                  stroke="#FE5656"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
              19%
            </h6>
          </div> */}{" "}
          <div className="gold_loanbooking flex justify-start items-center gap-[15px] max-sm:justify-start max-sm:gap-[8px] ">
            <div className="image_wrapper max-w-[80px] w-full mt-[3px] max-sm:max-w-[90px] ">
              <Image
                className="w-full object-contain"
                src={
                  data?.Icon?.url
                    ? `${process.env.NEXT_PUBLIC_API_BASE_URL}${data.Icon.url}`
                    : ""
                }
                alt={data?.Icon?.alternativeText || "icon"}
                width={97}
                height={22}
              />
            </div>
            <div className="content flex justify-start items-center gap-[12px] max-sm:gap-[6px]">
              <h6 className="text-[12px] font-[400] text-[#FFFFFF]">
                {data?.Text}
              </h6>
              <a
                href={`tel:${data?.Phone}`}
                className="text-[15px] font-[600] text-[#FECB05] hover:underline max-sm:text-[12px]"
              >
                {data?.Phone}
              </a>
            </div>
          </div>
        </div>

        <div className="language_switcher max-sm:w-full max-sm:justify-end  ">
          <LanguageSelector
            isOpen={isLanguageOpen}
            setIsOpen={toggleLanguageDropdown}
          />{" "}
        </div>
      </div>
    </div>
  );
}
