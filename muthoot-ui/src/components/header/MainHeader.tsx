"use client";

import React, { useEffect, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import Searchbar from "../ui/Searchbar";
import MobileNavigation from "../ui/Mobilemenu";
import PolicyTabs from "./PolicyTabs";
import InvesterTabs from "./InvestorsTabs";
import InvestorsTabs from "./InvestorsTabs";
import SearchBar1 from "../ui/SearchBar1";
import { useRouter, usePathname } from "next/navigation";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

export default function MainHeader({ data }: any) {
  const [isScrolled, setIsScrolled] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const router = useRouter();
  const pathname = usePathname();

  const handleSearchOpens = () => {};

  // Close dropdown when route changes
  useEffect(() => {
    setActiveDropdown(null);
  }, [pathname]);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  // Handle dropdown toggle
  const handleDropdownToggle = (id: string) => {
    setActiveDropdown(id);
  };

  // Handle dropdown close
  const handleDropdownClose = () => {
    setActiveDropdown(null);
  };

  // Handle mouse leave for menu items
  const handleMenuItemMouseLeave = (event: React.MouseEvent) => {
    const relatedTarget = event.relatedTarget as HTMLElement;
    // Only close if not moving to the dropdown
    if (!relatedTarget?.closest(".about_megamenu")) {
      setActiveDropdown(null);
    }
  };

  // Add this function to handle link clicks
  const handleLinkClick = (e: React.MouseEvent, href: string) => {
    // If clicking a link to the current page, prevent default and close dropdown
    if (href === pathname) {
      e.preventDefault();
      setActiveDropdown(null);
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (!target.closest(".header_wrapper")) {
        setActiveDropdown(null);
      }
    };

    const handleScroll = () => {
      setActiveDropdown(null);
    };

    window.addEventListener("scroll", handleScroll);
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      window.removeEventListener("scroll", handleScroll);
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <header
      className={`sticky top-[-1px] z-[80] bg-[rgba(255,255,255,0.9)] transition-shadow duration-300 max-lg:py-[12px] ${
        isScrolled
          ? "shadow-lg backdrop-blur-md scrolled_header"
          : "shadow-none"
      }`}
    >
      <div className="container">
        <div className="header_wrapper flex justify-between items-center gap-[20px] max-xl:gap-[5px]">
          <Link
            href={data?.Logo?.Link || "/"}
            className="logo max-w-[83px] w-full"
          >
            <Image
              className="object-contain w-full "
              src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${
                data?.Logo?.Logo?.url || ""
              }`}
              alt={data?.Logo?.Logo?.alternativeText || "logo"}
              width={1000}
              height={1000}
            />
          </Link>

          <div className="menu_wrapper flex justify-start items-center gap-[20px] max-xl:gap-[12px] max-lg:hidden max-w-[calc(100%-100px)] w-full justify-end">
            <ul className="flex justify-start items-center gap-[20px] max-xl:gap-[10px]">
              {data?.Header &&
                data?.Header.sort(
                  (a: any, b: any) => a.Menu_Order - b.Menu_Order
                ).map((item: any, index: any) => (
                  <li
                    key={index}
                    className="group py-[35px]"
                    onMouseLeave={handleMenuItemMouseLeave}
                  >
                    <Link
                      className="text-[14px] menu_item font-semibold text-[#141414] group-hover:text-[#008BD2] flex justify-start items-center gap-[6px] max-xl:text-[13px]"
                      href={item.Link || ""}
                      onClick={(e) => handleLinkClick(e, item.Link || "")}
                      onMouseEnter={() => {
                        if (
                          item.Type === "SubMenuType2" ||
                          item.Type === "SubMenu"
                        ) {
                          handleDropdownToggle(`menu-${index}`);
                        } else {
                          // Close dropdown when hovering over non-dropdown menu items
                          handleDropdownClose();
                        }
                      }}
                    >
                      {item.Label}
                      {(item.Type === "SubMenuType2" ||
                        item.Type === "SubMenu") && (
                        <svg
                          width="10"
                          height="6"
                          viewBox="0 0 10 6"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path d="M1 1L5 5L9 1" stroke="#2193D1" />
                        </svg>
                      )}
                    </Link>

                    {item.Type === "SubMenuType2" && (
                      <div
                        className={`about_megamenu fixed z-[96] ${
                          activeDropdown === `menu-${index}`
                            ? "block"
                            : "hidden"
                        } top-[125px] left-0 bg-[#0059a3] w-full transition-opacity duration-300`}
                        onMouseLeave={handleDropdownClose}
                      >
                        <div className="containers">
                          <div className="wrapper flex justify-start items-start gap-[200px] max-2xl:gap-[100px] max-xl:gap-[30px]">
                            <div className="policy_mainwrapper">
                              <InvestorsTabs
                                investorData={item.SubMenu_Type_2 || []}
                              />
                            </div>
                            <div className="srk_image max-w-[322px] w-full h-[400px] mt-[15px] self-stretch flex items-end">
                              <Image
                                className="max-w-[322px] h-full w-full object-contain"
                                src={
                                  item.SubMenu_Type_2_Srk_Image?.url
                                    ? `${process.env.NEXT_PUBLIC_API_BASE_URL}${item.SubMenu_Type_2_Srk_Image.url}`
                                    : "/srk_menubanner.png"
                                }
                                alt="banner"
                                width={1000}
                                height={1000}
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {item.Type === "SubMenu" && (
                      <div
                        className={`about_megamenu fixed ${
                          activeDropdown === `menu-${index}`
                            ? "block"
                            : "hidden"
                        } z-[96] top-[125px] left-0 bg-[#0059a3] w-full transition-opacity duration-300`}
                        onMouseLeave={handleDropdownClose}
                      >
                        <div className="container">
                          <div className="wrapper flex justify-center items-center gap-[30px]">
                            <div className="megamenu_items pt-[60px] max-w-[700px] w-full pb-[105px]">
                              <h3 className="text-[22px] font-semibold text-[white]">
                                {item.Sub_Menu?.SubMenu_Title || ""}
                              </h3>
                              <ul className="flex justify-start items-start gap-x-[35px] gap-y-[22px] mt-[30px] flex-wrap">
                                {item.Sub_Menu?.Submenu_Navigators?.map(
                                  (subItem: any, subIdx: any) => (
                                    <li
                                      key={subIdx}
                                      className=" bg-[#D8EFFB] border border-[#ADDBF3] rounded-[10px] w-[calc(100%/2-18px)] hover:bg-[#bbe7ff]"
                                    >
                                      <Link
                                        className="flex justify-start items-center gap-[12px] py-[15px] px-[12px]"
                                        href={subItem.Link || "#"}
                                        onClick={(e) =>
                                          handleLinkClick(
                                            e,
                                            subItem.Link || "#"
                                          )
                                        }
                                      >
                                        <Image
                                          className=" max-w-[41px]  w-full  object-contain"
                                          src={
                                            subItem.Icon?.url
                                              ? `${process.env.NEXT_PUBLIC_API_BASE_URL}${subItem.Icon.url}`
                                              : "/about_company.svg"
                                          }
                                          alt={subItem.Icon?.name || "icon"}
                                          width={41}
                                          height={41}
                                        />
                                        <div className="title">
                                          <h6 className="text-[16px] font-semibold text-[#000000]">
                                            {subItem.Label}
                                          </h6>
                                          {subItem.Description && (
                                            <p className="text-[#484848] text-[14px] font-normal mt-[6px]">
                                              {subItem.Description}
                                            </p>
                                          )}
                                        </div>
                                      </Link>
                                    </li>
                                  )
                                )}
                              </ul>
                            </div>
                            <div className="srk_image max-w-[322px] w-full h-[400px] mt-[15px] self-stretch flex items-end">
                              <Image
                                className="max-w-[322px] w-full h-full object-contain"
                                src={
                                  item.Sub_Menu?.SRK_Image?.url
                                    ? `${process.env.NEXT_PUBLIC_API_BASE_URL}${item.Sub_Menu.SRK_Image.url}`
                                    : "/srk_menubanner.png"
                                }
                                alt="banner"
                                width={1000}
                                height={1000}
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </li>
                ))}
            </ul>
            <div className="search_wrapper flex justify-start items-center gap-[26px] max-xl:gap-[12px]">
              <div className="search_bar">
                <SearchBar1
                  showSheetTrigger={true}
                  handleSearchOpens={handleSearchOpens}
                />{" "}
                {/* <Searchbar
                  showSheetTrigger={true}
                  handleSearchOpens={handleSearchOpens}
                />{" "} */}
              </div>
              <div className="scanner cursor-pointer flex justify-start items-center gap-[10px] text-[14px] font-bold text-[#008BD2]   max-xl:text-[12px] max-xl:px-[6px] max-w-[calc(100%-46px)] w-full max-xl:max-w-full">
                <div>
                  {data?.Download_Button?.Label && (
                    <Link
                      href={data?.Download_Button?.PlayStoreLink}
                      target="_blank"
                      className="flex justify-start items-center gap-[10px]"
                    >
                      {data?.Download_Button?.Label || "Download"}
                    </Link>
                  )}
                </div>

                <Dialog>
                  <DialogTrigger>
                    {" "}
                    <div className="scanner_image w-[51px] h-[51px] bg-[#008BD2] rounded-full flex justify-center items-center max-xl:w-[41px] max-xl:h-[41px]">
                      <Image
                        className="rounded-[10px max-w-[25px] w-full  object-contain"
                        src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${data?.Download_Button?.QR_Image?.url}`}
                        alt="scanner"
                        width={30}
                        height={30}
                      />
                    </div>
                  </DialogTrigger>
                  <DialogContent className="max-w-[350px] w-full max-sm:max-w-[250px] max-sm:rounded-[10px] max-sm:p-[15px]">
                    <DialogHeader>
                      <DialogDescription>
                        <div className="qr_scanner max-w-[350px] w-full mx-auto max-sm:max-w-[250px]">
                          <Image
                            className="rounded-[10px  w-full  object-cover"
                            src="/scanner_qr.png"
                            alt="scanner"
                            width={400}
                            height={400}
                          />
                        </div>
                      </DialogDescription>
                    </DialogHeader>
                  </DialogContent>
                </Dialog>
              </div>
            </div>
          </div>

          <div className="toggle_wrapper hidden max-lg:block">
            <MobileNavigation datas={data} />
          </div>
        </div>
      </div>
      <SearchBar1
        showSheetTrigger={false}
        handleSearchOpens={handleSearchOpens}
      />
    </header>
  );
}
