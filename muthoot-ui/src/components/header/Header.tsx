"use client";

import React, { useEffect, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import Searchbar from "../ui/Searchbar";
import MobileNavigation from "../ui/Mobilemenu";
import PolicyTabs from "./PolicyTabs";
import InvesterTabs from "./InvestorsTabs";
import InvestorTab from "./InvestorTab";

export default function Header() {
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return (
    <header
      className={`sticky top-[-1px] z-[80] bg-[rgba(255,255,255,0.9)] transition-shadow duration-300 max-lg:py-[12px] ${
        isScrolled
          ? "shadow-lg backdrop-blur-md scrolled_header"
          : "shadow-none"
      }`}
    >
      <div className="container">
        <div className="header_wrapper flex justify-between items-center gap-[20px] max-xl:gap-[5px]">
          <Link href="" className="logo max-w-[75px] w-full">
            <Image
              className="object-contain w-full "
              src="/logo.svg"
              alt="logo"
              width={1000}
              height={1000}
            />
          </Link>

          <div className="menu_wrapper flex justify-start items-center gap-[25px] max-xl:gap-[12px] max-lg:hidden">
            <ul className="flex justify-start items-center gap-[25px] max-xl:gap-[10px]">
              <li className="group py-[30px]">
                <Link
                  className="text-[14px] menu_item font-semibold text-[#141414] group-hover:text-[#008BD2] flex justify-start items-center gap-[6px] max-xl:text-[13px] "
                  href="/about"
                >
                  About Us{" "}
                  <svg
                    width="10"
                    height="6"
                    viewBox="0 0 10 6"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M5.67435 5.26323L9.71791 1.86938C9.80729 1.79497 9.87824 1.70644 9.92666 1.6089C9.97507 1.51136 10 1.40674 10 1.30107C10 1.1954 9.97507 1.09078 9.92666 0.993242C9.87824 0.895701 9.80729 0.807172 9.71791 0.732761C9.53922 0.583679 9.29752 0.5 9.04557 0.5C8.79362 0.5 8.55191 0.583679 8.37323 0.732761L4.99724 3.56631L1.62125 0.732761C1.44257 0.583679 1.20086 0.5 0.948916 0.5C0.696971 0.5 0.455261 0.583678 0.27658 0.732761C0.188192 0.807553 0.118264 0.896255 0.0708055 0.993778C0.0233469 1.0913 -0.000709564 1.19573 1.61774e-05 1.30107C-0.000709573 1.40641 0.0233469 1.51084 0.0708055 1.60836C0.118264 1.70589 0.188192 1.79459 0.27658 1.86938L4.32014 5.26323C4.40879 5.33826 4.51427 5.3978 4.63048 5.43844C4.7467 5.47908 4.87135 5.5 4.99724 5.5C5.12314 5.5 5.24779 5.47908 5.364 5.43844C5.48022 5.3978 5.58569 5.33826 5.67435 5.26323Z"
                      fill="#2193D1"
                    />
                  </svg>
                </Link>

                <div className="about_megamenu fixed hidden group-hover:block top-[127px] left-0 bg-[#0059a3] w-full transition-opacity duration-300">
                  <div className="container">
                    <div className="wrapper flex justify-center items-center gap-[30px]">
                      <div className="megamenu_items pt-[60px] max-w-[700px] w-full pb-[105px]">
                        <h3 className="text-[22px] font-semibold text-[white]">
                          “Success is the sum of small efforts repeated daily”
                        </h3>
                        <ul className="flex justify-start items-start gap-x-[35px] gap-y-[22px] mt-[30px] flex-wrap">
                          <li className=" bg-[#D8EFFB] border border-[#ADDBF3] rounded-[10px] w-[calc(100%/2-18px)] hover:bg-[#bbe7ff]">
                            <Link
                              className="flex justify-start items-center gap-[12px] py-[15px] px-[12px]"
                              href=""
                            >
                              {" "}
                              <Image
                                className=" max-w-[41px]  w-full  object-contain"
                                src="/about_company.svg"
                                alt="icon"
                                width={41}
                                height={41}
                              />{" "}
                              <h6 className="text-[16px] font-semibold text-[#000000]">
                                About Company
                              </h6>{" "}
                            </Link>
                          </li>
                          <li className=" bg-[#D8EFFB] border border-[#ADDBF3] rounded-[10px] w-[calc(100%/2-18px)] hover:bg-[#bbe7ff]">
                            <Link
                              className="flex justify-start items-center gap-[12px] py-[15px] px-[12px]"
                              href=""
                            >
                              {" "}
                              <Image
                                className=" max-w-[41px]  w-full  object-contain"
                                src="/about_company.svg"
                                alt="icon"
                                width={41}
                                height={41}
                              />{" "}
                              <h6 className="text-[16px] font-semibold text-[#000000]">
                                About Company
                              </h6>{" "}
                            </Link>
                          </li>
                          <li className=" bg-[#D8EFFB] border border-[#ADDBF3] rounded-[10px] w-[calc(100%/2-18px)] hover:bg-[#bbe7ff]">
                            <Link
                              className="flex justify-start items-center gap-[12px] py-[15px] px-[12px]"
                              href=""
                            >
                              {" "}
                              <Image
                                className=" max-w-[41px]  w-full  object-contain"
                                src="/about_company.svg"
                                alt="icon"
                                width={41}
                                height={41}
                              />{" "}
                              <h6 className="text-[16px] font-semibold text-[#000000]">
                                About Company
                              </h6>{" "}
                            </Link>
                          </li>
                          <li className=" bg-[#D8EFFB] border border-[#ADDBF3] rounded-[10px] w-[calc(100%/2-18px)] hover:bg-[#bbe7ff]">
                            <Link
                              className="flex justify-start items-center gap-[12px] py-[15px] px-[12px]"
                              href=""
                            >
                              {" "}
                              <Image
                                className=" max-w-[41]  w-full  object-contain"
                                src="/about_company.svg"
                                alt="icon"
                                width={41}
                                height={41}
                              />{" "}
                              <h6 className="text-[16px] font-semibold text-[#000000]">
                                About Company
                              </h6>{" "}
                            </Link>
                          </li>
                          <li className=" bg-[#D8EFFB] border border-[#ADDBF3] rounded-[10px] w-[calc(100%/2-18px)] hover:bg-[#bbe7ff]">
                            <Link
                              className="flex justify-start items-center gap-[12px] py-[15px] px-[12px]"
                              href=""
                            >
                              {" "}
                              <Image
                                className=" max-w-[41]  w-full  object-contain"
                                src="/about_company.svg"
                                alt="icon"
                                width={41}
                                height={41}
                              />{" "}
                              <h6 className="text-[16px] font-semibold text-[#000000]">
                                About Company
                              </h6>{" "}
                            </Link>
                          </li>
                          <li className=" bg-[#D8EFFB] border border-[#ADDBF3] rounded-[10px] w-[calc(100%/2-18px)] hover:bg-[#bbe7ff]">
                            <Link
                              className="flex justify-start items-center gap-[12px] py-[15px] px-[12px]"
                              href=""
                            >
                              {" "}
                              <Image
                                className=" max-w-[41]  w-full  object-contain"
                                src="/about_company.svg"
                                alt="icon"
                                width={41}
                                height={41}
                              />{" "}
                              <h6 className="text-[16px] font-semibold text-[#000000]">
                                About Company
                              </h6>{" "}
                            </Link>
                          </li>
                        </ul>
                      </div>
                      <div className="srk_image max-w-[322px] w-full self-stretch flex items-end">
                        <Image
                          className=" max-w-[322px]  w-full  object-contain"
                          src="/srk_menubanner.png"
                          alt="banner"
                          width={324}
                          height={452}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </li>

              <li className="group py-[30px]">
                <Link
                  className="text-[14px] menu_item font-semibold text-[#141414] group-hover:text-[#008BD2] flex justify-start items-center gap-[6px] max-xl:text-[13px]"
                  href=""
                >
                  Products{" "}
                  <svg
                    width="10"
                    height="6"
                    viewBox="0 0 10 6"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M5.67435 5.26323L9.71791 1.86938C9.80729 1.79497 9.87824 1.70644 9.92666 1.6089C9.97507 1.51136 10 1.40674 10 1.30107C10 1.1954 9.97507 1.09078 9.92666 0.993242C9.87824 0.895701 9.80729 0.807172 9.71791 0.732761C9.53922 0.583679 9.29752 0.5 9.04557 0.5C8.79362 0.5 8.55191 0.583679 8.37323 0.732761L4.99724 3.56631L1.62125 0.732761C1.44257 0.583679 1.20086 0.5 0.948916 0.5C0.696971 0.5 0.455261 0.583678 0.27658 0.732761C0.188192 0.807553 0.118264 0.896255 0.0708055 0.993778C0.0233469 1.0913 -0.000709564 1.19573 1.61774e-05 1.30107C-0.000709573 1.40641 0.0233469 1.51084 0.0708055 1.60836C0.118264 1.70589 0.188192 1.79459 0.27658 1.86938L4.32014 5.26323C4.40879 5.33826 4.51427 5.3978 4.63048 5.43844C4.7467 5.47908 4.87135 5.5 4.99724 5.5C5.12314 5.5 5.24779 5.47908 5.364 5.43844C5.48022 5.3978 5.58569 5.33826 5.67435 5.26323Z"
                      fill="#2193D1"
                    />
                  </svg>
                </Link>
                <div className="about_megamenu fixed hidden group-hover:block top-[127px] left-0 bg-[#0059a3] w-full transition-opacity duration-300">
                  <div className="container">
                    <div className="wrapper flex justify-center items-center gap-[30px]">
                      <div className="megamenu_items pt-[60px] max-w-[700px] w-full pb-[105px]">
                        <h3 className="text-[22px] font-semibold text-[white]">
                          “Success is the sum of small efforts repeated daily”
                        </h3>
                        <ul className="flex justify-start items-start gap-x-[35px] gap-y-[22px] mt-[30px] flex-wrap">
                          <li className=" bg-[#D8EFFB] border border-[#ADDBF3] rounded-[10px] w-[calc(100%/2-18px)] hover:bg-[#bbe7ff]">
                            <Link
                              className="flex justify-start items-center gap-[12px] py-[15px] px-[12px]"
                              href=""
                            >
                              {" "}
                              <Image
                                className=" max-w-[41]  w-full  object-contain"
                                src="/about_company.svg"
                                alt="icon"
                                width={41}
                                height={41}
                              />{" "}
                              <div className="title">
                                <h6 className="text-[16px] font-semibold text-[#000000]">
                                  Two Wheeler Loan
                                </h6>{" "}
                                <p className="text-[#484848] text-[14px] font-normal mt-[6px]">
                                  With upto 100% financing offers
                                </p>
                              </div>
                            </Link>
                          </li>
                          <li className=" bg-[#D8EFFB] border border-[#ADDBF3] rounded-[10px] w-[calc(100%/2-18px)] hover:bg-[#bbe7ff]">
                            <Link
                              className="flex justify-start items-center gap-[12px] py-[15px] px-[12px]"
                              href=""
                            >
                              {" "}
                              <Image
                                className=" max-w-[41]  w-full  object-contain"
                                src="/about_company.svg"
                                alt="icon"
                                width={41}
                                height={41}
                              />{" "}
                              <div className="title">
                                <h6 className="text-[16px] font-semibold text-[#000000]">
                                  Two Wheeler Loan
                                </h6>{" "}
                                <p className="text-[#484848] text-[14px] font-normal mt-[6px]">
                                  With upto 100% financing offers
                                </p>
                              </div>
                            </Link>
                          </li>
                          <li className=" bg-[#D8EFFB] border border-[#ADDBF3] rounded-[10px] w-[calc(100%/2-18px)] hover:bg-[#bbe7ff]">
                            <Link
                              className="flex justify-start items-center gap-[12px] py-[15px] px-[12px]"
                              href=""
                            >
                              {" "}
                              <Image
                                className=" max-w-[41]  w-full  object-contain"
                                src="/about_company.svg"
                                alt="icon"
                                width={41}
                                height={41}
                              />{" "}
                              <div className="title">
                                <h6 className="text-[16px] font-semibold text-[#000000]">
                                  Two Wheeler Loan
                                </h6>{" "}
                                <p className="text-[#484848] text-[14px] font-normal mt-[6px]">
                                  With upto 100% financing offers
                                </p>
                              </div>
                            </Link>
                          </li>
                          <li className=" bg-[#D8EFFB] border border-[#ADDBF3] rounded-[10px] w-[calc(100%/2-18px)] hover:bg-[#bbe7ff]">
                            <Link
                              className="flex justify-start items-center gap-[12px] py-[15px] px-[12px]"
                              href=""
                            >
                              {" "}
                              <Image
                                className=" max-w-[41]  w-full  object-contain"
                                src="/about_company.svg"
                                alt="icon"
                                width={41}
                                height={41}
                              />{" "}
                              <div className="title">
                                <h6 className="text-[16px] font-semibold text-[#000000]">
                                  Two Wheeler Loan
                                </h6>{" "}
                                <p className="text-[#484848] text-[14px] font-normal mt-[6px]">
                                  With upto 100% financing offers
                                </p>
                              </div>
                            </Link>
                          </li>
                          <li className=" bg-[#D8EFFB] border border-[#ADDBF3] rounded-[10px] w-[calc(100%/2-18px)] hover:bg-[#bbe7ff]">
                            <Link
                              className="flex justify-start items-center gap-[12px] py-[15px] px-[12px]"
                              href=""
                            >
                              {" "}
                              <Image
                                className=" max-w-[41]  w-full  object-contain"
                                src="/about_company.svg"
                                alt="icon"
                                width={41}
                                height={41}
                              />{" "}
                              <div className="title">
                                <h6 className="text-[16px] font-semibold text-[#000000]">
                                  Two Wheeler Loan
                                </h6>{" "}
                                <p className="text-[#484848] text-[14px] font-normal mt-[6px]">
                                  With upto 100% financing offers
                                </p>
                              </div>
                            </Link>
                          </li>
                          <li className=" bg-[#D8EFFB] border border-[#ADDBF3] rounded-[10px] w-[calc(100%/2-18px)] hover:bg-[#bbe7ff]">
                            <Link
                              className="flex justify-start items-center gap-[12px] py-[15px] px-[12px]"
                              href=""
                            >
                              {" "}
                              <Image
                                className=" max-w-[41]  w-full  object-contain"
                                src="/about_company.svg"
                                alt="icon"
                                width={41}
                                height={41}
                              />{" "}
                              <div className="title">
                                <h6 className="text-[16px] font-semibold text-[#000000]">
                                  Two Wheeler Loan
                                </h6>{" "}
                                <p className="text-[#484848] text-[14px] font-normal mt-[6px]">
                                  With upto 100% financing offers
                                </p>
                              </div>
                            </Link>
                          </li>
                        </ul>
                      </div>
                      <div className="srk_image max-w-[322px] w-full self-stretch flex items-end">
                        <Image
                          className=" max-w-[322px]  w-full  object-contain"
                          src="/srk_menubanner.png"
                          alt="banner"
                          width={324}
                          height={452}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </li>
              <li className="group py-[30px]">
                <Link
                  className="text-[14px] menu_item font-semibold text-[#141414] group-hover:text-[#008BD2] flex justify-start items-center gap-[6px] max-xl:text-[13px]"
                  href=""
                >
                  Investors
                  <svg
                    width="10"
                    height="6"
                    viewBox="0 0 10 6"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M5.67435 5.26323L9.71791 1.86938C9.80729 1.79497 9.87824 1.70644 9.92666 1.6089C9.97507 1.51136 10 1.40674 10 1.30107C10 1.1954 9.97507 1.09078 9.92666 0.993242C9.87824 0.895701 9.80729 0.807172 9.71791 0.732761C9.53922 0.583679 9.29752 0.5 9.04557 0.5C8.79362 0.5 8.55191 0.583679 8.37323 0.732761L4.99724 3.56631L1.62125 0.732761C1.44257 0.583679 1.20086 0.5 0.948916 0.5C0.696971 0.5 0.455261 0.583678 0.27658 0.732761C0.188192 0.807553 0.118264 0.896255 0.0708055 0.993778C0.0233469 1.0913 -0.000709564 1.19573 1.61774e-05 1.30107C-0.000709573 1.40641 0.0233469 1.51084 0.0708055 1.60836C0.118264 1.70589 0.188192 1.79459 0.27658 1.86938L4.32014 5.26323C4.40879 5.33826 4.51427 5.3978 4.63048 5.43844C4.7467 5.47908 4.87135 5.5 4.99724 5.5C5.12314 5.5 5.24779 5.47908 5.364 5.43844C5.48022 5.3978 5.58569 5.33826 5.67435 5.26323Z"
                      fill="#2193D1"
                    />
                  </svg>
                </Link>

                <div className="about_megamenu fixed hidden  group-hover:block top-[110px] left-0 bg-[#0059a3] w-full transition-opacity duration-300">
                  <div className="container">
                    <div className="wrapper flex justify-center items-start gap-[30px]">
                      <div className="policy_mainwrapper">
                        <InvestorTab />
                      </div>
                      <div className="srk_image max-w-[322px] w-full self-stretch flex items-end">
                        <Image
                          className=" max-w-[322px]  w-full  object-contain"
                          src="/srk_menubanner.png"
                          alt="banner"
                          width={324}
                          height={452}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </li>
              <li className="group py-[30px]">
                <Link
                  className="text-[14px] menu_item font-semibold text-[#141414] group-hover:text-[#008BD2] flex justify-start items-center gap-[6px] max-xl:text-[13px]"
                  href="/media"
                >
                  Media
                  <svg
                    width="10"
                    height="6"
                    viewBox="0 0 10 6"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M5.67435 5.26323L9.71791 1.86938C9.80729 1.79497 9.87824 1.70644 9.92666 1.6089C9.97507 1.51136 10 1.40674 10 1.30107C10 1.1954 9.97507 1.09078 9.92666 0.993242C9.87824 0.895701 9.80729 0.807172 9.71791 0.732761C9.53922 0.583679 9.29752 0.5 9.04557 0.5C8.79362 0.5 8.55191 0.583679 8.37323 0.732761L4.99724 3.56631L1.62125 0.732761C1.44257 0.583679 1.20086 0.5 0.948916 0.5C0.696971 0.5 0.455261 0.583678 0.27658 0.732761C0.188192 0.807553 0.118264 0.896255 0.0708055 0.993778C0.0233469 1.0913 -0.000709564 1.19573 1.61774e-05 1.30107C-0.000709573 1.40641 0.0233469 1.51084 0.0708055 1.60836C0.118264 1.70589 0.188192 1.79459 0.27658 1.86938L4.32014 5.26323C4.40879 5.33826 4.51427 5.3978 4.63048 5.43844C4.7467 5.47908 4.87135 5.5 4.99724 5.5C5.12314 5.5 5.24779 5.47908 5.364 5.43844C5.48022 5.3978 5.58569 5.33826 5.67435 5.26323Z"
                      fill="#2193D1"
                    />
                  </svg>
                </Link>

                <div className="about_megamenu fixed hidden group-hover:block top-[127px] left-0 bg-[#0059a3] w-full transition-opacity duration-300">
                  <div className="container">
                    <div className="wrapper flex justify-center items-center gap-[30px]">
                      <div className="megamenu_items pt-[60px] max-w-[700px] w-full pb-[105px]">
                        <h3 className="text-[22px] font-semibold text-[white]">
                          “Success is the sum of small efforts repeated daily”
                        </h3>
                        <ul className="flex justify-start items-start gap-x-[35px] gap-y-[22px] mt-[30px] flex-wrap">
                          <li className=" bg-[#D8EFFB] border border-[#ADDBF3] rounded-[10px] w-[calc(100%/2-18px)] hover:bg-[#bbe7ff]">
                            <Link
                              className="flex justify-start items-center gap-[12px] py-[15px] px-[12px]"
                              href=""
                            >
                              {" "}
                              <Image
                                className=" max-w-[41]  w-full  object-contain"
                                src="/about_company.svg"
                                alt="icon"
                                width={41}
                                height={41}
                              />{" "}
                              <div className="title">
                                <h6 className="text-[16px] font-semibold text-[#000000]">
                                  News
                                </h6>{" "}
                              </div>
                            </Link>
                          </li>
                          <li className=" bg-[#D8EFFB] border border-[#ADDBF3] rounded-[10px] w-[calc(100%/2-18px)] hover:bg-[#bbe7ff]">
                            <Link
                              className="flex justify-start items-center gap-[12px] py-[15px] px-[12px]"
                              href=""
                            >
                              {" "}
                              <Image
                                className=" max-w-[41]  w-full  object-contain"
                                src="/about_company.svg"
                                alt="icon"
                                width={41}
                                height={41}
                              />{" "}
                              <div className="title">
                                <h6 className="text-[16px] font-semibold text-[#000000]">
                                  Testimonials
                                </h6>{" "}
                              </div>
                            </Link>
                          </li>
                          <li className=" bg-[#D8EFFB] border border-[#ADDBF3] rounded-[10px] w-[calc(100%/2-18px)] hover:bg-[#bbe7ff]">
                            <Link
                              className="flex justify-start items-center gap-[12px] py-[15px] px-[12px]"
                              href=""
                            >
                              {" "}
                              <Image
                                className=" max-w-[41]  w-full  object-contain"
                                src="/about_company.svg"
                                alt="icon"
                                width={41}
                                height={41}
                              />{" "}
                              <div className="title">
                                <h6 className="text-[16px] font-semibold text-[#000000]">
                                  Awards
                                </h6>{" "}
                              </div>
                            </Link>
                          </li>
                          <li className=" bg-[#D8EFFB] border border-[#ADDBF3] rounded-[10px] w-[calc(100%/2-18px)] hover:bg-[#bbe7ff]">
                            <Link
                              className="flex justify-start items-center gap-[12px] py-[15px] px-[12px]"
                              href=""
                            >
                              {" "}
                              <Image
                                className=" max-w-[41]  w-full  object-contain"
                                src="/about_company.svg"
                                alt="icon"
                                width={41}
                                height={41}
                              />{" "}
                              <div className="title">
                                <h6 className="text-[16px] font-semibold text-[#000000]">
                                  Blogs
                                </h6>{" "}
                              </div>
                            </Link>
                          </li>
                          <li className=" bg-[#D8EFFB] border border-[#ADDBF3] rounded-[10px] w-[calc(100%/2-18px)] hover:bg-[#bbe7ff]">
                            <Link
                              className="flex justify-start items-center gap-[12px] py-[15px] px-[12px]"
                              href=""
                            >
                              {" "}
                              <Image
                                className=" max-w-[41]  w-full  object-contain"
                                src="/about_company.svg"
                                alt="icon"
                                width={41}
                                height={41}
                              />{" "}
                              <div className="title">
                                <h6 className="text-[16px] font-semibold text-[#000000]">
                                  Two Wheeler Loan
                                </h6>{" "}
                              </div>
                            </Link>
                          </li>
                          <li className=" bg-[#D8EFFB] border border-[#ADDBF3] rounded-[10px] w-[calc(100%/2-18px)] hover:bg-[#bbe7ff]">
                            <Link
                              className="flex justify-start items-center gap-[12px] py-[15px] px-[12px]"
                              href=""
                            >
                              {" "}
                              <Image
                                className=" max-w-[41]  w-full  object-contain"
                                src="/about_company.svg"
                                alt="icon"
                                width={41}
                                height={41}
                              />{" "}
                              <div className="title">
                                <h6 className="text-[16px] font-semibold text-[#000000]">
                                  Two Wheeler Loan
                                </h6>{" "}
                              </div>
                            </Link>
                          </li>
                        </ul>
                      </div>
                      <div className="srk_image max-w-[322px] w-full self-stretch flex items-end">
                        <Image
                          className=" max-w-[322px]  w-full  object-contain"
                          src="/srk_menubanner.png"
                          alt="banner"
                          width={324}
                          height={452}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </li>
              <li className="group py-[30px]">
                <Link
                  className="text-[14px] menu_item font-semibold text-[#141414] group-hover:text-[#008BD2] flex justify-start items-center gap-[6px] max-xl:text-[13px]"
                  href=""
                >
                  Policies
                  <svg
                    width="10"
                    height="6"
                    viewBox="0 0 10 6"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M5.67435 5.26323L9.71791 1.86938C9.80729 1.79497 9.87824 1.70644 9.92666 1.6089C9.97507 1.51136 10 1.40674 10 1.30107C10 1.1954 9.97507 1.09078 9.92666 0.993242C9.87824 0.895701 9.80729 0.807172 9.71791 0.732761C9.53922 0.583679 9.29752 0.5 9.04557 0.5C8.79362 0.5 8.55191 0.583679 8.37323 0.732761L4.99724 3.56631L1.62125 0.732761C1.44257 0.583679 1.20086 0.5 0.948916 0.5C0.696971 0.5 0.455261 0.583678 0.27658 0.732761C0.188192 0.807553 0.118264 0.896255 0.0708055 0.993778C0.0233469 1.0913 -0.000709564 1.19573 1.61774e-05 1.30107C-0.000709573 1.40641 0.0233469 1.51084 0.0708055 1.60836C0.118264 1.70589 0.188192 1.79459 0.27658 1.86938L4.32014 5.26323C4.40879 5.33826 4.51427 5.3978 4.63048 5.43844C4.7467 5.47908 4.87135 5.5 4.99724 5.5C5.12314 5.5 5.24779 5.47908 5.364 5.43844C5.48022 5.3978 5.58569 5.33826 5.67435 5.26323Z"
                      fill="#2193D1"
                    />
                  </svg>
                </Link>

                <div className="about_megamenu fixed hidden  group-hover:block top-[110px] left-0 bg-[#0059a3] w-full transition-opacity duration-300">
                  <div className="container">
                    <div className="wrapper flex justify-center items-start gap-[30px]">
                      <div className="policy_mainwrapper">
                        <PolicyTabs />
                      </div>
                      <div className="srk_image max-w-[322px] w-full self-stretch flex items-end">
                        <Image
                          className=" max-w-[322px]  w-full  object-contain"
                          src="/srk_menubanner.png"
                          alt="banner"
                          width={324}
                          height={452}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </li>
              <li className="group">
                <Link
                  className="text-[14px] menu_item font-semibold text-[#141414] group-hover:text-[#008BD2] flex justify-start items-center gap-[6px] max-xl:text-[13px]"
                  href="/careers"
                >
                  Career
                </Link>
              </li>
              <li className="group">
                <Link
                  className="text-[14px] menu_item font-semibold text-[#141414] group-hover:text-[#008BD2] flex justify-start items-center gap-[6px] max-xl:text-[13px]"
                  href=""
                >
                  {" "}
                  Digital initiatives
                </Link>
              </li>
              <li className="group">
                <Link
                  className="text-[14px] menu_item font-semibold text-[#141414] group-hover:text-[#008BD2] flex justify-start items-center gap-[6px] max-xl:text-[13px]"
                  href="/csr"
                >
                  CSR
                </Link>
              </li>
              <li className="group">
                <Link
                  className="text-[14px] menu_item font-semibold text-[#141414] group-hover:text-[#008BD2] flex justify-start items-center gap-[6px] max-xl:text-[13px]"
                  href="contact"
                >
                  Contact us
                </Link>
              </li>
            </ul>
            <div className="search_wrapper flex justify-start items-center gap-[26px] max-xl:gap-[12px]">
              <div className="search_bar">
                {/* <Searchbar /> */}
              </div>
              <div className="scanner cursor-pointer flex justify-start items-center gap-[10px] text-[14px] font-bold text-[#008BD2]   whitespace-nowrap max-xl:text-[12px] max-xl:px-[6px]">
                Download app{" "}
                <div className="scanner_image w-[51px] h-[51px] bg-[#008BD2] rounded-full flex justify-center items-center max-xl:w-[41px] max-xl:h-[41px]">
                  <Image
                    className="rounded-[10px max-w-[30px] w-full  object-contain"
                    src="/scanner.svg"
                    alt="scanner"
                    width={30}
                    height={30}
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="toggle_wrapper hidden max-lg:block">
            {/* <MobileNavigation /> */}
          </div>
        </div>
      </div>
    </header>
  );
}
