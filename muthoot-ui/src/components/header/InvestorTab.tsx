import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';

const investers = [
  {
    id: 1,
    title: 'Investors',
    content:
      'With a focus on ethical conduct, Muthoot Capital ensures that only the best practices are followed with utmost transparency in our service offerings.',
    links: ['Annual Report', 'Testimonials', 'Awards', 'Blogs', 'Two Wheeler Loan' , 'Two Wheeler Loan' , 'Two Wheeler Loan' , 'Two Wheeler Loan' , 'Two Wheeler Loan' , 'Two Wheeler Loan' , 'Two Wheeler Loan' , 'Two Wheeler Loan'],
  },
  {
    id: 2,
    title: 'More',
    content:
      'With a focus on ethical conduct, Muthoot Capital ensures that only the best practices are followed with utmost transparency in our service offerings.',
    links: ['Annual Reports', 'Testimonials', 'Awards', 'Blogs', 'Two Wheeler Loan'],
  },
  
  
];


const InvestorTab = () => {
    const [activeTab, setActiveTab] = useState<number>(1);
  
    return (
      <div className="poilcy_tabwrapper flex justify-start items-start gap-[50px]">
        {/* Left Tabs */}
        <div className="policy_hoveredtabs bg-[#058DD2] px-[15px] max-w-[233px] w-full">
          <ul>
            {investers.map((policy) => (
              <li
                key={policy.id}
                className={`border-b border-[#32ADE5] py-[25px] cursor-pointer ${
                  activeTab === policy.id ? 'active_tab1' : ''
                }`}
                onMouseEnter={() => setActiveTab(policy.id)}
              >
                <h4 className="text-[16px] font-semibold text-white">{policy.title}</h4>
                <p className="text-[14px] font-normal mt-[6px] text-white">
                  {policy.content}
                </p>
              </li>
            ))}
          </ul>
        </div>
  
        {/* Right MegaMenu */}
        {investers.map((policy) => (
          <div
            key={policy.id}
            className={`megamenu_items pt-[60px] max-w-[700px] w-full pb-[105px] ${
              activeTab === policy.id ? 'block active_tab1' : 'hidden'
            }`}
          >
            <ul className="flex justify-start items-start gap-x-[35px] gap-y-[22px]  flex-wrap max-h-[370px] overflow-y-auto pr-[10px]">
              {policy.links.map((linkTitle, idx) => (
                <li
                  key={idx}
                  className="bg-[#D8EFFB] border border-[#ADDBF3] rounded-[10px] w-[calc(100%/2-18px)] hover:bg-[#bbe7ff]"
                >
                  <Link
                    className="flex justify-start items-center gap-[12px] py-[15px] px-[12px]"
                    href="#"
                  >
                    <Image
                      className="max-w-[41] w-full object-contain"
                      src="/about_company.svg"
                      alt="icon"
                      width={41}
                      height={41}
                    />
                    <div className="title">
                      <h6 className="text-[16px] font-semibold text-[#000000] line-clamp-1">
                        {linkTitle}
                      </h6>
                    </div>
                  </Link>
                </li>
                
              ))}
            </ul>
          </div>
        ))}
      </div>
    );
  };
  
  export default InvestorTab;
  