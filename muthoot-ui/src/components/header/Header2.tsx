"use client";

import React, { useEffect, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import Searchbar from "../ui/Searchbar";
import MobileNavigation from "../ui/Mobilemenu";
import { usePathname } from "next/navigation";
import MenuLink from "../wrapper/MenuLink";

export default function Header2({ data }: any) {
  const { Download_Button, Header } = data;
  const [isScrolled, setIsScrolled] = useState(false);
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  const pathname = usePathname();
  const paths = [
    "/cibil/personalDetails",
    "/cibil/addressDetails",
    "/cibil/declaration",
    "/cibil/identityDetails",
  ];

  const isDynamicPath = (path: string): boolean => {
    return paths.some(
      (p) => path.startsWith(p) || path === p || path === `${p}/`
    );
  };

  const headerClassName = isDynamicPath(pathname)
    ? `${
        isScrolled ? "shadow-lg backdrop-blur-md scrolled_header" : ""
      } sticky top-0 z-[80] bg-[rgba(255,255,255,0.9)] transition-shadow duration-300 bg-[white] shadow-lg`
    : `${
        isScrolled
          ? "sticky top-0 z-[80] bg-[rgba(255,255,255,0.9)] transition-shadow duration-300 bg-[white] shadow-lg"
          : ""
      } sticky top-0 z-[80] bg-[rgba(255,255,255,0.9)] transition-shadow duration-300 bg-[white]`;

  return (
    <header className={headerClassName}>
      <div className="container">
        <div className="header_wrapper flex justify-between items-center gap-[20px] max-xl:gap-[5px] max-lg:py-[10px]">
          {/* logo  */}
          <Link href="/" className="logo max-w-[84px] w-full cursor-pointer">
            <Image
              className="  w-full  object-contain"
              src="/logo.svg"
              alt="logo"
              width={84}
              height={67}
            />
          </Link>

          <div className="menu_wrapper flex justify-start items-center gap-[20px] max-xl:gap-[10px] max-lg:hidden">
            {/* navigation  */}
            <ul className="flex justify-start items-center gap-[20px] max-xl:gap-[10px]">
              {Header &&
                Header?.map((item: any, index: number) => {
                  return (
                    <li
                      className={`${
                        item?.SubMenu ? "group" : "block menu_links"
                      } py-[30px]`}
                      key={index}
                    >
                      <MenuLink
                        className="cursor-pointer text-[14px] menu_item font-semibold text-[#141414] group-hover:text-[#008BD2] flex justify-start items-center gap-[6px] max-xl:text-[13px] "
                        href={item?.Link}
                      >
                        {item?.Label}

                        {item?.SubMenu && (
                          <svg
                            width="10"
                            height="6"
                            viewBox="0 0 10 6"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M5.67435 5.26323L9.71791 1.86938C9.80729 1.79497 9.87824 1.70644 9.92666 1.6089C9.97507 1.51136 10 1.40674 10 1.30107C10 1.1954 9.97507 1.09078 9.92666 0.993242C9.87824 0.895701 9.80729 0.807172 9.71791 0.732761C9.53922 0.583679 9.29752 0.5 9.04557 0.5C8.79362 0.5 8.55191 0.583679 8.37323 0.732761L4.99724 3.56631L1.62125 0.732761C1.44257 0.583679 1.20086 0.5 0.948916 0.5C0.696971 0.5 0.455261 0.583678 0.27658 0.732761C0.188192 0.807553 0.118264 0.896255 0.0708055 0.993778C0.0233469 1.0913 -0.000709564 1.19573 1.61774e-05 1.30107C-0.000709573 1.40641 0.0233469 1.51084 0.0708055 1.60836C0.118264 1.70589 0.188192 1.79459 0.27658 1.86938L4.32014 5.26323C4.40879 5.33826 4.51427 5.3978 4.63048 5.43844C4.7467 5.47908 4.87135 5.5 4.99724 5.5C5.12314 5.5 5.24779 5.47908 5.364 5.43844C5.48022 5.3978 5.58569 5.33826 5.67435 5.26323Z"
                              fill="#2193D1"
                            />
                          </svg>
                        )}
                      </MenuLink>
                      {item?.SubMenu &&
                      item?.SubMenu !== null &&
                      item?.SubMenu?.SubMenu_Data[0]?.Image === null ? (
                        <div className="about_megamenu fixed hidden group-hover:block top-[110px] left-0 bg-white w-full transition-opacity duration-300">
                          <div className="megamenu_itemswrapper w-full flex justify-between items-center gap-[50px] bg-white relative z-[9] shadow-top-bottom">
                            <div className="megamenu_parentwrapper w-full flex justify-between items-center gap-[50px]">
                              {/* submentData  text only */}
                              <ul
                                className={`${
                                  item?.SubMenu?.Link !== "/about"
                                    ? "flex justify-start items-start flex-wrap gap-x-[50px] gap-y-[26px] py-[40px]  pl-[50px]w-full max-xl:gap-x-[40px] pl-[50px] max-w-[calc(100%-530px)]"
                                    : "flex justify-start items-start flex-wrap gap-x-[100px] gap-y-[38px] py-[50px] pl-[50px] max-w-[570px] w-full max-xl:gap-x-[40px] max-xl:max-lg:max-w-[450px]"
                                }`}
                              >
                                {item?.SubMenu?.SubMenu_Data?.length > 0 &&
                                  item?.SubMenu?.SubMenu_Data?.map(
                                    (item: any, index: number) => (
                                      <li
                                        className={`
                                          ${
                                            item?.SubMenu?.Link !== "/about"
                                              ? "w-[calc(100%/3-34px)]"
                                              : " w-[calc(100%/2-100px)]"
                                          }
                                          
                                       max-xl:w-[calc(100%/2-40px)]`}
                                        key={index}
                                      >
                                        <Link
                                          className="text-[16px] font-bold text-black block hover:text-[#008BD2] hover:translate-x-[10px] transition-all"
                                          href={item?.Link}
                                        >
                                          {item?.Label}
                                        </Link>
                                      </li>
                                    )
                                  )}
                              </ul>

                              {/* SRK  image  */}
                              <div className="banner_wrapper flex justify-end items-end gap-[50px]">
                                <div className="logo_shade mb-[10px]">
                                  <Image
                                    src="/shade.svg"
                                    alt="shade-logo"
                                    width={227}
                                    height={183}
                                  />
                                </div>

                                <div className="megamenu_banner h-[340px]">
                                  <Image
                                    className="h-full object-cover"
                                    src="/megamenu-banner.png"
                                    alt="shade-logo"
                                    width={456}
                                    height={324}
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="about_megamenu fixed hidden group-hover:block top-[110px] left-0 bg-white w-full transition-opacity duration-300">
                          <div className="megamenu_itemswrapper w-full flex justify-between items-center gap-[50px] bg-white relative z-[9] shadow-top-bottom">
                            <div className="megamenu_parentwrapper w-full flex justify-between items-center gap-[50px]">
                              <ul
                                className={`
                                ${
                                  item?.Link !== "/media"
                                    ? "flex justify-start items-start flex-wrap gap-x-[100px] gap-y-[38px] py-[50px] pl-[50px] max-w-[800px] w-full max-xl:gap-x-[40px] max-xl:max-lg:max-w-[450px]"
                                    : "flex justify-start items-start flex-wrap gap-x-[50px] gap-y-[26px] py-[40px] ] w-full max-xl:gap-x-[40px] pl-[50px] max-w-[calc(100%-530px)]"
                                }
                              `}
                              >
                                {item?.SubMenu?.SubMenu_Data?.length > 0 &&
                                  item?.SubMenu?.SubMenu_Data?.map(
                                    (item2: any, index: number) => {
                                      if (item?.Link !== "/media") {
                                        return (
                                          <li
                                            className="w-[calc(100%/2-100px)] max-xl:w-[calc(100%/2-40px)]"
                                            key={index}
                                          >
                                            <Link
                                              className="text-[16px] font-bold text-black hover:text-[#008BD2] hover:translate-x-[10px] transition-all flex items-center gap-[12px]"
                                              href={item2?.Link}
                                            >
                                              <Image
                                                className="max-w-[30px] w-full object-contain"
                                                src={
                                                  process.env
                                                    .NEXT_PUBLIC_API_BASE_URL +
                                                  item2?.Image?.url
                                                }
                                                alt={item2?.Image?.name}
                                                width={30}
                                                height={30}
                                              />
                                              {item2?.Label}
                                            </Link>
                                          </li>
                                        );
                                      } else {
                                        return (
                                          <li key={index}>
                                            <Link href={item2?.Link}>
                                              <Image
                                                className="rounded-[10px] max-w-[148px] w-full object-cover"
                                                src={
                                                  process.env
                                                    .NEXT_PUBLIC_API_BASE_URL +
                                                  item2?.Image?.url
                                                }
                                                alt={item2?.Image?.name}
                                                width={148}
                                                height={148}
                                              />
                                              <h6 className="text-[16px] font-medium text-black mt-[6px] text-center">
                                                {item2?.Label}
                                              </h6>
                                            </Link>
                                          </li>
                                        );
                                      }
                                    }
                                  )}
                              </ul>

                              {/* side bar images  */}
                              <div className="banner_wrapper flex justify-end items-end gap-[50px]">
                                <div className="logo_shade mb-[10px]">
                                  <Image
                                    src="/shade.svg"
                                    alt="shade-logo"
                                    width={227}
                                    height={183}
                                  />
                                </div>

                                <div className="megamenu_banner h-[340px]">
                                  <Image
                                    className="h-full object-cover"
                                    src="/megamenu-banner.png"
                                    alt="shade-logo"
                                    width={456}
                                    height={324}
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </li>
                  );
                })}
            </ul>

            {/* search bar and download button */}
            <div className="search_wrapper flex justify-start items-center gap-[26px] max-xl:gap-[8px]">
              <div className="search_bar">
                {/* <Searchbar /> */}
              </div>
              {Download_Button && Download_Button?.Link && (
                <Link href={Download_Button.Link} target="_blank">
                  <div className="scanner cursor-pointer flex justify-start items-center gap-[10px] text-[14px] font-bold text-[#008BD2]   whitespace-nowrap max-xl:text-[12px] max-xl:px-[6px]">
                    {Download_Button?.Label || "Download"}
                    {Download_Button?.QR_Image?.url && (
                      <div className="scanner_image w-[51px] h-[51px] bg-[#008BD2] rounded-full flex justify-center items-center max-xl:w-[41px] max-xl:h-[41px]">
                        <Image
                          className="rounded-[10px] max-w-[30px] w-full object-contain max-xl:max-w-[25px]"
                          src={
                            Download_Button.QR_Image.url.startsWith("http")
                              ? Download_Button.QR_Image.url
                              : `${process.env.NEXT_PUBLIC_API_BASE_URL}${Download_Button.QR_Image.url}`
                          }
                          alt={Download_Button?.QR_Image?.name || "QR Code"}
                          width={30}
                          height={30}
                        />
                      </div>
                    )}
                  </div>
                </Link>
              )}
            </div>
          </div>

          {/* mobile navigation */}
          <div className="toggle_wrapper hidden max-lg:flex justify-start gap-[20px] items-center">
            <div className="search_bar">
              {/* <Searchbar /> */}
            </div>
            {/* <MobileNavigation /> */}
          </div>
        </div>
      </div>
    </header>
  );
}
