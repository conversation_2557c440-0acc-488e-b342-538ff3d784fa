"use client";

import React, { useState, useRef, useEffect } from "react";

const VideoPlayer = () => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isButtonVisible, setIsButtonVisible] = useState(true);
  const videoRef = useRef<HTMLVideoElement | null>(null);
  const playAttemptRef = useRef<boolean>(false);

  // Handle cleanup and prevent abort errors
  useEffect(() => {
    return () => {
      // Cleanup function to pause video when component unmounts
      if (videoRef.current && !videoRef.current.paused) {
        videoRef.current.pause();
      }
    };
  }, []);

  const handlePauseButtonClick = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
        setIsPlaying(false);
      } else {
        // Use a try-catch to handle potential play() errors
        playAttemptRef.current = true;
        videoRef.current.play()
          .then(() => {
            setIsPlaying(true);
            setIsButtonVisible(false);
          })
          .catch(error => {
            // Only log if component is still mounted (playAttemptRef is true)
            if (playAttemptRef.current) {
              console.error("Video play error:", error);
              setIsPlaying(false);
              setIsButtonVisible(true);
            }
          });
      }
    }
  };

  const handleVideoClick = () => {
    if (videoRef.current && isPlaying) {
      videoRef.current.pause();
      setIsPlaying(false);
      setIsButtonVisible(true);
    }
  };

  const handleVideoEnd = () => {
    setIsPlaying(false);
    setIsButtonVisible(true);
  };

  // Reset playAttemptRef when component unmounts
  useEffect(() => {
    return () => {
      playAttemptRef.current = false;
    };
  }, []);

  return (
    <div className="video_wrapper max-w-[440px] w-full relative">
      {isButtonVisible && (
        <div
          className="pause_btn absolute top-[50%] left-[50%] -translate-x-[50%] -translate-y-[50%] opacity-100 transition-opacity duration-300"
          onClick={handlePauseButtonClick}
        >
          <svg
            width="62"
            height="62"
            viewBox="0 0 62 62"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g clipPath="url(#clip0)">
              <path
                d="M60.7032 15.9806C59.9902 13.2966 57.8899 11.1808 55.2213 10.4626C50.3879 9.16064 31 9.16064 31 9.16064C31 9.16064 11.6121 9.16064 6.77608 10.4626C4.11008 11.1808 2.00983 13.294 1.29683 15.9806C0 20.8476 0 31.0001 0 31.0001C0 31.0001 0 41.1526 1.29683 46.0196C2.00983 48.7037 4.11008 50.8195 6.77867 51.5377C11.6121 52.8397 31 52.8396 31 52.8396C31 52.8396 50.3879 52.8397 55.2239 51.5377C57.8899 50.8195 59.9902 48.7063 60.7057 46.0196C62 41.1526 62 31.0001 62 31.0001C62 31.0001 62 20.8476 60.7032 15.9806ZM24.6605 40.2201V21.7802L40.8632 31.0001L24.6605 40.2201Z"
                fill="white"
              />
            </g>
            <defs>
              <clipPath id="clip0">
                <rect width="62" height="62" fill="white" />
              </clipPath>
            </defs>
          </svg>
        </div>
      )}

      <video
        ref={videoRef}
        className="w-full h-full object-cover"
        width="437"
        height="540"
        poster="/Thomas-john.png"
        onClick={handleVideoClick}
        onEnded={handleVideoEnd}
        preload="metadata"
      >
        <source src="/video.mp4" type="video/mp4" />
        Your browser does not support the video tag.
      </video>
    </div>
  );
};

export default VideoPlayer;
