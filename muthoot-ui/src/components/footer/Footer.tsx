import React from "react";
import Image from "next/image";
import Link from "next/link";
import { GetFooter } from "@/lib/api/common";

export default async function Footer() {
  const { data } = await GetFooter();
  return (
    <main>
      <footer className="bg-[#003E7D] ">
        <div className="container">
          <div className="main_footer py-[80px] border-b border-[rgba(255,255,255,0.1)] flex justify-start items-start gap-[90px] max-lg:flex-col max-lg:gap-[40px] max-sm:pt-[50px] max-sm:pb-[30px]">
            <div className="logowrapper max-w-[381px] w-full max-lg:max-w-full">
              <div className="logo max-sm:flex max-sm:justify-center">
                {data?.Logo?.url && (
                  <Image
                    className="rounded-[10px] max-w-[148px] w-full object-cover"
                    src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${data?.Logo?.url}`}
                    alt="mini banner"
                    width={171}
                    height={79}
                  />
                )}
              </div>

              {data?.Description && (
                <p className="text-[14px] font-normal mt-[24px] text-[#D8F0FC] max-sm:text-center">
                  {data?.Description}
                </p>
              )}

              <div className="nse_wrapper flex justify-start items-center gap-[17px] mt-[40px] max-sm:mt-[25px] max-sm:justify-center">
                {data?.StockLogos?.map((stockLogo: any, index: number) =>
                  stockLogo?.Logo?.map((logo: any, logoIndex: number) => (
                    <Image
                      key={`${index}-${logoIndex}`}
                      className="rounded-[13px] max-w-[148px] w-full object-cover"
                      src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${logo.url}`}
                      alt={logo.alternativeText || `logo-${logoIndex}`}
                      width={logo.width}
                      height={logo.height}
                    />
                  ))
                )}
              </div>

              <div className="disclaimer_links flex justify-start items-center flex-wrap mt-[25px] max-sm:gap-[10px] max-sm:justify-center">
                {data?.StockLinks?.length > 0 &&
                  data.StockLinks.map((link: any, idx: number) => {
                    let linkClass = "";

                    if (idx === 0) {
                      linkClass = "pr-[8px]";
                    } else if (idx === 2) {
                      linkClass =
                        "pr-[8px] border-r border-[rgba(255,255,255,0.4)]";
                    } else if (idx === data.StockLinks.length - 1) {
                      linkClass = "pl-[8px]";
                    } else {
                      linkClass =
                        "px-[8px] border-l border-r border-[rgba(255,255,255,0.4)]";
                    }

                    return (
                      <Link
                        key={link.Label} // Ideally use a unique ID
                        href={link.Link}
                        target="_blank"
                        rel="noopener noreferrer"
                        className={`text-[14px] font-normal text-white ${linkClass} max-sm:border-none max-sm:p-0`}
                      >
                        {link.Label}
                      </Link>
                    );
                  })}
              </div>

              <div className="social_mediaWrapper flex justify-start items-center gap-[15px] mt-[41px] max-sm:mt-[20px] max-sm:justify-center">
                {data?.socialMedia &&
                  data?.socialMedia?.length > 0 &&
                  data?.socialMedia?.map((media: any) => (
                    <Link
                      key={media?.id}
                      href={media?.Link}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="social_icon border border-[rgba(255,255,255,0.1)] p-[11px] flex justify-center items-center rounded-[50%] w-[35px] h-[35px] hover:bg-white group"
                    >
                      <div className="">
                        <Image
                          src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${media?.Icon?.url}`}
                          alt={
                            media?.Icon?.alternativeText || media?.Icon?.name
                          }
                          width={media?.Icon?.width || 35}
                          height={media?.Icon?.height || 35}
                          className=""
                        />
                      </div>
                    </Link>
                  ))}
              </div>
            </div>

            <div className="quick_linkwrapper max-w-[calc(100%-381px)] w-full flex justify-start items-start gap-[50px] flex-wrap max-lg:max-w-full max-sm:gap-[30px]">
              {data?.LinkSection1 &&
                data?.LinkSection1.Links &&
                data?.LinkSection1.Links.length > 0 && (
                  <div className="quick_links w-[calc(100%/3-34px)] max-sm:w-[calc(100%/2-15px)]">
                    <h3 className="text-[16px] font-bold text-white capitalize">
                      {data?.LinkSection1.Title || "Links"}
                    </h3>
                    <ul className="mt-[16px]">
                      {data?.LinkSection1.Links.map(
                        (link: any, index: number) => (
                          <li key={link.id || index} className="mt-[13px]">
                            <Link
                              className="text-[14px] font-normal text-[#D8F0FC]"
                              href={link.Link || "#"}
                            >
                              {link.Label || "Link"}
                            </Link>
                          </li>
                        )
                      )}
                    </ul>
                  </div>
                )}
              {data?.LinkSection2 && data?.LinkSection2.Links.length > 0 && (
                <div className="quick_links w-[calc(100%/3-34px)] max-sm:w-[calc(100%/2-15px)]">
                  <h3 className="text-[16px] font-bold text-white capitalize">
                    {data?.LinkSection2.Title}
                  </h3>
                  <ul className="mt-[16px]">
                    {data?.LinkSection2.Links.map((link: any) => (
                      <li key={link.id} className="mt-[13px]">
                        <Link
                          className="text-[14px] font-normal text-[#D8F0FC]"
                          href={link.Link}
                        >
                          {link.Label}
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
              {data?.LinkSection3 && data?.LinkSection3.Links.length > 0 && (
                <div className="quick_links w-[calc(100%/3-34px)] max-sm:w-[calc(100%/2-15px)]">
                  <h3 className="text-[16px] font-bold text-white capitalize">
                    {data?.LinkSection3.Title}
                  </h3>
                  <ul className="mt-[16px]">
                    {data?.LinkSection3.Links.map((link: any) => (
                      <li key={link.id} className="mt-[13px]">
                        <Link
                          className="text-[14px] font-normal text-[#D8F0FC]"
                          href={link.Link}
                        >
                          {link.Label}
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
              {data?.Address && (
                <div className="quick_links w-[calc(100%/2-34px)] max-sm:w-full max-sm:order-5">
                  <h3 className="text-[16px] font-bold text-white capitalize">
                    Address
                  </h3>
                  <ul className="mt-[16px]">
                    <li className="text-[14px] font-normal text-[#D8F0FC]">
                      {data?.Address.Address}
                    </li>
                    {data?.Address.LegalNumbers.map((legalNumber: any) => (
                      <li
                        key={legalNumber.id}
                        className="text-[14px] font-normal text-[#D8F0FC] mt-[14px]"
                      >
                        {legalNumber.Text}: {legalNumber.Value}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
              {data?.Contact && (
                <div className="quick_links w-[calc(100%/2-34px)] max-sm:w-[calc(100%/2-15px)] max-sm:order-4 contact_quick">
                  <h3 className="text-[16px] font-bold text-white capitalize">
                    {data?.Contact.Title}
                  </h3>
                  <ul className="mt-[16px]">
                    <li className="text-[14px] font-normal text-[#D8F0FC] max-sm:break-all">
                      <span className="max-sm:block max-sm:mb-[6px]">
                        Email us :
                      </span>
                      <a
                        className="pl-[5px] max-sm:pl-[0] mail_area"
                        href={`mailto:${data?.Contact.Mail}`}
                      >
                        {data?.Contact.Mail}
                      </a>
                    </li>
                    <li className="text-[14px] font-normal text-[#D8F0FC] mt-[8px]">
                      <span className="max-sm:block">Call us : </span>
                      <a
                        className="pl-[5px] max-sm:pl-[0] max-sm:block max-sm:w-fit max-sm:mt-[8px]"
                        href={`tel:${data?.Contact.Phone1}`}
                      >
                        {data?.Contact.Phone1}
                      </a>{" "}
                      <span className="max-sm:hidden">|</span>{" "}
                      <a
                        className="max-sm:block max-sm:mt-[8px] max-sm:w-fit"
                        href={`tel:${data?.Contact.Phone2}`}
                      >
                        {data?.Contact.Phone2}
                      </a>
                    </li>
                    <li className="text-[14px] font-normal text-[#D8F0FC] mt-[8px]">
                      TOLL-FREE :{" "}
                      <a
                        className="pl-[5px] max-sm:mt-[8px] max-sm:block max-sm:w-fit max-sm:pl-[0]"
                        href={`tel:${data?.Contact.TollFreeNumber}`}
                      >
                        {data?.Contact.TollFreeNumber}
                      </a>
                    </li>
                  </ul>
                </div>
              )}
            </div>
          </div>

          <div className="sub_footer flex justify-between items-center gap-[40px] pt-[28px] pb-[34px] max-lg:flex-col-reverse max-lg:gap-[15px]">
            <div className="copyright">
              {data?.CopyRight && (
                <p className="text-[14px] font-normal text-white flex justify-start items-center gap-[10px] max-sm:flex-col max-sm:text-center">
                  {data?.CopyRight}
                  <Link
                    className="mt-[-5px]"
                    href="https://webcastletech.com/"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <Image
                      className=""
                      src="/webcastle.svg"
                      alt="company"
                      width={82}
                      height={17}
                    />
                  </Link>
                </p>
              )}
            </div>

            <div className="terms_links">
              <ul className="flex justify-start items-center">
                {data?.TermsPrivacy &&
                  data?.TermsPrivacy.length > 0 &&
                  data?.TermsPrivacy.map((item: any, index: any) => (
                    <li
                      key={item.id}
                      className={
                        index > 0
                          ? "border-l border-[white] pl-[6px]"
                          : "pr-[6px]"
                      }
                    >
                      <Link
                        className="text-[14px] font-normal text-white"
                        href={item.Link}
                      >
                        {item.Label}
                      </Link>
                    </li>
                  ))}
              </ul>
            </div>
          </div>
        </div>
      </footer>
    </main>
  );
}
