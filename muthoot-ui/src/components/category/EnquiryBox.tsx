"use client";
import React, { useState, useRef } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import Button from "../ui/Button";
import toast from "react-hot-toast";
import useFetch from "@/hooks/useFetch";
import endpoints from "@/endpoints";
import Image from "next/image";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/style.css";

interface Errors {
  Name?: string;
  Email?: string;
  Phone?: string;
  Service?: string;
  Pincode?: string;
  Terms?: string;
}

export default function EnquiryBox() {
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [mobile, setMobile] = useState("");
  const [serviceName, setServiceName] = useState("");
  const [pincode, setPincode] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Errors>({});
  const [isChecked, setIsChecked] = useState(false);

  const { data: serviceData } = useFetch<any>(
    `${process.env.NEXT_PUBLIC_API_URL}${endpoints.getProductTitle}`,
    { cache: false }
  );

  const handleInputChange = (field: keyof Errors, value: string) => {
    if (field === "Name") {
      const nameRegex = /^[a-zA-Z\s]*$/;
      if (nameRegex.test(value)) {
        setName(value);
      }
    }
    if (field === "Email") setEmail(value);
    if (field === "Phone") setMobile(value);
    if (field === "Service") setServiceName(value);
    if (field === "Pincode") setPincode(value);
    setErrors((prevErrors) => ({ ...prevErrors, [field]: undefined }));
  };

  const validate = () => {
    const errors: Errors = {};
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const phoneRegex = /^\d{10}$/;
    const pincodeExactly6Digits = /^\d{6}$/;
    const pincodeStartsWithZero = /^0\d{5}$/;

    if (!name) errors.Name = "Name is required";
    else if (name.length < 3)
      errors.Name = "Name must be at least 3 characters";

    if (!email) {
      errors.Email = "Email is required";
    } else if (!emailRegex.test(email)) {
      errors.Email = "Enter a valid email address";
    }
    if (!mobile) {
      errors.Phone = "Mobile no is required";
    } else if (!phoneRegex.test(mobile)) {
      errors.Phone = "Enter a 10-digit mobile no";
    }
    if (!serviceName) errors.Service = "Select a service";
    if (!pincode) {
      errors.Pincode = "Pincode is required";
    } else if (pincodeStartsWithZero.test(pincode)) {
      errors.Pincode = "Pincode cannot start with 0";
    } else if (!pincodeExactly6Digits.test(pincode)) {
      errors.Pincode = "Enter a valid 6-digit pincode";
    }
    if (!isChecked) errors.Terms = "You must agree to the terms";
    return errors;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    const validationErrors = validate();

    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      setIsSubmitting(false);
      toast.error("Please fill all required fields correctly");
    } else {
      try {
        const apiUrl =
          "https://api-in21.leadsquared.com/v2/LeadManagement.svc/Lead.Capture?accessKey=u%24r1a694dc4e52d13c376ff37e3fac30111&secretKey=cb50b489ae36463ec43ae5d10eb9cc3b7bfc85fc";

        const getServiceCode = (title: string) => {
          const serviceMap: { [key: string]: string } = {
            "Two Wheeler Loans": "TWL",
            "Used Car Loan": "UCL",
            "Loyalty Loans": "LLN",
            "Used Commercial Vehicle": "UCV",
            "Fixed deposits": "FXD",
          };
          return serviceMap[title] || title;
        };

        const leadsquaredPayload = [
          {
            Attribute: "FirstName",
            Value: name,
          },
          {
            Attribute: "LastName",
            Value: "",
          },
          {
            Attribute: "Phone",
            Value: mobile,
          },
          {
            Attribute: "SearchBy",
            Value: "Phone",
          },
          {
            Attribute: "mx_PinCode",
            Value: pincode,
          },
          {
            Attribute: "mx_User_State",
            Value: "",
          },
          {
            Attribute: "mx_Type_of_products",
            Value: getServiceCode(serviceName) || "General Inquiry",
          },
        ];

        const response = await fetch(apiUrl, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(leadsquaredPayload),
        });

        const responseData = await response.json();

        if (response.ok && responseData.Status === "Success") {
          toast.success("Thank you! We'll get back to you shortly.");
          setName("");
          setEmail("");
          setMobile("");
          setServiceName("");
          setPincode("");
          setIsChecked(false);
        } else {
          // Handle API error response
          if (responseData.Status === "Error") {
            // Map API error messages to form fields
            if (responseData.ExceptionMessage.includes("Phone")) {
              setErrors((prev) => ({
                ...prev,
                Phone: responseData.ExceptionMessage,
              }));
            } else if (responseData.ExceptionMessage.includes("Email")) {
              setErrors((prev) => ({
                ...prev,
                Email: responseData.ExceptionMessage,
              }));
            } else {
              // Generic error message for other errors
              toast.error(
                responseData.ExceptionMessage ||
                  "Something went wrong. Please try again later."
              );
            }
          } else {
            toast.error("Something went wrong. Please try again later.");
          }
        }
      } catch (error) {
        console.error("API call error:", error);
        toast.error("An error occurred. Please try again.");
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  return (
    <div
      data-aos="fade-up"
      className="enquiry_box bg-white w-full rounded-[10px] p-[20px]"
    >
      <h4 className="text-[24px] font-bold text-black mb-[16px]">Apply Now</h4>
      <form onSubmit={handleSubmit}>
        <div className="multi_input flex justify-start items-start gap-[25px] mt-[25px] max-xl:flex-col max-lg:flex-row max-sm:flex-col">
          <div className="input_parent w-[calc(100%/2-12px)] max-xl:w-full max-lg:w-[calc(100%/2-12px)] max-sm:w-full">
            <input
              placeholder="Your name"
              className={`bg-white w-full border ${
                errors.Name ? "border-red-500" : "border-[rgba(33,147,209,0.5)]"
              } rounded-[8px] p-[16px] h-[46px] text-[14px] font-normal text-[#6C727F] placeholder:text-[#6C727F]`}
              type="text"
              value={name}
              onChange={(e) => handleInputChange("Name", e.target.value)}
            />
            {errors.Name && (
              <p className="text-red-500 text-xs mt-2">{errors.Name}</p>
            )}
          </div>
          <div className="input_parent w-[calc(100%/2-12px)] max-xl:w-full max-lg:w-[calc(100%/2-12px)] max-sm:w-full">
            <div className="relative">
              <input
                placeholder="<EMAIL>"
                className={`bg-white w-full border pl-[18px] ${
                  errors.Email
                    ? "border-red-500"
                    : "border-[rgba(33,147,209,0.5)]"
                } rounded-[8px] p-[16px] h-[46px] text-[14px] font-normal text-[#6C727F] placeholder:text-[#6C727F]`}
                type="text"
                value={email}
                onChange={(e) => handleInputChange("Email", e.target.value)}
              />
              <Image
                src="/email1.svg"
                alt="email"
                width={18}
                height={12}
                className="absolute top-[50%] -translate-y-[50%] right-[16px]"
              />
            </div>
            {errors.Email && (
              <p className="text-red-500 text-xs mt-2">{errors.Email}</p>
            )}
          </div>
        </div>
        <div className="multi_input flex justify-start items-start gap-[25px] mt-[25px] max-xl:flex-col max-lg:flex-row max-sm:flex-col">
          <div className="input_parent w-[calc(100%/2-12px)] max-xl:w-full max-lg:w-[calc(100%/2-12px)] max-sm:w-full">
            <input
              placeholder="Phone Number"
              className={`bg-white w-full rounded-[8px] border ${
                errors.Phone
                  ? "border-red-500"
                  : "border-[rgba(33,147,209,0.5)]"
              } py-[22px] px-[16px] h-[46px] text-[14px] font-normal text-[#6C727F] placeholder:text-[#6C727F] max-md:h-[49px] [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none`}
              type="number"
              value={mobile}
              onChange={(e) => handleInputChange("Phone", e.target.value)}
            />
            {errors.Phone && (
              <p className="text-red-500 text-xs mt-2">{errors.Phone}</p>
            )}
          </div>
          <div className="input_parent w-[calc(100%/2-12px)] max-xl:w-full max-lg:w-[calc(100%/2-12px)] max-sm:w-full">
            <input
              placeholder="Pincode"
              className={`bg-white w-full rounded-[8px] border ${
                errors.Pincode
                  ? "border-red-500"
                  : "border-[rgba(33,147,209,0.5)]"
              } py-[22px] px-[16px] h-[46px] text-[14px] font-normal text-[#6C727F] placeholder:text-[#6C727F] max-md:h-[49px] [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none`}
              type="number"
              value={pincode}
              onChange={(e) => handleInputChange("Pincode", e.target.value)}
            />
            {errors.Pincode && (
              <p className="text-red-500 text-xs mt-2">{errors.Pincode}</p>
            )}
          </div>
        </div>
        <div className="single_field mt-[25px]">
          <div className="input_parent">
            <Select
              key={serviceName || "service-select"}
              onValueChange={(value) => handleInputChange("Service", value)}
              value={serviceName}
            >
              <SelectTrigger
                className={`bg-white rounded-[8px] border ${
                  errors.Service
                    ? "border-red-500"
                    : "border-[rgba(33,147,209,0.5)]"
                } py-[22px] px-[16px] h-[46px] text-[14px] font-normal text-[#6C727F] placeholder:text-[#6C727F] max-md:h-[49px]`}
              >
                <SelectValue
                  placeholder={
                    <>
                      Select service <span className="text-[#FF0000]">*</span>
                    </>
                  }
                />
              </SelectTrigger>
              <SelectContent>
                {serviceData?.map((serviceItem: any, index: number) => (
                  <SelectItem key={index} value={serviceItem.Title}>
                    {serviceItem.Title}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.Service && (
              <p className="text-red-500 text-xs mt-2">{errors.Service}</p>
            )}
          </div>
        </div>

        <div className="terms_wrapper flex justify-start items-start gap-[8px] mt-[16px]">
          <div className="check_boxwrapper">
            <Checkbox
              checked={isChecked}
              onCheckedChange={(checked) => {
                setIsChecked(checked as boolean);
                if (checked) {
                  setErrors((prevErrors) => ({
                    ...prevErrors,
                    Terms: undefined,
                  }));
                }
              }}
            />
          </div>
          <div className="flex flex-col">
            <p className="text-[12px] leading-[16px] text-[#484848] font-normal">
              I authorize Muthoot Capital & other Muthoot Pappachan Group
              companies (including its Agents/representatives) to
              call/communicate with me on their product offerings/promotions
              through Telephone/Mobile/SMS/email ID/WhatsApp.
            </p>
            {errors.Terms && (
              <p className="text-red-500 text-xs mt-1">{errors.Terms}</p>
            )}
          </div>
        </div>

        <div className="btn_wrapper mt-[26px]">
          <Button className="max-w-[255px] w-full" disabled={isSubmitting}>
            {isSubmitting ? "Submitting..." : "Submit"}
          </Button>
        </div>
      </form>
    </div>
  );
}
