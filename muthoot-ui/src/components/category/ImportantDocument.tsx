"use client";
import React from "react";

export default function ImportantDocument({ data }: any) {
  return (
    <>
      {data?.map((benefit: any, index: any) => (
        <div
          data-aos="fade-up"
          key={index}
          className="benefit_card flex flex-col justify-between self-stretch bg-[rgba(255,255,255,0.2)] rounded-[20px] p-[24px] w-[calc(100%/4-22px)] max-xl:w-[calc(100%/3-18px)] max-lg:w-[calc(100%/2-14px)] max-sm:w-full "
        >
          <div className="content">
            <h3 className="text-[20px] font-semibold text-white max-md:text-[17px]">
              {benefit.Title}
            </h3>
            <p className="text-[16px] leading-[24px] text-[#DFDBDB] font-normal max-md:text-[14px] mt-[10px]">
              {benefit.Description}
            </p>
          </div>
          <button
            className="download_btn w-fit text-[14px] mt-[17px] font-semibold text-[#008bd2] bg-[white] py-[7px] px-[16px] rounded-[8px] flex items-center gap-[7px]"
            onClick={() =>
              window.open(
                `${process.env.NEXT_PUBLIC_API_BASE_URL}${benefit?.Document?.url}`,
                "_blank"
              )
            }
          >
            Download{" "}
            <svg
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g clipPath="url(#clip0_0_12087)">
                <path
                  d="M20 10C20 4.48613 15.5139 0 10 0C4.48613 0 0 4.48613 0 10C0 15.5139 4.48613 20 10 20C15.5139 20 20 15.5139 20 10ZM0.999184 10C0.999184 5.03671 5.03671 0.999184 10 0.999184C14.9633 0.999184 19.0008 5.03671 19.0008 10C19.0008 14.9633 14.9633 19.0008 10 19.0008C5.03671 19.0008 0.999184 14.9633 0.999184 10Z"
                  fill="#008bd2"
                />
                <path
                  d="M10.3539 14.6983L13.1638 11.8883C13.3596 11.6926 13.3596 11.3785 13.1638 11.1828C12.9681 10.987 12.6541 10.987 12.4583 11.1828L10.5007 13.1403V5.65666C10.5007 5.37934 10.2764 5.15503 9.99908 5.15503C9.72176 5.15503 9.49745 5.37934 9.49745 5.65666V13.1363L7.53986 11.1787C7.34411 10.9829 7.03008 10.9829 6.83432 11.1787C6.63856 11.3744 6.63856 11.6885 6.83432 11.8842L9.64427 14.6942C9.74215 14.7921 9.86858 14.841 9.99908 14.841C10.1296 14.841 10.256 14.7961 10.3539 14.6983Z"
                  fill="#008bd2"
                />
              </g>
              <defs>
                <clipPath id="clip0_0_12087">
                  <rect width="20" height="20" fill="white" />
                </clipPath>
              </defs>
            </svg>
          </button>
        </div>
      ))}
    </>
  );
}
