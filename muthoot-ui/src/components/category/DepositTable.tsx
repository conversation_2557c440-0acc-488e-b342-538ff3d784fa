import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import CommonTextBlue from "../common/CommonTextBlue";

export default function DepositTable({ data }: any) {
  const depositTable = data;
  const tableContent = depositTable?.Table_Content || [];

  const defaultTab = tableContent[0]?.Scheme_Tab?.Tab_Name || "";

  return (
    <div className="fd_navtabwrapper">
      <h2 className="text-[44px] max-w-[705px] w-full mx-auto font-semibold text-[#141414] text-center max-md:text-[35px] max-md:leading-[43px] ">
        <CommonTextBlue title={depositTable?.Title} />
      </h2>
      <p className="text-[16px] text-center text-[#484848] mt-[20px] font-[400] max-w-[950px] w-full mx-auto">
        {depositTable?.Description}
      </p>

      <Tabs defaultValue={defaultTab} className="w-full mt-[40px]">
        {tableContent.some((item: any) => item?.Scheme_Tab?.Tab_Name) && (
          <TabsList className="justify-center w-full max-lg:overflow-x-auto max-lg:justify-start max-lg:pb-[10px] max-md:justify-center max-sm:justify-start">
            {tableContent.map((item: any, index: any) => {
              const tabName = item?.Scheme_Tab?.Tab_Name;
              if (!tabName) return null;

              const displayName = tabName.replace(/_/g, " ");
              return (
                <TabsTrigger
                  key={index}
                  className="max-md:p-[15px] max-md:text-[16px]"
                  value={tabName}
                >
                  {displayName}
                </TabsTrigger>
              );
            })}
          </TabsList>
        )}

        {tableContent.map((item: any, index: any) => {
          const schemeTab = item?.Scheme_Tab;
          const tabName = schemeTab?.Tab_Name || "";
          const schemeContent = schemeTab?.Scheme_Content || [];
          const columns = schemeTab?.Column || [];

          const columnCount = columns.length;

          // Check if any column has a title
          const hasColumnTitles = columns.some(
            (column: any) => column.Column_Title
          );

          const maxRows = Math.max(
            ...columns.map((col: any) => col.Column_Options?.length || 0)
          );

          return (
            <TabsContent key={index} value={tabName}>
              <div className="table_wrapper mt-[50px] max-w-[850px] w-full mx-auto max-sm:overflow-x-auto">
                <table className="w-full">
                  <thead className="w-full">
                    {schemeContent.map((scheme: any, schemeIndex: any) => (
                      <tr key={schemeIndex}>
                        <th
                          className="bg-[#008bd2] p-[20px] py-[13px] border border-[#41afe6] text-white"
                          colSpan={columnCount}
                        >
                          {scheme.Scheme_Name}
                        </th>
                      </tr>
                    ))}
                  </thead>
                  <tbody className="w-full">
                    {hasColumnTitles && (
                      <tr>
                        {columns.map((column: any, colIndex: any) => (
                          <td
                            key={colIndex}
                            className="p-[20px] py-[13px]  bg-[#e7f7ff] text-center border border-[#41afe6]"
                            colSpan={1}
                          >
                            <strong>{column.Column_Title}</strong>
                          </td>
                        ))}
                      </tr>
                    )}

                    {Array.from({ length: maxRows }).map((_, rowIndex) => (
                      <tr key={rowIndex}>
                        {columns.map((column: any, colIndex: any) => {
                          const option = column.Column_Options?.[rowIndex];
                          const bgColor =
                            rowIndex % 2 === 0 ? "bg-[white]" : "bg-[#e7f7ff]";

                          return (
                            <td
                              key={colIndex}
                              className={`p-[20px] py-[13px]  ${bgColor} text-center border border-[#41afe6]`}
                              colSpan={1}
                            >
                              {option?.Option || ""}
                            </td>
                          );
                        })}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </TabsContent>
          );
        })}
      </Tabs>
    </div>
  );
}
