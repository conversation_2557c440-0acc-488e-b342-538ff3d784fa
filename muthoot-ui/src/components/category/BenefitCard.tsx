import React from "react";

export default function BenefitList({ data }: any) {
  return (
    <>
      {data?.map((benefit: any, index: any) => (
        <div
          data-aos="fade-up"
          key={index}
          className="benefit_card self-stretch bg-[rgba(255,255,255,0.2)] rounded-[20px] p-[24px] w-[calc(100%/4-22px)] max-xl:w-[calc(100%/3-18px)] max-lg:w-[calc(100%/2-14px)] max-sm:w-full "
        >
          <h3 className="text-[20px] font-semibold text-white max-md:text-[17px]">
            {benefit.Title}
          </h3>
          <p className="text-[16px] leading-[24px] text-[#DFDBDB] font-normal max-md:text-[14px] mt-[10px]">
            {benefit.Description}
          </p>
        </div>
      ))}
    </>
  );
}
