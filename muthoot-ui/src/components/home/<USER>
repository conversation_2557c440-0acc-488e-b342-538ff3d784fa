import React from "react";
import { ProductBoxSkeleton } from "../skeleton/ProductBoxSkeleton";
import Image from "next/image";
import Link from "next/link";

const ProductBoxHome = ({ data }: any) => {
  const sortedData =
    data && data.length > 0
      ? [...data].sort(
          (a, b) => (a.Product_Order || 999) - (b.Product_Order || 999)
        )
      : [];
  return (
    <div className="financial_loanwrapper flex justify-start items-start gap-[16px] flex-wrap mt-[36px] relative z-[9]">
      {sortedData && sortedData?.length > 0 ? (
        sortedData?.map((item: any, index: number) => (
          <div
            data-aos="fade-up"
            key={index}
            className="financial_box flex self-stretch justify-start items-center gap-[16px] p-[14px] rounded-[14px] border bg-[rgba(255,255,255,0.2)] border-[rgba(255,255,255,0.3)] w-[calc(100%/2-8px)] max-xl:max-w-[800px] max-xl:mx-auto max-xl:w-full hover:bg-[#5b3d3d33] max-sm:items-start "
          >
            <div className="financial_image max-w-[210px] w-full   max-sm:max-w-[80px]">
              <Image
                className="rounded-[6px] w-full h-full aspect-square object-cover"
                src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${
                  item?.Thumbnail?.url || ""
                }`}
                alt="founder"
                width={200}
                height={210}
              />
            </div>

            <div className="financial_contents max-w-[calc(100%-210px)] w-full max-sm:max-w-[calc(100%-100px)]">
              <h4 className="text-[20px] leading-[26px] text-white font-semibold line-clamp-2 max-md:text-[18px] ma-md:leading-[25px]">
                {item?.Title}
              </h4>
              <p className="text-[16px] leading-[22px] text-white font-normal mt-[12px] line-clamp-4 max-md:text-[14px] max-md:leading-[22px] max-md:mt-[6px]">
                {item?.Short_Description}
              </p>

              <Link
                href={`/products/${item?.slug}`}
                className="view_all text-[16px] font-bold text-white mt-[14px] flex justify-start items-center gap-[4px] max-md:text-[14px] max-md:mt-[8px]"
              >
                View All{" "}
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M9 18L15 12L9 6"
                    stroke="white"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </Link>
            </div>
          </div>
        ))
      ) : (
        <>
          <ProductBoxSkeleton />
          <ProductBoxSkeleton />
          <ProductBoxSkeleton />
          <ProductBoxSkeleton />
        </>
      )}
    </div>
  );
};

export default ProductBoxHome;
