import React from "react";
import Image from "next/image";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

export default function TestimonialHome({ data }: any) {
  const truncateText = (text: string, maxLength: number = 200) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + "...";
  };

  return (
    <div className="testimonial_boxwrapper flex justify-start items-start gap-[30px] flex-wrap">
      {data?.map((testimonial: any) => (
        <div
          key={testimonial.id}
          className="testimonial_box bg-white rounded-[12px] p-[32px] w-[calc(100%/2-15px)] max-md:w-full max-sm:p-[20px]"
        >
          <Image
            className="max-w-[50px] w-full object-contain "
            src="/quote_icon.svg"
            alt="quote_icon"
            width={50}
            height={40}
          />
          <p className="mt-[18px]">
            {truncateText(testimonial.Quote)}{" "}
            <Dialog>
              <DialogTrigger asChild>
                <span className="text-[18px] font-semibold text-[#008BD2] cursor-pointer">
                  Read more
                </span>
              </DialogTrigger>
              <DialogContent className="bg-white max-w-[800px] max-h-[80vh] p-[32px] w-full border-none rounded-[20px] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle className="text-[24px] font-semibold text-[#141414] mb-4">
                    Customer Testimonial
                  </DialogTitle>
                </DialogHeader>
                <DialogDescription asChild>
                  <div className="space-y-6">
                    <div className="flex justify-start items-center gap-[24px] max-md:flex-col">
                      <div className="testimonial_image max-w-[100px] w-full aspect-square flex-shrink-0">
                        <Image
                          className="w-full h-full object-cover aspect-square rounded-full"
                          src={
                            testimonial.User_Image?.url
                              ? `${process.env.NEXT_PUBLIC_API_BASE_URL}${testimonial.User_Image.url}`
                              : "/testimonial-image.svg"
                          }
                          alt={testimonial.Name || "testimonial"}
                          width={150}
                          height={150}
                        />
                      </div>
                      <div className="content flex-1">
                        <h4 className="text-[20px] font-[700] text-[#141414]">
                          {testimonial.Name}
                        </h4>

                        <div className="region_wrapper mt-[8px]">
                          <span className="text-[16px] font-[700] text-[#141414]">
                            Region :
                          </span>{" "}
                          <span className="text-[14px] font-normal text-[#484848]">
                            {testimonial.Region}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="quote_content">
                      <Image
                        className="max-w-[40px] w-full object-contain mb-4"
                        src="/quote_icon.svg"
                        alt="quote_icon"
                        width={40}
                        height={32}
                      />
                      <p className="text-[16px] font-normal text-[#141414] leading-[24px]">
                        {testimonial.Quote}
                      </p>
                    </div>
                  </div>
                </DialogDescription>
              </DialogContent>
            </Dialog>
          </p>

          <div className="wrapper mt-[26px] flex justify-start items-center gap-[18px]">
            <div className="testimonial_image max-w-[100px] w-full aspect-square">
              <Image
                className="w-full h-full object-cover aspect-square rounded-full"
                src={
                  testimonial.User_Image?.url
                    ? `${process.env.NEXT_PUBLIC_API_BASE_URL}${testimonial.User_Image.url}`
                    : "/testimonial-image.svg"
                }
                alt={testimonial.Name || "testimonial"}
                width={100}
                height={100}
              />
            </div>
            <div className="content">
              <h4 className="text-[16px] font-[700] text-[#141414]">
                {testimonial.Name}
              </h4>
              <div className="region_wrapper mt-[8px]">
                <span className="text-[16px] font-[700] text-[#141414]">
                  Region :
                </span>{" "}
                <span className="text-[14px] font-normal text-[#484848]">
                  {testimonial.Region}
                </span>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
