import React from "react";
import DigitalInitiativeHome from "./DigitalInitiativeHome";
import Link from "next/link";
import CommonTextBlue from "../common/CommonTextBlue";
import { GetDigitalHome } from "@/lib/api/general";

export default async function DigitalSection({ data }: any) {
  const { data: digitalHome } = await GetDigitalHome();

  return (
    <section
      data-aos="fade-up"
      className="digital_intiatives py-[100px] bg-[#E7F7FF] max-md:py-[50px]"
    >
      <div className="container">
        <div className="media_head flex justify-between items-end mb-[36px] max-lg:flex-col max-lg:items-start max-lg:gap-[30px]">
          <div className="title max-w-[600px] w-full">
            <h3
              data-aos="fade-up"
              className="text-[44px] text-[#141414] font-semibold max-md:text-[35px] max-md:leading-[45px]"
            >
              <CommonTextBlue title={data?.Title} />
            </h3>
            <p
              data-aos="fade-up"
              className="text-[16px] font-normal text-[#484848] leading-[24px] mt-[16px] text-justify max-sm:text-[14px] max-sm:leading-[22px]"
            >
              {data?.Description}
            </p>
          </div>
          <div
            data-aos="fade-up"
            className="view_wrapper  max-lg:w-full max-lg:flex max-lg:justify-end"
          >
            <Link
              className="text-[16px] font-bold text-[#008BD2] flex justify-start items-center gap-[13px]"
              href={data?.Button?.Link}
            >
              {data?.Button?.Label}
              <svg
                width="8"
                height="14"
                viewBox="0 0 8 14"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M1 13L7 7L1 1"
                  stroke="#008BD2"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>{" "}
            </Link>
          </div>
        </div>

        {digitalHome?.length > 0 ? (
          <DigitalInitiativeHome data={digitalHome} />
        ) : (
          <p></p>
        )}
      </div>
    </section>
  );
}
