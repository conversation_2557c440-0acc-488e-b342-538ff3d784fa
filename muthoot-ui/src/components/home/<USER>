import React from "react";
import CommonTextBlue from "../common/CommonTextBlue";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import Image from "next/image";
const BranchSectionShowcase = ({ data }: any) => {
  return (
    <section className="brand_showcase pt-[90px] max-md:pt-[60px] ">
      <div className="container">
        <div className="media_head flex justify-between items-end mb-[36px] max-lg:flex-col max-lg:items-start max-lg:gap-[30px]">
          <div className="title max-w-full w-full">
            <h3
              data-aos="fade-up"
              className="text-[44px] text-[#141414] font-semibold max-md:text-[35px] max-md:leading-[45px]"
            >
              <CommonTextBlue title={data?.Showcase_Section_Title} />
            </h3>
          </div>
        </div>

        <div className="showcase_imagewrapper flex justify-start items-start gap-[32px] pb-[100px] max-lg:flex-col max-lg:justify-center max-md:!pb-[60px]">
          {data?.ShowcaseVideos?.map((item: any, index: number) => (
            <Dialog key={index}>
              <DialogTrigger className=" w-[calc(100%/2-16px)] max-lg:w-full max-lg:max-w-[700px] max-lg:mx-auto">
                <div
                  data-aos="fade-up"
                  className="showcase_imagebox relative overflow-hidden rounded-[20px] max-lg:w-full max-lg:max-w-[700px] max-lg:mx-auto"
                >
                  <div className="bg-gradient-to-b from-transparent to-black/60 absolute bottom-0 left-0 w-full h-full"></div>
                  <div className="image_wrapper">
                    <Image
                      className="h-full w-full object-cover"
                      src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${item?.Thumbnail?.url}`}
                      alt="thumb-image"
                      width={1500}
                      height={1500}
                    />
                  </div>
                  <div className="overlaped_content flex justify-between items-start gap-[50px] absolute w-[90%] mx-auto bottom-[20px] left-[50%] -translate-x-[50%] ">
                    <h4 className="max-w-[436px] w-full text-[20px] leading-[26px] text-white max-md:text-[16px] max-sm:leading-[22px] text-start line-clamp-2 ">
                      {item.Text}
                    </h4>
                    <div className="icon">
                      <svg
                        width="46"
                        height="46"
                        viewBox="0 0 46 46"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M23.0026 42.1668C33.5881 42.1668 42.1693 33.5856 42.1693 23.0002C42.1693 12.4147 33.5881 3.8335 23.0026 3.8335C12.4171 3.8335 3.83594 12.4147 3.83594 23.0002C3.83594 33.5856 12.4171 42.1668 23.0026 42.1668Z"
                          stroke="white"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                        <path
                          d="M19.1641 15.3335L30.6641 23.0002L19.1641 30.6668V15.3335Z"
                          stroke="white"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
              </DialogTrigger>

              <DialogContent className="bg-transparent max-w-[850px] h-[450px] w-full p-0 border-none rounded-[20px] max-md:max-w-[550px] max-md:h-[350px] max-sm:h-[280px] ">
                <DialogTitle></DialogTitle>
                <DialogDescription>
                  {item.Video ? (
                    <video
                      className="w-full h-full object-cover rounded-[20px]"
                      controls
                      autoPlay
                    >
                      <source
                        src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${item.Video?.url}`}
                        type={item.Video?.mime}
                      />
                      Your browser does not support the video tag.
                    </video>
                  ) : item.Video_Link ? (
                    <iframe
                      className="w-full h-full object-cover rounded-[20px]"
                      allowFullScreen
                      src={item.Video_Link.replace(
                        "youtu.be/",
                        "youtube.com/embed/"
                      ).replace("watch?v=", "embed/")}
                      title={item.Text || "Video player"}
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                    ></iframe>
                  ) : (
                    <div className="flex items-center justify-center h-full bg-black rounded-[20px]">
                      <p className="text-white">No video available</p>
                    </div>
                  )}
                </DialogDescription>
              </DialogContent>
            </Dialog>
          ))}
        </div>
      </div>
    </section>
  );
};

export default BranchSectionShowcase;
