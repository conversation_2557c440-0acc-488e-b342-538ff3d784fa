"use client";
import { useEffect } from "react";

export default function ScrollToSection() {
  useEffect(() => {
    const handleHash = () => {
      const hash = window.location.hash;

      if (hash === "#calculator") {
        const calculatorSection = document.getElementById("calculator");
        if (calculatorSection) {
          setTimeout(() => {
            calculatorSection.scrollIntoView({ behavior: "smooth" });
          }, 300);
        }
      } else if (hash === "#interest-rates-table") {
        const depositTableSection = document.getElementById(
          "interest-rates-table"
        );
        if (depositTableSection) {
          setTimeout(() => {
            depositTableSection.scrollIntoView({ behavior: "smooth" });
          }, 300);
        }
      }
    };

    handleHash();

    window.addEventListener("hashchange", handleHash);

    return () => {
      window.removeEventListener("hashchange", handleHash);
    };
  }, []);

  return null;
}
