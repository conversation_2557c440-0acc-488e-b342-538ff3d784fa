import React from "react";
import Image from "next/image";
import Link from "next/link";

export default function BranchBox({ branches }: any) {
  return (
    <>
      {branches?.map((branch: any, index: any) => (
        <div
          data-aos="fade-up"
          key={index}
          className="branch_box rounded-[14px] self-stretch  overflow-hidden p-[20px] border border-[#B7DFF4] bg-[#E7F7FF] w-[calc(100%/3-19px)] relative max-lg:w-[calc(100%/2-14px)] max-md:w-full"
        >
          <div className="branch_particle absolute top-0 right-0 max-w-[260px] w-full">
            <Image
              className="w-full h-full"
              src="/branch_particle.png"
              alt="particle"
              width={260}
              height={260}
            />
          </div>

          <div className="content relative z-[60]">
            <h4 className="text-[20px] font-semibold text-[#141414]">
              {branch.Place}
            </h4>
            <p className="text-[16px] font-normal text-[#484848] mt-[12px]">
              {branch.Address}
            </p>
            <div className="time text-[18px] font-medium text-[#3C3C3C] flex items-center gap-[14px] mt-[18px]">
              <svg
                className="mt-[-2px]"
                width="18"
                height="18"
                viewBox="0 0 18 18"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g clipPath="url(#clip0_0_16381)">
                  <path
                    d="M9 0.75C7.36831 0.75 5.77325 1.23385 4.41655 2.14038C3.05984 3.0469 2.00242 4.33537 1.378 5.84286C0.753575 7.35035 0.590197 9.00915 0.908525 10.6095C1.22685 12.2098 2.01259 13.6798 3.16637 14.8336C4.32016 15.9874 5.79017 16.7732 7.39051 17.0915C8.99085 17.4098 10.6497 17.2464 12.1571 16.622C13.6646 15.9976 14.9531 14.9402 15.8596 13.5835C16.7661 12.2268 17.25 10.6317 17.25 9C17.2474 6.81276 16.3774 4.71584 14.8308 3.16922C13.2842 1.6226 11.1872 0.75258 9 0.75ZM11.7803 11.7803C11.6396 11.9209 11.4489 11.9998 11.25 11.9998C11.0511 11.9998 10.8604 11.9209 10.7198 11.7803L8.46975 9.53025C8.32909 9.38963 8.25005 9.1989 8.25 9V4.5C8.25 4.30109 8.32902 4.11032 8.46967 3.96967C8.61033 3.82902 8.80109 3.75 9 3.75C9.19892 3.75 9.38968 3.82902 9.53033 3.96967C9.67099 4.11032 9.75 4.30109 9.75 4.5V8.6895L11.7803 10.7198C11.9209 10.8604 11.9998 11.0511 11.9998 11.25C11.9998 11.4489 11.9209 11.6396 11.7803 11.7803Z"
                    fill="#008BD2"
                  />
                </g>
                <defs>
                  <clipPath id="clip0_0_16381">
                    <rect width="18" height="18" fill="white" />
                  </clipPath>
                </defs>
              </svg>
              <h5 className="w-[calc(100%-25px)]">{branch.Time}</h5>
            </div>
            <div className="time text-[18px] font-medium text-[#3C3C3C] flex items-center gap-[14px] mt-[16px]">
              <svg
                className="mt-[-2px]"
                width="18"
                height="18"
                viewBox="0 0 18 18"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M13.6846 11.9073C13.0965 11.3267 12.3624 11.3267 11.7781 11.9073C11.3323 12.3492 10.8866 12.7912 10.4484 13.2407C10.3285 13.3643 10.2274 13.3905 10.0813 13.3081C9.79289 13.1508 9.48575 13.0235 9.20857 12.8512C7.91633 12.0384 6.83384 10.9933 5.87496 9.81719C5.39926 9.23287 4.97601 8.60735 4.6801 7.90317C4.62017 7.76084 4.63141 7.6672 4.74752 7.55108C5.19325 7.12034 5.62775 6.67835 6.06599 6.23637C6.67652 5.62208 6.67653 4.90292 6.06224 4.28489C5.7139 3.9328 5.36555 3.5882 5.01721 3.23611C4.65763 2.87653 4.30179 2.51321 3.93847 2.15737C3.3504 1.58429 2.61626 1.58429 2.03194 2.16112C1.58246 2.6031 1.15172 3.05632 0.69475 3.49082C0.271493 3.8916 0.0579922 4.38228 0.0130446 4.95536C-0.0581224 5.88802 0.170361 6.76825 0.492486 7.626C1.15172 9.40143 2.15555 10.9783 3.37288 12.4242C5.01721 14.3794 6.97992 15.9263 9.27599 17.0425C10.3098 17.5444 11.381 17.9302 12.5459 17.9939C13.3475 18.0389 14.0442 17.8366 14.6023 17.2111C14.9843 16.7841 15.4151 16.3945 15.8196 15.9862C16.4189 15.3795 16.4227 14.6453 15.8271 14.046C15.1154 13.3306 14.4 12.6189 13.6846 11.9073Z"
                  fill="#008BD2"
                />
                <path
                  d="M12.9704 8.92175L14.3526 8.68577C14.1353 7.416 13.536 6.26609 12.6258 5.35216C11.6632 4.38953 10.4459 3.78274 9.10493 3.59546L8.91016 4.98509C9.9477 5.13117 10.8916 5.59937 11.637 6.34475C12.3412 7.04893 12.8019 7.94039 12.9704 8.92175Z"
                  fill="#008BD2"
                />
                <path
                  d="M15.1317 2.9141C13.5361 1.31846 11.5172 0.310888 9.28852 0L9.09375 1.38963C11.019 1.65932 12.7645 2.53205 14.1429 3.9067C15.4501 5.21392 16.3078 6.86575 16.6187 8.68238L18.0009 8.4464C17.6375 6.34136 16.6449 4.43108 15.1317 2.9141Z"
                  fill="#008BD2"
                />
              </svg>

              <h5 className="w-[calc(100%-25px)]">{branch.Phone}</h5>
            </div>
            <Link
              href={branch.Link}
              target="_blank"
              className="time text-[18px] font-medium text-[#008BD2] italic flex items-start gap-[14px] mt-[16px] underline"
            >
              <svg
                className="mt-[2px]"
                width="18"
                height="20"
                viewBox="0 0 18 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M9.00116 0.00146484C5.53822 0.00146484 2.72266 2.84214 2.72266 6.34601C2.72266 7.53288 3.2454 9.00531 4.00626 10.474C5.68146 13.7081 8.47191 16.9655 8.47191 16.9655C8.60399 17.1199 8.79746 17.2092 9.00116 17.2092C9.20487 17.2092 9.39834 17.1199 9.53042 16.9655C9.53042 16.9655 12.3209 13.7081 13.9961 10.474C14.7569 9.00531 15.2797 7.53288 15.2797 6.34601C15.2797 2.84214 12.4641 0.00146484 9.00116 0.00146484ZM9.00116 3.72206C7.5892 3.72206 6.44325 4.86801 6.44325 6.27997C6.44325 7.69194 7.5892 8.83788 9.00116 8.83788C10.4131 8.83788 11.5591 7.69194 11.5591 6.27997C11.5591 4.86801 10.4131 3.72206 9.00116 3.72206Z"
                  fill="#008BD2"
                />
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M13.4519 15.0022C14.2081 15.2292 14.8313 15.5101 15.262 15.8394C15.541 16.0514 15.7457 16.2588 15.7457 16.5118C15.7457 16.6607 15.661 16.7955 15.542 16.9304C15.3448 17.1527 15.0518 17.3527 14.689 17.5387C13.4073 18.1945 11.3349 18.6047 9.00209 18.6047C6.66927 18.6047 4.5969 18.1945 3.31516 17.5387C2.9524 17.3527 2.6594 17.1527 2.46221 16.9304C2.34315 16.7955 2.25851 16.6607 2.25851 16.5118C2.25851 16.2588 2.46314 16.0514 2.74218 15.8394C3.17284 15.5101 3.79604 15.2292 4.55225 15.0022C4.92059 14.8915 5.12988 14.5018 5.01919 14.1335C4.9085 13.7642 4.51877 13.5549 4.15043 13.6656C2.9989 14.0125 2.1041 14.4878 1.56647 15.0134C1.09396 15.4738 0.863281 15.9891 0.863281 16.5118C0.863281 17.1648 1.23255 17.8159 1.98318 18.3591C3.30027 19.3116 5.94562 19.9999 9.00209 19.9999C12.0586 19.9999 14.7039 19.3116 16.021 18.3591C16.7716 17.8159 17.1409 17.1648 17.1409 16.5118C17.1409 15.9891 16.9102 15.4738 16.4377 15.0134C15.9001 14.4878 15.0053 14.0125 13.8537 13.6656C13.4854 13.5549 13.0957 13.7642 12.985 14.1335C12.8743 14.5018 13.0836 14.8915 13.4519 15.0022Z"
                  fill="#008BD2"
                />
              </svg>
              <h5 className="w-[calc(100%5px)]">{branch.Location}</h5>
            </Link>
          </div>
        </div>
      ))}
    </>
  );
}
