import Image from "next/image";
import React from "react";
import CommonTextBlue from "../common/CommonTextBlue";
import Link from "next/link";
import Counter from "./Counter";
import Button from "@/components/ui/Button";

const AboutUsandCounterSection = ({ data, counterData }: any) => {
  return (
    <section className="about_section pt-[100px] relative max-sm:pt-[60px]">
      <div className="gradient_overlay w-full h-[433px] absolute top-0 left-0 bg-[linear-gradient(180deg,_#008BD2_6.98%,_rgba(0,139,210,0.804112)_22.82%,_rgba(0,139,210,0.480664)_47.96%,_rgba(0,139,210,0.146018)_63.98%,_rgba(0,139,210,0)_80.59%)]"></div>
      <div className="container">
        <div className="about_mainwrapper flex justify-between items-start gap-[70px] max-md:gap-[50px]">
          <div className="content max-w-[590px] w-full max-sm:!max-w-full">
            <Image
              data-aos="fade-up"
              className="w-full max-w-[130px] aspect-[130/105] h-full object-contain max-md:max-w-[100px]"
              src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${data?.Logo?.url}`}
              alt="logo"
              width={160}
              height={117}
            />
            <h3
              data-aos="fade-up"
              className="text-[44px] font-semibold text-[#141414] mt-[30px] mb-[18px] leading-[54px] max-md:text-[35px] max-md:leading-[44px]"
            >
              <CommonTextBlue title={data?.Title} />
            </h3>
            <p
              data-aos="fade-up"
              className="text-[16px] leading-[24px] font-normal text-[#141414] max-md:text-[14px] max-md:leading-[22px] text-justify"
            >
              {data?.Description}
            </p>
            <Link href={data?.Button?.Link}>
              <div
                data-aos="fade-up"
                className="btn_wrapper mt-[48px] max-md:mt-[25px]"
              >
                <Button children={data?.Button?.Label} />
              </div>
            </Link>
          </div>

          <div className="about_banner pl-[120px] relative max-w-[640px] w-full pr-[41px] max-sm:pl-0 max-sm:pr-0">
            <div
              data-aos="fade-down"
              className="branch-box rounded-[20px] px-[18px] py-[24px] bg-[#DCEFF9] shadow-md max-w-[197px] w-full absolute top-[143px] left-0 max-sm:max-w-[140px] max-sm:px-[10px] "
            >
              <Image
                className="w-full max-w-[45px] aspect-square h-full object-contain mx-auto max-md:max-w-[25px]"
                src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${data?.Counts_Top?.Icon?.url}`}
                alt="icon"
                width={45}
                height={45}
              />
              <h6 className="text-[18px] text-center leading-[26px] text-[#141414] font-normal mt-[10px] max-md:text-[14px] max-md:leading-[20px]">
                <CommonTextBlue
                  title={data?.Counts_Top?.Text}
                  highlightColor="font-bold text-[#008BD2]"
                />
              </h6>
            </div>
            <div
              data-aos="fade-up"
              className="branch-box rounded-[20px] px-[18px] py-[24px] bg-[#DCEFF9] shadow-md max-w-[197px] w-full absolute bottom-[66px] right-0  max-sm:max-w-[140px] max-sm:px-[10px]"
            >
              <Image
                className="w-full max-w-[45px] aspect-square h-full object-contain mx-auto max-md:max-w-[25px]"
                src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${data?.Counts_Bottom?.Icon?.url}`}
                alt="icon"
                width={45}
                height={45}
              />
              <h6 className="text-[18px] text-center leading-[26px] text-[#141414] font-normal mt-[10px] max-md:text-[14px] max-md:leading-[20px]">
                <CommonTextBlue
                  title={data?.Counts_Bottom?.Text}
                  highlightColor="font-bold text-[#008BD2]"
                />
              </h6>
            </div>
            <div className="main_image">
              <Image
                className="w-full max-w-[451px] aspect-[451/624] h-full object-contain mx-auto max-sm:w-[90%]"
                src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${data?.SRK_Image?.url}`}
                alt="banner"
                width={451}
                height={624}
              />
            </div>
          </div>
        </div>

        <div className="count_mainwrapper">
          <Counter data={counterData} />
        </div>
      </div>
    </section>
  );
};

export default AboutUsandCounterSection;
