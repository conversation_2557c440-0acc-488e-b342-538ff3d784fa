import Image from "next/image";
import Link from "next/link";
import React from "react";

const LoanRedirector = ({ data }: any) => {
  return (
    <div className="loan_wrapper z-[9] bg-white rounded-[20px]  py-[11px] px-[18px] absolute bottom-0 left-[50%] -translate-x-[50%] max-w-[873px] w-full shadow-lg max-md:relative max-md:translate-x-0 max-md:left-0 max-md:!p-[20px] max-md:mt-[-40px] max-sm:bg-[#008bd2] max-sm:rounded-[0] max-sm:relative max-sm:!mt-[-25px] max-sm:!shadow-none">
      <ul className="flex justify-between items-start  max-lg:gap-[25px] max-md:flex-wrap max-md:justify-center max-sm:gap-[12px] gap-[12px]">
        {data?.map((item: any, index: any) => (
          <li
            key={index}
            className="w-[calc(100%/3-8px)] self-stretch max-md:w-[calc(100%/3-8px)] max-sm:w-[calc(100%/2-8px)] bg-[#E8F7FF] rounded-[11px] p-[15px] hover:bg-[#bbe7ff]"
          >
            <Link href={item.Link || ""}>
              <Image
                className="max-w-[50px] object-contain h-auto aspect-square mx-auto max-lg:max-w-[30px]"
                src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${item.Icon?.url}`}
                alt={item.Title || "icon"}
                width={50}
                height={50}
              />

              <h6 className="text-[14px] font-semibold text-[#141414] text-center mt-[7px] line-clamp-2 max-w-[140px] w-full mx-auto">
                {item.Title}
              </h6>
            </Link>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default LoanRedirector;
