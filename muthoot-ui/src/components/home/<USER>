"use client";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON>alogDescription,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alog<PERSON><PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog";
import Image from "next/image";
import React, { useState } from "react";
import { Tabs, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";

export default function Timeline({ data }: any) {
  return (
    <Tabs
      defaultValue={data && data.length > 0 ? `step_${data[0].id}` : "step_1"}
      className="w-full flex flex-row gap-[50px] items-start max-sm:gap-[20px]"
    >
      <TabsList className="flex-col items-start w-[33px]">
        {data?.map((item: any, index: number) => (
          <TabsTrigger
            key={item.id}
            className="text-[12px] font-bold p-[0] text-[#BEBEBE] pl-[7px] relative items-start bg-transparent"
            value={`step_${item.id}`}
          >
            <span className="w-[2px] h-[70px] bg-[#BEBEBE] mr-[7px]"></span>
            {String(index + 1).padStart(2, "0")}
          </TabsTrigger>
        ))}
      </TabsList>

      {data?.map((item: any) => (
        <TabsContent
          key={item.id}
          className="max-w-[calc(100%-60px)] w-full mt-0"
          value={`step_${item.id}`}
        >
          <div
            data-aos="fade-up"
            className="wrapper flex justify-start items-start gap-[56px] max-lg:gap-[30px] max-md:flex-col"
          >
            <div className="image max-w-[540px] w-full relative pl-[130px] max-lg:pl-[90px] max-md:pr-[60px] max-md:pl-0 max-md:max-w-[360px]">
              <div className="quote_image absolute top-0 left-0 z-[9] max-md:left-[unset] max-md:right-0 max-md:max-w-[50px] max-md:w-full">
                <svg
                  className="max-md:w-full"
                  width="80"
                  height="60"
                  viewBox="0 0 80 60"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M25 10L30 0H20C8.95 0 0 13.95 0 25V60H35V25H15C15 10 25 10 25 10ZM60 25C60 10 70 10 70 10L75 0H65C53.95 0 45 13.95 45 25V60H80V25H60Z"
                    fill="#008BD2"
                  />
                </svg>
              </div>
              <Image
                className="max-w-[400px] w-full object-cover rounded-[20px] aspect-[400/560] max-md:max-w-[280px]"
                src={
                  item.User_Image?.url
                    ? `${process.env.NEXT_PUBLIC_API_BASE_URL}${item.User_Image.url}`
                    : "/vijaywada.png"
                }
                alt="member image"
                width={346}
                height={446}
              />
            </div>
            <div className="content max-w-[544px] w-full max-md:max-w-full">
              <p className="text-[20px] font-normal text-[#141414] max-md:text-[18px] leading-[32px] max-lg:text-[15px] max-lg:leading-[24px] max-sm:text-[14px]">
                {item.Quote}
              </p>

              <Dialog>
                <DialogTrigger className="outline-none w-full h-full text-start">
                  <span className="text-[20px] font-semibold text-[#008BD2] mt-2 max-md:text-[16px]">
                    Read More
                  </span>
                </DialogTrigger>

                <DialogContent className="bg-white max-w-[1223px] h-[567px] p-0 w-full border-none rounded-[20px] max-md:max-w-[550px] timeline_modal">
                  <DialogTitle></DialogTitle>
                  <DialogDescription>
                    <Tabs
                      defaultValue={`step_${item.id}`}
                      className="w-full flex flex-row gap-[50px] p-[50px] inear_tab items-center max-sm:gap-[20px] h-[567px] max-md:p-[20px]"
                    >
                      <TabsList className="flex-col items-start w-[33px]">
                        {data?.map((tabItem: any, tabIndex: number) => (
                          <TabsTrigger
                            key={tabItem.id}
                            className="text-[12px] font-bold p-[0] text-[#BEBEBE] pl-[7px] relative items-start bg-transparent"
                            value={`step_${tabItem.id}`}
                          >
                            {" "}
                            <span className="w-[2px] h-[70px] bg-[#BEBEBE] mr-[7px]"></span>{" "}
                            {String(tabIndex + 1).padStart(2, "0")}
                          </TabsTrigger>
                        ))}
                      </TabsList>

                      {data?.map((contentItem: any) => (
                        <TabsContent
                          key={contentItem.id}
                          className="max-w-[calc(100%-60px)] w-full mt-0"
                          value={`step_${contentItem.id}`}
                        >
                          <div
                            data-aos="fade-up"
                            className="wrapper flex justify-start items-start gap-[56px] max-lg:gap-[30px] max-md:flex-col max-md:overflow-y-auto max-md:h-[460px]"
                          >
                            <div className="image max-w-[450px] w-full relative pl-[100px] max-lg:pl-[90px] max-md:pl-[45px] max-md:max-w-[360px]">
                              <div className="quote_image absolute top-0 left-0 z-[9] max-md:left-0 max-md:max-w-[35px] max-md:w-full">
                                <svg
                                  className="w-full"
                                  width="80"
                                  height="60"
                                  viewBox="0 0 80 60"
                                  fill="none"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path
                                    d="M25 10L30 0H20C8.95 0 0 13.95 0 25V60H35V25H15C15 10 25 10 25 10ZM60 25C60 10 70 10 70 10L75 0H65C53.95 0 45 13.95 45 25V60H80V25H60Z"
                                    fill="#008BD2"
                                  />
                                </svg>
                              </div>
                              <Image
                                className="max-w-[346px] w-full object-cover rounded-[20px] aspect-[346/466] max-md:max-w-[280px]"
                                src={
                                  contentItem.User_Image?.url
                                    ? `${process.env.NEXT_PUBLIC_API_BASE_URL}${contentItem.User_Image.url}`
                                    : "/vijaywada.png"
                                }
                                alt="member image"
                                width={346}
                                height={446}
                              />
                            </div>
                            <div className="content max-w-[544px] w-full max-md:max-w-full overflow-y-auto max-h-[467px] max-md:h-auto max-md:overflow-y-visible">
                              <p className="text-[20px] font-normal text-[#141414] max-md:text-[18px] leading-[32px] max-lg:text-[15px] max-lg:leading-[24px] max-sm:text-[14px]">
                                {contentItem.Quote}
                              </p>
                              <div className="member_details flex justify-start items-start flex-col mt-[24px] max-md:mt-[15px]">
                                <h5 className="text-[20px] font-semibold text-[#141414] leading-[25px] max-md:text-[16px]">
                                  {contentItem.Name}
                                </h5>
                                <h6 className="text-[16px] font-normal leading-[25px] text-[#484848] max-md:text-[14px]">
                                  {contentItem.Designation}
                                </h6>
                              </div>
                              <div className="member_details flex justify-start items-start mt-[20px] max-md:mt-[10px] max-lg:flex-col">
                                <h5 className="text-[20px] font-semibold text-[#141414] leading-[25px] max-md:text-[16px]">
                                  Region :{" "}
                                </h5>
                                <h6 className="text-[16px] font-normal leading-[25px] text-[#484848] ml-[5px] max-md:text-[14px]">
                                  {" "}
                                  {contentItem.Region}
                                </h6>
                              </div>
                              <div className="member_details flex justify-start items-start mt-[20px] max-md:mt-[10px] max-lg:flex-col">
                                <h5 className="text-[20px] font-semibold text-[#141414] leading-[25px] max-md:text-[16px]">
                                  Location :{" "}
                                </h5>
                                <h6 className="text-[16px] font-normal leading-[25px] text-[#484848] ml-[5px] max-md:text-[14px]">
                                  {" "}
                                  {contentItem.Branch_Name}
                                </h6>
                              </div>
                              <div className="quote mt-[32px] max-md:max-w-[35px] max-md:mt-[18px] max-md:w-full">
                                <svg
                                  className="max-md:w-full"
                                  width="80"
                                  height="60"
                                  viewBox="0 0 80 60"
                                  fill="none"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path
                                    d="M45 25V60H80V25C80 14 71 0 60 0H50L55 10C55 10 65 10 65 25H45ZM0 25V60H35V25C35 14 26 0 15 0H5L10 10C10 10 20 10 20 25H0Z"
                                    fill="#008BD2"
                                  />
                                </svg>
                              </div>
                            </div>
                          </div>
                        </TabsContent>
                      ))}
                    </Tabs>
                  </DialogDescription>
                </DialogContent>
              </Dialog>

              <div className="member_details flex justify-start items-start flex-col mt-[24px] max-md:mt-[15px]">
                <h5 className="text-[20px] font-semibold text-[#141414] leading-[25px] max-md:text-[16px]">
                  {item.Name}
                </h5>
                <h6 className="text-[16px] font-normal leading-[25px] text-[#484848] max-md:text-[14px]">
                  {item.Designation}
                </h6>
              </div>
              <div className="member_details flex justify-start items-start mt-[20px] max-md:mt-[10px]">
                <h5 className="text-[20px] font-semibold text-[#141414] leading-[25px] max-md:text-[16px]">
                  Region :{" "}
                </h5>
                <h6 className="text-[16px] font-normal leading-[25px] text-[#484848] ml-[5px] max-md:text-[14px]">
                  {" "}
                  {item.Region}
                </h6>
              </div>
              <div className="member_details flex justify-start items-start mt-[20px] max-md:mt-[10px]">
                <h5 className="text-[20px] font-semibold text-[#141414] leading-[25px] max-md:text-[16px]">
                  Location :{" "}
                </h5>
                <h6 className="text-[16px] font-normal leading-[25px] text-[#484848] ml-[5px] max-md:text-[14px]">
                  {" "}
                  {item.Branch_Name}
                </h6>
              </div>
              <div className="quote mt-[32px] max-md:max-w-[50px] max-md:mt-[18px] max-md:w-full">
                <svg
                  className="max-md:w-full"
                  width="80"
                  height="60"
                  viewBox="0 0 80 60"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M45 25V60H80V25C80 14 71 0 60 0H50L55 10C55 10 65 10 65 25H45ZM0 25V60H35V25C35 14 26 0 15 0H5L10 10C10 10 20 10 20 25H0Z"
                    fill="#008BD2"
                  />
                </svg>
              </div>
            </div>
          </div>
        </TabsContent>
      ))}
    </Tabs>
  );
}
