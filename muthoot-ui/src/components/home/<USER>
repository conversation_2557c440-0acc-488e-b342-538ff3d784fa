import React from "react";
import CommonText from "../common/CommonText";
import Image from "next/image";
import ProductBoxHome from "./ProductBoxHome";

const ProductSection = ({ data, productData }: any) => {
  return (
    <section className="financial_needs_section bg-[#008BD2] py-[100px] relative max-md:py-[50px] overflow-hidden ">
      <div className="container">
        <h2
          data-aos="fade-up"
          className="text-[44px] max-w-[577px] w-full mx-auto text-center font-semibold text-white max-md:text-[30px] max-md:leading-[40px]"
        >
          <CommonText title={data?.Title} />
        </h2>

        <div className="masked_srk max-w-[756px] w-full absolute top-0 right-0">
          <Image
            className="max-w-full w-full  object-contain"
            src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${data.SRK_Image?.url}`}
            alt="srk"
            width={756}
            height={1081}
          />
        </div>

        <ProductBoxHome data={productData} />
      </div>
    </section>
  );
};

export default ProductSection;
