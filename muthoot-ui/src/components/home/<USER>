import Image from "next/image";
import Link from "next/link";
import React from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

// Helper function to extract text from Description array
const getDescriptionText = (description: any[]) => {
  if (!description || !Array.isArray(description)) return "";

  // Find the first paragraph type item
  const paragraph = description.find((item) => item.type === "paragraph");
  if (paragraph?.children?.[0]?.text) {
    // Return truncated text (first 100 characters)
    return paragraph.children[0].text.substring(0, 100) + "...";
  }
  return "";
};

// Helper function to render rich text content for dialog
const renderRichText = (description: any) => {
  // If description is an array (structured content)
  if (Array.isArray(description)) {
    return (
      <div>
        {description.map((item: any, index: number) => {
          if (item.type === "paragraph" && item.children) {
            return (
              <p
                key={index}
                className="font-normal text-[16px] text-[#3F3F3F] leading-[22px] mb-[16px]"
              >
                {item.children
                  .map((child: any, childIndex: number) => child.text)
                  .join("")}
              </p>
            );
          }
          if (item.type === "list" && item.children) {
            return (
              <ul key={index} className="mt-[20px] pl-[20px] mb-[16px]">
                {item.children.map((listItem: any, listIndex: number) => (
                  <li
                    key={listIndex}
                    className="font-normal list-disc text-[16px] text-[#3F3F3F] mt-[10px] leading-[22px]"
                  >
                    {listItem.children
                      ?.map((child: any) => child.text)
                      .join("") || ""}
                  </li>
                ))}
              </ul>
            );
          }
          return null;
        })}
      </div>
    );
  }

  // If description is HTML string
  if (typeof description === "string") {
    try {
      const formattedHTML = description
        .replace(/<ul>/g, '<ul class="mt-[20px] pl-[20px]">')
        .replace(
          /<li>/g,
          '<li class="font-normal list-disc text-[16px] text-[#3F3F3F] mt-[10px] leading-[22px]">'
        );

      return (
        <div
          dangerouslySetInnerHTML={{
            __html: formattedHTML,
          }}
        />
      );
    } catch (error) {
      console.error("Error processing rich text:", error);
      return (
        <div>
          <p className="font-normal text-[16px] text-[#3F3F3F] leading-[22px]">
            Error loading description.
          </p>
        </div>
      );
    }
  }

  return (
    <div>
      <p className="font-normal text-[16px] text-[#3F3F3F] leading-[22px]">
        No description available.
      </p>
    </div>
  );
};

const DigitalInitiativeHome = ({ data }: any) => {
  return (
    <div className="grid_wrapper flex justify-start items-start gap-[21px] flex-wrap max-md:justify-center ">
      {data && data.length > 0 ? (
        data.map((item: any, index: number) => (
          <Dialog key={item.id}>
            <DialogTrigger className="grid_box cursor-pointer w-[calc(100%/3-14px)] max-md:w-[calc(100%/2-11px)] max-sm:max-w-[420px] max-sm:w-full max-sm:mx-auto">
              <div data-aos="fade-up">
                <div className="grid_image overflow-hidden rounded-[20px] aspect-square">
                  <Image
                    className="w-full object-cover aspect-square rounded-[20px]"
                    src={
                      item.Thumbnail?.url
                        ? `${process.env.NEXT_PUBLIC_API_BASE_URL}${item.Thumbnail.url}`
                        : "/digital_1.png"
                    }
                    alt="digital initiative image"
                    width={1000}
                    height={1000}
                  />
                </div>
                <h6 className="text-[16px] flex justify-center leading-[24px] text-[#141414] font-semibold mt-[14px] max-md:text-[14px] max-md:leading-[22px] line-clamp-2">
                  {item.Title}
                </h6>
              </div>
            </DialogTrigger>

            <DialogContent className="max-w-[700px] p-[40px]">
              <DialogHeader>
                <h2 className="text-[20px] font-bold text-black">
                  {item?.DialogTitle || item?.title || "Digital Initiative"}
                </h2>
                <DialogDescription className="text-left">
                  {renderRichText(item?.Description)}
                </DialogDescription>
              </DialogHeader>
            </DialogContent>
          </Dialog>
        ))
      ) : (
        <div className="flex justify-center items-center w-full h-full">
          <p className="text-[16px] text-[#484848]">No data available</p>
        </div>
      )}
    </div>
  );
};

export default DigitalInitiativeHome;
