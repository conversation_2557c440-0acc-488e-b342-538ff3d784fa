import React from "react";
import TestimonialHome from "./Testimonials";
import Link from "next/link";
import CommonTextBlue from "../common/CommonTextBlue";
import { GetClientsTestimonial } from "@/lib/api/cms";

export default async function TestimonialSection({ data }: any) {
  const { data: clientsTestimonial } = await GetClientsTestimonial();

  const sortedTestimonials = clientsTestimonial
    ? [...clientsTestimonial]
        .sort((a, b) => {
          const orderA = a?.TestimonialOrder || 999;
          const orderB = b?.TestimonialOrder || 999;
          return orderA - orderB;
        })
        .slice(0, 4)
    : [];
  return (
    <section
      data-aos="fade-up"
      className="timeline_section bg-[#E7F7FF] py-[100px] mt-[100px] max-md:py-[50px] max-md:mt-[50px]"
    >
      <div className="container">
        <div className="media_head flex justify-between items-end mb-[48px] gap-[30px] max-lg:flex-col max-lg:items-start max-lg:gap-[30px]">
          <div className="title max-w-[860px] w-full">
            <h3
              data-aos="fade-up"
              className="text-[44px] text-[#141414] font-semibold max-md:text-[35px] max-md:leading-[45px]"
            >
              <CommonTextBlue title={data?.Title} makeSplit={true} />
            </h3>
            <p
              data-aos="fade-up"
              className="text-[16px] font-normal text-[#484848] leading-[24px] mt-[16px] max-sm:text-[14px] max-sm:leading-[22px] text-justify"
            >
              {data?.Description}
            </p>
          </div>
          <div
            data-aos="fade-up"
            className="view_wrapper max-w-[120px] w-full max-lg:max-w-full  max-lg:w-full max-lg:flex max-lg:justify-end"
          >
            <Link
              className="text-[16px] font-bold text-[#008BD2] flex justify-end items-center gap-[13px]"
              href={data?.Button?.Link}
            >
              {data?.Button?.Label}
              <svg
                width="8"
                height="14"
                viewBox="0 0 8 14"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M1 13L7 7L1 1"
                  stroke="#008BD2"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>{" "}
            </Link>
          </div>
        </div>

        <TestimonialHome data={sortedTestimonials} />
      </div>
    </section>
  );
}
