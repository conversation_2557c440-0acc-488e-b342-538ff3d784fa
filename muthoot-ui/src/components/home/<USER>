import { Checkbox } from "@radix-ui/react-checkbox";
import Image from "next/image";
import React from "react";

import Button from "@/components/ui/Button";
import CommonTextBlue from "../common/CommonTextBlue";
import GetinTouchForm from "../form/GetinTouchForm";

const HomeForm = ({ data }: any) => {
  return (
    <section className="getin_touch pt-[100px] bg-[#E7F7FF] pb-[100px] mt-[100px] max-md:mt-[50px] relative max-xl:pb-0 max-md:pt-[60px]">
      <div className="container">
        <div className="get_wrapper flex justify-start items-start max-xl:flex-col">
          <div className="content max-w-[367px] w-full max-xl:max-w-full">
            <h3
              data-aos="fade-down"
              className="text-[44px] font-semibold text-[#141414] leading-normal max-md:text-[35px] max-md:leading-[45px] "
            >
              <CommonTextBlue title={data?.Title} />
            </h3>
            <p
              data-aos="fade-down"
              className="text-[16px] leading-[24px] text-[#484848] font-normal mt-[26px] text-justify max-xl:mt-[12px] max-md:text-[14px]"
            >
              {data?.Description}
            </p>
            <h4
              data-aos="fade-down"
              className="mt-[42px] text-[24px] leading-[33px] text-[#141414] font-normal max-xl:mt-[15px] max-sm:text-[20px]"
            >
              <CommonTextBlue title={data?.Privacy_Text} />
              {data?.Mail}
            </h4>
          </div>

          <GetinTouchForm />
        </div>
      </div>
      <div
        data-aos="fade-down"
        className="contact_banner absolute bottom-0 right-0 max-w-[389px] w-full max-xl:relative max-xl:hidden"
      >
        <Image
          className=" w-full h-full object-contain aspect-[389/605] "
          src={process.env.NEXT_PUBLIC_API_BASE_URL + data?.SRK_Image?.url}
          alt="contact banner"
          width={389}
          height={605}
        />
      </div>
      {/* <ServiceForm /> */}
    </section>
  );
};

export default HomeForm;
