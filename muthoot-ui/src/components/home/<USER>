import Image from "next/image";
import Link from "next/link";
import React from "react";

export default function CsrHome({ data }: any) {
  return (
    <div className="grid_wrapper flex justify-start items-start gap-[21px] flex-wrap max-md:justify-center ">
      {data && data.length > 0 ? (
        data?.map((item: any, index: number) => (
          <div
            key={item.id}
            data-aos="fade-up"
            className="grid_box cursor-pointer w-[calc(100%/4-16px)] max-md:w-[calc(100%/2-11px)] max-sm:max-w-[420px] max-sm:w-full max-sm:mx-auto "
          >
            <Link href={`/csr/${item.documentId}`}>
              <div className="grid_image aspect-square ">
                <Image
                  className=" w-full object-cover aspect-square rounded-[20px]"
                  src={
                    item.Thumbnail?.url
                      ? `${process.env.NEXT_PUBLIC_API_BASE_URL}${item.Thumbnail.url}`
                      : "/csr1.png"
                  }
                  alt="csr image"
                  width={411}
                  height={411}
                />
              </div>
              <h6 className="text-[16px] text-center leading-[24px] text-[##141414] font-semibold mt-[14px] max-md:text-[14px] max-md:leading-[22px] line-clamp-2">
                {item.Title ||
                  "Donation of Shoes & Socks to 100 Needy & Underprivileged Children at Munirka, New Delhi"}
              </h6>
            </Link>
          </div>
        ))
      ) : (
        <>
          <div
            data-aos="fade-up"
            className="grid_box w-[calc(100%/4-16px)] max-md:w-[calc(100%/2-11px)] max-sm:max-w-[420px] max-sm:w-full max-sm:mx-auto "
          >
            <div className="grid_image aspect-square ">
              <Image
                className=" w-full object-cover aspect-square rounded-[20px]"
                src="/csr1.png"
                alt="csr image"
                width={411}
                height={411}
              />
            </div>
            <h6 className="text-[16px] leading-[24px] text-[##141414] font-semibold mt-[14px] max-md:text-[14px] max-md:leading-[22px] line-clamp-2">
              Donation of Shoes & Socks to 100 Needy & Underprivileged Children
              at Munirka, New Delhi
            </h6>
          </div>
          <div
            data-aos="fade-up"
            className="grid_box w-[calc(100%/3-14px)] max-md:w-[calc(100%/2-11px)] max-sm:max-w-[420px] max-sm:w-full max-sm:mx-auto "
          >
            <div className="grid_image aspect-square ">
              <Image
                className=" w-full object-cover aspect-square rounded-[20px]"
                src="/csr1.png"
                alt="csr image"
                width={411}
                height={411}
              />
            </div>
            <h6 className="text-[16px] leading-[24px] text-[##141414] font-semibold mt-[14px] max-md:text-[14px] max-md:leading-[22px] line-clamp-2">
              Donation of Shoes & Socks to 100 Needy & Underprivileged Children
              at Munirka, New Delhi
            </h6>
          </div>
          <div
            data-aos="fade-up"
            className="grid_box w-[calc(100%/3-14px)] max-md:w-[calc(100%/2-11px)] max-sm:max-w-[420px] max-sm:w-full max-sm:mx-auto "
          >
            <div className="grid_image aspect-square ">
              <Image
                className=" w-full object-cover aspect-square rounded-[20px]"
                src="/csr1.png"
                alt="csr image"
                width={411}
                height={411}
              />
            </div>
            <h6 className="text-[16px] leading-[24px] text-[##141414] font-semibold mt-[14px] max-md:text-[14px] max-md:leading-[22px] line-clamp-2">
              Donation of Shoes & Socks to 100 Needy & Underprivileged Children
              at Munirka, New Delhi
            </h6>
          </div>
        </>
      )}
    </div>
  );
}
