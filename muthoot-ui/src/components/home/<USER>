"use client";

import React, { useEffect, useState, useRef } from "react";
import { useCallback, useMemo } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, EffectFade, Navigation } from "swiper/modules";
import type { Swiper as SwiperType } from "swiper";
import "swiper/css";
import "swiper/css/pagination";
import "swiper/css/effect-fade";
import Image from "next/image";

export default function BannerSlider({ data, careersData }: any) {
  console.log(data);
  const [mounted, setMounted] = useState(false);
  const [isMuted, setIsMuted] = useState(true);
  const [currentSlideHasVideo, setCurrentSlideHasVideo] = useState(false);
  const [activeSlideIndex, setActiveSlideIndex] = useState(0);
  const [mobileActiveSlideIndex, setMobileActiveSlideIndex] = useState(0);
  const swiperRef = useRef<SwiperType | null>(null);
  const videoRefs = useRef<(HTMLVideoElement | null)[]>([]);
  const mobileSwiperRef = useRef<SwiperType | null>(null);
  const useBannerCache = () => {
    const [imageCache, setImageCache] = useState(new Map());
    const [videoCache, setVideoCache] = useState(new Map());

    const cacheImage = useCallback(
      (url: string) => {
        if (imageCache.has(url)) return;

        const img = new window.Image();
        img.onload = () => {
          setImageCache((prev) => new Map(prev).set(url, img));
        };
        img.src = url;
      },
      [imageCache]
    );

    const cacheVideo = useCallback(
      (url: string) => {
        if (videoCache.has(url)) return;

        const video = document.createElement("video");
        video.preload = "metadata";
        video.onloadedmetadata = () => {
          setVideoCache((prev) => new Map(prev).set(url, video));
        };
        video.src = url;
      },
      [videoCache]
    );

    return { imageCache, videoCache, cacheImage, cacheVideo };
  };

  const { imageCache, videoCache, cacheImage, cacheVideo } = useBannerCache();

  const toggleMute = () => {
    setIsMuted(!isMuted);
    videoRefs.current.forEach((video) => {
      if (video) video.muted = !isMuted;
    });
  };

  useEffect(() => {
    setMounted(true);
    if (data) {
      videoRefs.current = data?.map(() => null);

      // Cache all banner media on mount
      data?.forEach((slide: any) => {
        const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;

        if (slide.ImageOrVideo?.url) {
          const fullUrl = `${baseUrl}${slide.ImageOrVideo.url}`;
          if (slide.ImageOrVideo.mime?.includes("video")) {
            cacheVideo(fullUrl);
          } else {
            cacheImage(fullUrl);
          }
        }

        if (slide.mobileBanner?.url) {
          const fullUrl = `${baseUrl}${slide.mobileBanner.url}`;
          if (slide.mobileBanner.mime?.includes("video")) {
            cacheVideo(fullUrl);
          } else {
            cacheImage(fullUrl);
          }
        }
      });
    }
  }, [data, cacheImage, cacheVideo]);

  const hasVideoSlides = data?.some((slide: any) =>
    slide.ImageOrVideo?.mime?.includes("video")
  );

  const handleVideoEnded = () => {
    if (swiperRef.current) {
      swiperRef.current.slideNext();
    }
  };

  const renderMedia = useCallback(
    (media: any, index?: number, isMobile = false) => {
      if (!media) return null;

      const fullUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}${media.url}`;

      if (media.mime?.includes("video")) {
        return (
          <video
            ref={
              !isMobile && index !== undefined
                ? (el) => {
                    videoRefs.current[index] = el;
                  }
                : undefined
            }
            className="w-full h-full object-cover object-top"
            src={fullUrl}
            autoPlay
            muted={isMuted}
            playsInline
            preload="metadata"
            onEnded={
              !isMobile && index !== undefined ? handleVideoEnded : undefined
            }
          />
        );
      } else {
        // Use Next.js Image component for optimal performance
        return (
          <div className="relative w-full h-full">
            <Image
              src={fullUrl}
              alt={`Banner slide ${index !== undefined ? index + 1 : ""}`}
              fill
              className="object-cover object-top"
              sizes={isMobile ? "100vw" : "(max-width: 768px) 100vw, 100vw"}
              priority={index === 0}
              quality={75}
            />
          </div>
        );
      }
    },
    [isMuted, handleVideoEnded]
  );

  const mobileBannerSlides = useMemo(
    () => data?.filter((slide: any) => slide.mobileBanner) || [],
    [data]
  );
  return (
    <div className="relative">
      <div
        data-aos="fade-down"
        className="certification max-w-[100px] w-full absolute left-[80px] z-10"
      >
        {/* Certification image commented out */}
      </div>

      {/* Desktop Swiper - Only visible on desktop */}
      <div className="hidden md:block">
        <Swiper
          modules={[Autoplay, EffectFade, Navigation]}
          speed={1200}
          autoplay={{
            delay: 5000,
            disableOnInteraction: false,
            pauseOnMouseEnter: false,
          }}
          effect="fade"
          fadeEffect={{ crossFade: true }}
          spaceBetween={0}
          slidesPerView={1}
          loop={true}
          navigation={{
            nextEl: ".swiper-button-next",
            prevEl: ".swiper-button-prev",
          }}
          className="mySwiper"
          onSwiper={(swiper) => {
            swiperRef.current = swiper;
            // Initialize with the first slide
            setActiveSlideIndex(0);
          }}
          onSlideChange={(swiper) => {
            // Update active slide index
            setActiveSlideIndex(swiper.realIndex);

            // Pause all videos when slide changes
            videoRefs.current.forEach((video) => {
              if (video) video.pause();
            });

            const currentVideo = videoRefs.current[swiper.realIndex];
            const currentSlide = data?.[swiper.realIndex];

            if (
              currentVideo &&
              currentSlide?.ImageOrVideo?.mime?.includes("video")
            ) {
              currentVideo.currentTime = 0;
              currentVideo.play();
              swiper.autoplay.stop();
              setCurrentSlideHasVideo(true);
            } else {
              swiper.autoplay.start();
              setCurrentSlideHasVideo(false);
            }
          }}
        >
          {data?.map((slide: any, index: number) => (
            <SwiperSlide key={index} className="!h-[revert] bg-[#008bd2]">
              <div className="home_banner home_bannershade bg-[#008BD2] h-[550px] relative max-lg:h-[750px]">
                {slide.ImageOrVideo?.mime?.includes("video") ? (
                  <div className="bannervideo_wrapper h-full">
                    {renderMedia(slide.ImageOrVideo, index)}

                    <div className="container absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 flex justify-center items-center gap-[50%] max-lg:justify-start">
                      <div
                        data-aos="fade-up"
                        className="banner_videocontents max-w-[560px] w-full max-lg:max-w-[85%]"
                      >
                        <h1 className="text-[44px] font-semibold text-white max-md:text-[35px] line-clamp-3">
                          {slide.Title?.split("(").length > 1 ? (
                            <>
                              {slide.Title?.split("(")[0]}{" "}
                              <span className="text-[#FECB05]">
                                {slide.Title?.split("(")[1]?.split(")")[0]}
                              </span>{" "}
                              {slide.Title?.split(")")[1] || ""}
                            </>
                          ) : (
                            slide.Title
                          )}
                        </h1>
                        <p className="text-[16px] font-normal text-white mt-[16px] leading-[24px] max-md:text-[14px] line-clamp-2">
                          {slide.Description}
                        </p>
                      </div>
                      <div className="side_image self-end max-lg:self-center max-md:mt-[40px] opacity-0 max-lg:hidden">
                        {/* Side image commented out */}
                      </div>
                    </div>
                    {index === activeSlideIndex &&
                      slide.ImageOrVideo?.mime?.includes("video") && (
                        <button
                          className="absolute top-4 right-4 w-[40px] h-[40px]  rounded-full flex items-center justify-center transition-transform hover:scale-110 z-[999]"
                          onClick={toggleMute}
                        >
                          {isMuted ? (
                            <svg
                              width="24"
                              height="24"
                              viewBox="0 0 24 24"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M3.63 3.63C3.24 4.02 3.24 4.65 3.63 5.04L7.29 8.7L7 9H4C3.45 9 3 9.45 3 10V14C3 14.55 3.45 15 4 15H7L10.29 18.29C10.92 18.92 12 18.47 12 17.58V13.41L16.18 17.59C15.69 17.96 15.16 18.27 14.58 18.5C14.22 18.65 14 19.03 14 19.42C14 20.14 14.73 20.6 15.39 20.33C16.19 20 16.94 19.56 17.61 19.02L18.95 20.36C19.34 20.75 19.97 20.75 20.36 20.36C20.75 19.97 20.75 19.34 20.36 18.95L5.05 3.63C4.66 3.24 4.03 3.24 3.63 3.63ZM19 12C19 12.82 18.85 13.61 18.59 14.34L20.12 15.87C20.68 14.7 21 13.39 21 12C21 8.17 18.6 4.89 15.22 3.6C14.63 3.37 14 3.83 14 4.46V4.65C14 5.03 14.25 5.36 14.61 5.5C17.18 6.54 19 9.06 19 12ZM10.29 5.71L10.12 5.88L12 7.76V6.41C12 5.52 10.92 5.08 10.29 5.71ZM16.5 12C16.5 10.23 15.48 8.71 14 7.97V9.76L16.48 12.24C16.49 12.16 16.5 12.08 16.5 12Z"
                                fill="#209FDA"
                              />
                            </svg>
                          ) : (
                            <svg
                              width="24"
                              height="24"
                              viewBox="0 0 24 24"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M3 10V14C3 14.55 3.45 15 4 15H7L10.29 18.29C10.92 18.92 12 18.47 12 17.58V6.41C12 5.52 10.92 5.07 10.29 5.7L7 9H4C3.45 9 3 9.45 3 10ZM16.5 12C16.5 10.23 15.48 8.71 14 7.97V16.02C15.48 15.29 16.5 13.77 16.5 12ZM14 4.45V4.65C14 5.03 14.25 5.36 14.61 5.5C17.18 6.53 19 9.06 19 12C19 14.94 17.18 17.47 14.61 18.5C14.25 18.64 14 18.97 14 19.35V19.55C14 20.18 14.63 20.62 15.21 20.4C18.6 19.11 21 15.84 21 12C21 8.16 18.6 4.89 15.21 3.6C14.63 3.37 14 3.82 14 4.45Z"
                                fill="#209FDA"
                              />
                            </svg>
                          )}
                        </button>
                      )}
                  </div>
                ) : (
                  <>
                    <div
                      className="h-full w-full"
                      style={{
                        backgroundImage: `url(${process.env.NEXT_PUBLIC_API_BASE_URL}${slide.ImageOrVideo.url})`,
                        backgroundSize: "cover",
                        backgroundPosition: "top",
                      }}
                    ></div>
                    <div className="container absolute top-0 left-1/2 transform -translate-x-1/2 flex justify-center gap-[60px] relative max-lg:flex-col max-lg:justify-center max-lg:gap-[50px] max-lg:h-full">
                      <div className="content max-w-[620px] w-full flex flex-col items-center justify-center pt-[70px] pb-[160px] max-lg:pb-0">
                        <h1
                          data-aos="fade-up"
                          className="text-[44px] font-semibold text-white max-md:text-[35px] line-clamp-3"
                        >
                          {slide.Title?.split("(").length > 1 ? (
                            <>
                              {slide.Title?.split("(")[0]}{" "}
                              <span className="text-[#FECB05]">
                                {slide.Title?.split("(")[1]?.split(")")[0]}
                              </span>{" "}
                              {slide.Title?.split(")")[1] || ""}
                            </>
                          ) : (
                            slide.Title
                          )}
                        </h1>
                        <p className="text-[16px] font-normal text-white mt-[16px] leading-[24px] max-md:text-[14px] line-clamp-2">
                          {slide.Description}
                        </p>
                      </div>
                      <div className="side_image self-end max-lg:self-center">
                        {/* Side image can be added here if needed */}
                      </div>
                    </div>
                  </>
                )}
              </div>
            </SwiperSlide>
          ))}
          <div className="banner_prevwrapper max-lg:hidden">
            <div className="swiper-button-prev custom-nav-button banner_prev" />
          </div>
          <div className="banner_nextwrapper max-lg:hidden">
            <div className="swiper-button-next custom-nav-button banner_next" />
          </div>
        </Swiper>
      </div>

      {/* Mobile Swiper - Only visible on mobile and only shows slides with mobile banners */}
      <div className="block md:hidden">
        <Swiper
          modules={[Autoplay, EffectFade, Navigation]}
          speed={1200}
          autoplay={{
            delay: 5000,
            disableOnInteraction: false,
            pauseOnMouseEnter: false,
          }}
          effect="fade"
          fadeEffect={{ crossFade: true }}
          spaceBetween={0}
          slidesPerView={1}
          loop={mobileBannerSlides?.length > 1}
          navigation={mobileBannerSlides?.length > 1}
          className="mySwiper"
          onSwiper={(swiper) => {
            mobileSwiperRef.current = swiper;
            // Initialize with the first slide
            setMobileActiveSlideIndex(0);
          }}
          onSlideChange={(swiper) => {
            // Update mobile active slide index
            setMobileActiveSlideIndex(swiper.realIndex);

            const currentSlide = mobileBannerSlides?.[swiper.realIndex];
            setCurrentSlideHasVideo(
              currentSlide?.mobileBanner?.mime?.includes("video") || false
            );
          }}
        >
          {mobileBannerSlides?.map((slide: any, index: number) => (
            <SwiperSlide key={index} className="!h-[revert] bg-[#008bd2]">
              <div className="home_banner bg-[#008BD2] h-[600px] relative max-md:h-[600px]">
                {renderMedia(slide.mobileBanner, undefined, true)}
                <div className="container absolute top-0 left-1/2 transform -translate-x-1/2 flex justify-center gap-[55px] relative max-lg:flex-col max-lg:justify-center max-lg:gap-[45px] max-md:gap-0 max-lg:h-full">
                  <div className="content max-w-[620px] w-full flex flex-col items-center justify-center pt-[80px] pb-[100px] max-lg:pb-0 max-sm:pt-[70px]">
                    <h1
                      data-aos="fade-up"
                      className="text-[40px] font-semibold text-white max-md:text-[34px] line-clamp-3"
                    >
                      {slide.Title?.split("(").length > 1 ? (
                        <>
                          {slide.Title?.split("(")[0]}{" "}
                          <span className="text-[#FECB05]">
                            {slide.Title?.split("(")[1]?.split(")")[0]}
                          </span>{" "}
                          {slide.Title?.split(")")[1] || ""}
                        </>
                      ) : (
                        slide.Title
                      )}
                    </h1>
                    <p className="text-[17px] font-normal text-white mt-[16px] leading-[26px] max-md:text-[15px] line-clamp-2">
                      {slide.Description}
                    </p>
                  </div>
                </div>
                {index === mobileActiveSlideIndex &&
                  slide.mobileBanner?.mime?.includes("video") && (
                    <button
                      className="absolute bottom-4 right-4 w-[50px] h-[50px] bg-white/80 rounded-full flex items-center justify-center transition-transform hover:scale-110 z-[999]"
                      onClick={toggleMute}
                    >
                      {isMuted ? (
                        <svg
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M3.63 3.63C3.24 4.02 3.24 4.65 3.63 5.04L7.29 8.7L7 9H4C3.45 9 3 9.45 3 10V14C3 14.55 3.45 15 4 15H7L10.29 18.29C10.92 18.92 12 18.47 12 17.58V13.41L16.18 17.59C15.69 17.96 15.16 18.27 14.58 18.5C14.22 18.65 14 19.03 14 19.42C14 20.14 14.73 20.6 15.39 20.33C16.19 20 16.94 19.56 17.61 19.02L18.95 20.36C19.34 20.75 19.97 20.75 20.36 20.36C20.75 19.97 20.75 19.34 20.36 18.95L5.05 3.63C4.66 3.24 4.03 3.24 3.63 3.63Z"
                            fill="#209FDA"
                          />
                        </svg>
                      ) : (
                        <svg
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M3 10V14C3 14.55 3.45 15 4 15H7L10.29 18.29C10.92 18.92 12 18.47 12 17.58V6.41C12 5.52 10.92 5.07 10.29 5.7L7 9H4C3.45 9 3 9.45 3 10ZM16.5 12C16.5 10.23 15.48 8.71 14 7.97V16.02C15.48 15.29 16.5 13.77 16.5 12ZM14 4.45V4.65C14 5.03 14.25 5.36 14.61 5.5C17.18 6.53 19 9.06 19 12C19 14.94 17.18 17.47 14.61 18.5C14.25 18.64 14 18.97 14 19.35V19.55C14 20.18 14.63 20.62 15.21 20.4C18.6 19.11 21 15.84 21 12C21 8.16 18.6 4.89 15.21 3.6C14.63 3.37 14 3.82 14 4.45Z"
                            fill="#209FDA"
                          />
                        </svg>
                      )}
                    </button>
                  )}
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </div>
  );
}
