import Link from "next/link";
import React from "react";
import BlogHome from "./BlogHome";
import { GetBlogsHome } from "@/lib/api/general";

export default async function BlogSection({ data }: any) {
  const { data: blogs } = await GetBlogsHome();

  return (
    <section className="blog_section mb-[100px] max-md:mb-[50px] ">
      <div className="container">
        <div className="media_head flex justify-between items-end mb-[36px] max-lg:flex-col max-lg:items-start max-lg:gap-[30px]">
          <div className="title max-w-[600px] w-full">
            <h3
              data-aos="fade-up"
              className="text-[44px] text-[#141414] font-semibold max-md:text-[35px] max-md:leading-[45px]"
            >
              {data?.Title}
            </h3>
            <p
              data-aos="fade-up"
              className="text-[16px] font-normal text-[#484848] leading-[24px] mt-[16px] text-justify max-sm:text-[14px] max-sm:leading-[22px]"
            >
              {data?.Description}
            </p>
          </div>
          <div
            data-aos="fade-up"
            className="view_wrapper  max-lg:w-full max-lg:flex max-lg:justify-end"
          >
            <Link
              className="text-[16px] font-bold text-[#008BD2] flex justify-start items-center gap-[13px]"
              href={data?.Button?.Link}
            >
              {data?.Button?.Label}
              <svg
                width="8"
                height="14"
                viewBox="0 0 8 14"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M1 13L7 7L1 1"
                  stroke="#008BD2"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>{" "}
            </Link>
          </div>
        </div>
        {blogs?.length > 0 ? <BlogHome data={blogs} /> : <p></p>}
      </div>
    </section>
  );
}
