import React from "react";
import BranchBox from "../contact/BranchBox";
import Link from "next/link";
import CommonTextBlue from "../common/CommonTextBlue";
import { GetBranchHome3 } from "@/lib/api/home";

export default async function BranchHomeSection({ data }: any) {
  const { data: branchHome3 } = await GetBranchHome3();

  return (
    <section className="branch_section pt-[100px] max-md:pt-[50px]">
      <div className="container">
        <div className="media_head flex justify-between items-end mb-[36px] max-lg:flex-col max-lg:items-start max-lg:gap-[30px]">
          <div className="title max-w-[616px] w-full">
            <h3
              data-aos="fade-up"
              className="text-[44px] text-[#141414] font-semibold max-md:text-[35px] max-md:leading-[45px]"
            >
              <CommonTextBlue title={data?.Title} />
            </h3>
          </div>
          <div
            data-aos="fade-down"
            className="view_wrapper  max-lg:w-full max-lg:flex max-lg:justify-end"
          >
            <Link
              className="text-[16px] font-bold text-[#008BD2] flex justify-start items-center gap-[13px]"
              href={data?.Button?.Link}
            >
              {data?.Button?.Label}
              <svg
                width="8"
                height="14"
                viewBox="0 0 8 14"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M1 13L7 7L1 1"
                  stroke="#008BD2"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>{" "}
            </Link>
          </div>
        </div>

        <div className="branch_listwrapper  flex justify-start items-start gap-[28px] mt-[36px] flex-wrap">
          <BranchBox branches={branchHome3} isHome={true} />
        </div>
      </div>
    </section>
  );
}
