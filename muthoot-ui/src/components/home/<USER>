"use client";

import React, { useState, useEffect, useRef } from "react";
import Image from "next/image";

export default function Counter({ data }: any) {
  const [customers, setCustomers] = useState(0);
  const [states, setStates] = useState(0);
  const [assets, setAssets] = useState(0);
  const [rating, setRating] = useState(0);
  const [hasAnimated, setHasAnimated] = useState(false);
  const sectionRef = useRef<HTMLDivElement>(null);

  const animateCount = (
    target: number,
    setState: React.Dispatch<React.SetStateAction<number>>,
    duration: number
  ) => {
    const stepTime = 10; // Milliseconds per step
    const steps = Math.ceil(duration / stepTime);
    const increment = target / steps;
    let current = 0;

    const timer = setInterval(() => {
      current += increment;
      if (current >= target) {
        current = target;
        clearInterval(timer);
      }
      setState(Math.round(current));
    }, stepTime);
  };

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasAnimated) {
          setHasAnimated(true); // Prevent re-animation
          animateCount(7, setCustomers, 6000);
          animateCount(20, setStates, 6000);
          animateCount(2596, setAssets, 6000);
          animateCount(4, setRating, 6000);
        }
      },
      { threshold: 0.5 } // Trigger when 50% of the section is visible
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
    };
  }, [hasAnimated]);

  return (
    <main>
      <div
        ref={sectionRef}
        className="counter_wrapper bg-[#008BD2] rounded-2xl flex justify-between items-center gap-0 mt-[60px] max-xl:flex-wrap max-xl:justify-center max-md:gap-[30px] py-[30px] px-[20px] max-sm:gap-[15px] max-sm:items-start"
      >
        {data?.length > 0 &&
          data?.map((item: any, index: number) => (
            <React.Fragment key={index}>
              <div className="count_box p-[30px] flex flex-col justify-center items-center text-center max-xl:w-[calc(100%/2-25px)] max-md:w-[calc(100%/2-39px)] max-sm:p-[0]">
                <Image
                  className="max-w-[68px] object-contain h-auto aspect-square"
                  src={process.env.NEXT_PUBLIC_API_BASE_URL + item?.Icon?.url}
                  alt={item?.Icon?.name}
                  width={68}
                  height={68}
                />
                <h4 className="text-[40px] font-bold text-[#ffffff] mt-[8px] max-md:text-[30px] max-md:leading-[38px] max-sm:text-[20px]">
                  {item?.Value}
                </h4>
                <h6 className="text-[20px] font-normal text-[#ffffff] mt-[4px] max-md:text-[16px] max-sm:text-[14px] md:whitespace-nowrap overflow-hidden text-ellipsis">
                  {item?.Text}
                </h6>
              </div>
              {index < data?.length - 1 && (
                <div className="separator-container h-[140px] flex items-center">
                  <Image
                    src="/seperator.svg"
                    alt="separator"
                    width={1}
                    height={140}
                    className="mx-2"
                  />
                </div>
              )}
            </React.Fragment>
          ))}
      </div>
    </main>
  );
}
