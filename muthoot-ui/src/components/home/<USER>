import Image from "next/image";
import React from "react";
import Link from "next/link";

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  const day = date.getDate();
  const month = date.toLocaleString("default", { month: "short" });
  const year = date.getFullYear();
  return `${day} ${month} ${year}`;
};

const BlogHome = ({ data }: any) => {
  const blogs = data || [];
  const mainBlog = blogs[0];
  const sideBlogsList = blogs.slice(1, 4);

  return (
    <div className="blog_wrapper flex justify-start items-start gap-[40px] max-lg:20px max-md:flex-col">
      {mainBlog && (
        <div
          data-aos="fade-up"
          className="main_blogs max-w-[660px] w-full max-md:max-w-full max-xl:max-w-[480px] "
        >
          <Link href={`/blogs/${mainBlog.documentId}`}>
            <div className="main_blogImage relative w-full aspect-[600/360] h-[423px] max-lg:h-[260px] ">
              <div className="date text-[12px] font-bold text-white py-[8px] px-[12px] bg-[#008BD2] rounded-[110px] w-fit absolute top-[20px] left-[20px]">
                {formatDate(mainBlog.Date)}
              </div>
              <Image
                className="w-full object-cover aspect-[600/360] h-full rounded-[20px]"
                src={
                  process.env.NEXT_PUBLIC_API_BASE_URL +
                    mainBlog.Thumbnail?.url || "/mainblog.png"
                }
                alt={mainBlog.Title}
                width={660}
                height={360}
              />
            </div>
            <h3 className="text-[20px] leading-[26px] text-[#141414] font-semibold mt-[18px] line-clamp-2 max-lg:text-[18px]">
              {mainBlog.Title}
            </h3>
            <p className="text-[16px] leading-[24px] font-normal text-[#484848] mt-[12px] line-clamp-3 max-lg:text-[14px]">
              {mainBlog.Description?.[0]?.children?.[0]?.text?.substring(
                0,
                150
              ) + "..."}
            </p>
          </Link>
        </div>
      )}
      <div className="side_blogs max-w-[calc(100%-700px)] max-xl:max-w-[650px] w-full max-md:max-w-full ">
        {sideBlogsList.map((blog: any, index: any) => (
          <div
            key={blog.id}
            data-aos="fade-up"
            className={`single_blog flex justify-start items-start gap-[30px] ${
              index > 0 ? "mt-[37px]" : ""
            } max-lg:gap-[18px]`}
          >
            <div className="Image max-w-[210px] w-full aspect-[210/148] h-[148px] max-lg:max-w-[140px] max-lg:h-[120px]">
              <Image
                className="w-full object-cover aspect-[210/148] h-full rounded-[20px]"
                src={
                  process.env.NEXT_PUBLIC_API_BASE_URL + blog.Thumbnail?.url ||
                  "/sub_blog.png"
                }
                alt={blog.Title}
                width={210}
                height={148}
              />
            </div>
            <div className="content max-w-[calc(100%-210px)] w-full max-lg:max-w-[calc(100%-140px)]">
              <h4 className="text-[16px] font-semibold leading-[24px] text-[#0A0A0A] mt-[15px] line-clamp-3 max-lg:text-[14px] max-lg:leading-[20px]">
                {blog.Title}
              </h4>
              <Link href={`/blogs/${blog.documentId}`}>
                <h6 className="text-[18px] font-semibold leading-[28px] text-[#008BD2] mt-[19px] max-lg:text-[16px] max-lg:leading-[22px] max-lg:mt-[13px]">
                  Read Article
                </h6>
              </Link>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default BlogHome;
