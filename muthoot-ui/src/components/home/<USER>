import Image from "next/image";
import React from "react";
import CommonTextBlue from "../common/CommonTextBlue";
import Link from "next/link";
import Button from "../ui/Button";

const CalculatorSection = ({ data }: any) => {
  return (
    <section id="calculators" className="loan_calculator">
      <div className="container">
        <h2 className="text-[44px] font-[600] text-[#141414] max-w-[705px] w-full mx-auto text-center max-md:text-[35px]">
          <CommonTextBlue title={data?.Title} />
        </h2>
        <ul className="loan_list flex justify-start items-start gap-[13px] mt-[36px] flex-wrap max-lg:justify-center">
          {data?.Calculators?.map((calculator: any, index: number) => (
            <li
              key={index}
              id={`calculator-${calculator.Title.toLowerCase().replace(
                /\s+/g,
                "-"
              )}`}
              className="rounded-[14px] border border-[#B7DFF4] bg-[#E7F7FF] p-[30px] relative overflow-hidden w-[calc(100%/2-7px)] self-stretch max-lg:w-[calc(100%/2-9px)] max-md:w-full max-sm:p-[20px]"
            >
              <div className="particle absolute right-0 top-0 h-full">
                <Image
                  className="w-full max-w-[330px] aspect-[331/254] h-full object-cover mx-auto "
                  src="/loan_calculatorparticle.png"
                  alt="banner"
                  width={331}
                  height={254}
                />
              </div>
              <div className="content relative z-[9] flex justify-between items-start flex-col h-full">
                <div className="title_wrap">
                  <h3 className="text-[26px] font-semibold text-[#141414]">
                    {calculator.Title}
                  </h3>
                  <p className="text-[16px] mt-[12px] font-[400] text-[#484848]">
                    {calculator.Description}
                  </p>
                </div>
                <div className="btn_wrapper mt-[44px] max-md:mt-[25px]">
                  <Link
                    href={
                      calculator.Title.toLowerCase().includes("fd") ||
                      calculator.Title.toLowerCase().includes("fixed deposit")
                        ? "/products/fixed-deposit#calculator"
                        : "/products/two-wheeler-loan#calculator"
                    }
                  >
                    <Button
                      className="!py-[14px] !px-[33px]"
                      children={calculator.Button?.Label || "Check Now"}
                    />
                  </Link>
                </div>
              </div>
            </li>
          ))}
        </ul>
      </div>
    </section>
  );
};

export default CalculatorSection;
