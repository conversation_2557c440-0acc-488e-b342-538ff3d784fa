import React from "react";
import Image from "next/image";
import Link from "next/link";

interface RecommendedBlogsProps {
  currentBlog: any;
  allBlogs: any[];
}

const RecommendedBlogs = ({ currentBlog, allBlogs }: RecommendedBlogsProps) => {
  const getRecommendedBlogs = () => {
    // Safely get current tags with null checks
    const currentTags = currentBlog?.Tags?.map((tag: any) =>
      tag?.name?.toLowerCase()
    ) || [];

    // Filter with proper null checks
    const relatedBlogs = (allBlogs || []).filter((blog: any) => {
      if (!blog || blog?.documentId === currentBlog?.documentId) return false;

      const blogTags = blog?.Tags?.map((tag: any) => tag?.name?.toLowerCase()) || [];
      return blogTags.some((tag: string) => currentTags.includes(tag));
    });

    return relatedBlogs.length > 0
      ? relatedBlogs.slice(0, 4)
      : (allBlogs || [])
          .filter((blog) => blog?.documentId !== currentBlog?.documentId)
          .slice(0, 4);
  };

  const recommendedBlogs = getRecommendedBlogs();

  return (
    <div className="sub_contents max-w-[447px] w-full sticky top-[20px] max-lg:max-w-full max-lg:relative max-lg:top-0">
      <h3 className="text-[32px] font-semibold text-[#008BD2] max-sm:text-[28px]">
        Recommended Blogs
      </h3>
      <div className="sub_list mt-[24px]">
        {recommendedBlogs?.map((blog: any) => (
          <div
            key={blog?.documentId || Math.random().toString()}
            className="sub_cards flex justify-start items-center gap-[30px] mt-[22px] max-sm:flex-col max-sm:items-start max-sm:gap-[15px]"
          >
            <div className="sub_image max-w-[173px] w-full">
              <Image
                className="w-full h-full object-cover rounded-[10px] aspect-[173/124]"
                src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${
                  blog?.Thumbnail?.formats?.thumbnail?.url || 
                  blog?.Thumbnail?.url || 
                  "/placeholder.jpg"
                }`}
                alt={blog?.Thumbnail?.alternativeText || blog?.Title || "Blog image"}
                width={173}
                height={124}
              />
            </div>
            <div className="content max-w-[244px] w-full max-lg:max-w-[calc(100%-173px)] max-lg:w-full max-sm:max-w-full">
              <h4 className="text-[20px] leading-[24px] font-medium text-[#141414] line-clamp-1">
                {blog?.Title || "Untitled Blog"}
              </h4>
              <p className="text-[14px] leading-[18px] font-normal text-[#3F3F3F] mt-[8px] line-clamp-2 my-[8px]">
                {blog?.Description?.[0]?.children?.[0]?.text?.slice(0, 100) || "No description available"}...
              </p>
              <Link
                className="text-[16px] font-semibold text-[#008BD2] hover:underline"
                href={`/blogs/${blog?.documentId || ""}`}
              >
                Read Now
              </Link>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default RecommendedBlogs;
