import React from "react";
import Image from "next/image";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ger,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON>Header,
  <PERSON>alogTitle,
  DialogDescription,
  DialogClose,
} from "@/components/ui/dialog";

export default function DirectorBox({ data }: any) {
  return (
    <div className="grid_wrapper flex flex-wrap justify-start items-start gap-[22px] mt-[46px] max-lg:justify-center">
      {data?.map((director: any, index: any) => (
        <div
          data-aos="fade-up"
          key={index}
          className="director_box border border-[#a2d4ee] self-stretch bg-[#e7f7ff] rounded-[26px] w-[calc(100%/3-15px)] max-lg:w-[calc(100%/2-15px)] max-md:w-full max-md:max-w-[630px] max-md:mx-auto"
        >
          {director?.Image?.url && (
            <div className="director_image aspect-[412/287] rounded-t-[20px]">
              <Image
                className="w-full aspect-[412/287] h-full object-cover rounded-t-[20px]"
                src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${director.Image.url}`}
                alt={director.Name || "Director"}
                width={1000}
                height={1000}
              />
            </div>
          )}
          <div className="director_details px-[40px] pb-[23px]">
            {director.Name && (
              <h3
                data-aos="fade-up"
                className="text-[22px] font-semibold text-[#141414] mt-[14px] mb-[10px] text-center max-md:text-[20px]"
              >
                {director.Name}
              </h3>
            )}
            {director.Designation && (
              <h5
                data-aos="fade-up"
                className="text-[18px] font-medium text-[#008BD2] mt-[10px] mb-[20px] text-center max-md:text-[16px]"
              >
                {director.Designation}
              </h5>
            )}

            {Array.isArray(director.About) &&
              director.About.length > 0 &&
              director.About.map((about: any, idx: any) => {
                const isLastItem = idx === director.About.length - 1;

                if (!isLastItem) {
                  // Show full content for all items except the last one
                  return (
                    <p
                      data-aos="fade-up"
                      key={idx}
                      className="text-[16px] leading-[22px] text-[#484848] font-normal py-[20px] text-center border-t border-[#BCE2F5] max-md:text-[14px] max-md:leading-[22px] max-md:py-[10px]"
                    >
                      {about.About}
                    </p>
                  );
                } else {
                  // For the last item, truncate and add inline Read more button
                  const truncatedText = about.About.substring(0, 150);

                  return (
                    <p
                      data-aos="fade-up"
                      key={idx}
                      className="text-[16px] leading-[22px] text-[#484848] font-normal py-[20px] text-center border-t border-[#BCE2F5] max-md:text-[14px] max-md:leading-[22px] max-md:py-[10px]"
                    >
                      {truncatedText}
                      {about.About.length > 150 && (
                        <>
                          ...
                          <Dialog>
                            <DialogTrigger className="inline-block">
                              <span className="text-[16px] font-[700] text-[#008BD2] ml-1">
                                Read more
                              </span>
                            </DialogTrigger>

                            <DialogContent className="bg-[#F2FBFF] p-[50px] border border-[#2193D133] max-w-[1250px] w-full rounded-[26px] max-md:p-[20px] max-md:h-[90vh]">
                              {/* Dialog content remains the same */}
                              <DialogClose asChild>
                                <div className="modal_closebtn cursor-pointer absolute right-[20px] top-[20px] z-10">
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="24"
                                    height="24"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    stroke="currentColor"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    className="lucide lucide-x h-4 w-4 text-white"
                                  >
                                    <path d="M18 6 6 18"></path>
                                    <path d="m6 6 12 12"></path>
                                  </svg>
                                </div>
                              </DialogClose>

                              <DialogHeader>
                                <DialogTitle></DialogTitle>
                                <DialogDescription>
                                  <div className="wrapper flex justify-center items-start gap-[35px] max-md:flex-col max-md:gap-[25px]">
                                    <div className="director_image rounded-[20px] max-w-[346px] max-md:max-w-[320px] max-md:m-auto">
                                      {director?.Image?.url ? (
                                        <Image
                                          className="w-full object-cover rounded-[20px] aspect-[346/465] max-md:aspect-square object-top"
                                          src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${director?.Image?.url}`}
                                          alt="director image"
                                          width={346}
                                          height={465}
                                        />
                                      ) : (
                                        <Image
                                          className="w-full object-cover rounded-[20px] aspect-[346/465] max-md:aspect-square object-top"
                                          src="/fallbacklogo.svg"
                                          alt="director image"
                                          width={346}
                                          height={465}
                                        />
                                      )}
                                    </div>
                                    <div className="director_contents max-w-[calc(100%-380px)] w-full max-md:max-w-full">
                                      <h3 className="text-[26px] font-[600] text-[#141414] leading-[35px]">
                                        {director?.Name}
                                      </h3>
                                      <h4 className="text-[20px] font-[500] text-[#008BD2] mt-[18px] pb-[25px] leading-[30px] max-md:pb-[10px] max-md:mt-[12px]">
                                        {director?.Designation}
                                      </h4>
                                      <div className="scrollable_contents max-h-[380px] overflow-y-auto max-md:max-h-[250px]">
                                        {Array.isArray(director.About) &&
                                          director.About.length > 0 &&
                                          director.About.map(
                                            (about: any, idx: any) => (
                                              <p
                                                data-aos="fade-up"
                                                key={idx}
                                                className="text-[16px] leading-[22px] text-[#484848] text-left font-normal py-[10px] max-md:text-[14px] max-md:leading-[22px] max-md:py-[5px]"
                                              >
                                                {about.About}
                                              </p>
                                            )
                                          )}
                                      </div>
                                    </div>
                                  </div>
                                </DialogDescription>
                              </DialogHeader>
                            </DialogContent>
                          </Dialog>
                        </>
                      )}
                    </p>
                  );
                }
              })}
          </div>
        </div>
      ))}
    </div>
  );
}
