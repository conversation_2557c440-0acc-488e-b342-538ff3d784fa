import React from "react";

const MilestoneCard = ({ data }: any) => {
  // If no data provided, use empty arrays
  if (!data || !Array.isArray(data)) {
    return <div>No milestone data available</div>;
  }

  // Sort data by year to ensure chronological order
  const sortedData = [...data].sort((a, b) => a.Year - b.Year);

  // Get milestone by index, return empty object if not available
  const getMilestone = (index: any) => {
    return sortedData[index] || { About: "", Year: "" };
  };

  return (
    <div className="milestone_mapwrapper h-[650px] relative pt-[40px] max-w-[1200px] w-full">
      {/* first step */}
      <div className="first_step_row relative z-[9] top-[8px] flex justify-start gap-[140px] items-start">
        <div className="single_milestone flex justify-center items-center gap-[10px] flex-col max-w-[200px] w-full max-lg:mx-auto">
          <div className="content_box rounded-[10px]  p-[40px]  w-full min-h-[126px] flex justify-center items-center ">
            <h4 className="text-[14px] font-[600] text-white text-center line-clamp-3">
              {getMilestone(0).About}
            </h4>
          </div>

          <div className="year_box w-[66px] h-[66px] flex justify-center items-center rounded-full  border-[5px] border-[#F8B900] text-[18px] font-[600] text-[#141414] bg-white">
            {getMilestone(0).Year}
          </div>
        </div>
        <div className="single_milestone flex justify-center items-center gap-[10px] flex-col max-w-[200px] w-full">
          <div className="content_box rounded-[10px]  p-[40px]  w-full min-h-[126px] flex justify-center items-center   ">
            <h4 className="text-[14px] font-[600] text-white text-center line-clamp-3">
              {getMilestone(1).About}
            </h4>
          </div>

          <div className="year_box w-[66px] h-[66px] flex justify-center items-center rounded-full  border-[5px] border-[#F8B900] text-[18px] font-[600] text-[#141414] bg-white">
            {getMilestone(1).Year}
          </div>
        </div>
        <div className="single_milestone flex justify-center items-center gap-[10px] flex-col max-w-[200px] w-full">
          <div className="content_box rounded-[10px]  p-[40px]  w-full min-h-[126px] flex justify-center items-center   ">
            {" "}
            <h4 className="text-[14px] font-[600] text-white text-center line-clamp-3">
              {getMilestone(2).About}
            </h4>
          </div>

          <div className="year_box w-[66px] h-[66px] flex justify-center items-center rounded-full  border-[5px] border-[#F8B900] text-[18px] font-[600] text-[#141414] bg-white">
            {getMilestone(2).Year}
          </div>
        </div>
        <div className="single_milestone flex justify-center items-center gap-[10px] flex-col max-w-[200px] w-full">
          <div className="content_box rounded-[10px]  p-[40px]  w-full min-h-[126px] flex justify-center items-center   ">
            {" "}
            <h4 className="text-[14px] font-[600] text-white text-center line-clamp-3">
              {getMilestone(3).About}
            </h4>
          </div>

          <div className="year_box w-[66px] h-[66px] flex justify-center items-center rounded-full  border-[5px] border-[#F8B900] text-[18px] font-[600] text-[#141414] bg-white">
            {getMilestone(3).Year}
          </div>
        </div>
      </div>
      {/* second step */}
      <div className="second_step_row relative z-[9] top-[8px] flex justify-start gap-[40px] items-start max-w-[1100px] w-full mx-auto">
        <div className="single_milestone flex justify-center items-center gap-[10px] flex-col max-w-[180px] w-full">
          <div className="content_box rounded-[10px]  p-[40px]  w-full min-h-[126px] flex justify-center items-center ">
            <h4 className="text-[14px] font-[600] text-white text-center line-clamp-3">
              {getMilestone(4).About}
            </h4>
          </div>

          <div className="year_box w-[66px] h-[66px] flex justify-center items-center rounded-full  border-[5px] border-[#F8B900] text-[18px] font-[600] text-[#141414] bg-white">
            {getMilestone(4).Year}
          </div>
        </div>
        <div className="single_milestone flex justify-center items-center gap-[10px] flex-col max-w-[180px] w-full">
          <div className="content_box rounded-[10px]  p-[40px]  w-full min-h-[126px] flex justify-center items-center   ">
            <h4 className="text-[14px] font-[600] text-white text-center line-clamp-3">
              {getMilestone(5).About}
            </h4>
          </div>

          <div className="year_box w-[66px] h-[66px] flex justify-center items-center rounded-full  border-[5px] border-[#F8B900] text-[18px] font-[600] text-[#141414] bg-white">
            {getMilestone(5).Year}
          </div>
        </div>
        <div className="single_milestone flex justify-center items-center gap-[10px] flex-col max-w-[180px] w-full">
          <div className="content_box rounded-[10px]  p-[40px]  w-full min-h-[126px] flex justify-center items-center   ">
            {" "}
            <h4 className="text-[14px] font-[600] text-white text-center line-clamp-3">
              {getMilestone(6).About}
            </h4>
          </div>

          <div className="year_box w-[66px] h-[66px] flex justify-center items-center rounded-full  border-[5px] border-[#F8B900] text-[18px] font-[600] text-[#141414] bg-white">
            {getMilestone(6).Year}
          </div>
        </div>
        <div className="single_milestone flex justify-center items-center gap-[10px] flex-col max-w-[180px] w-full">
          <div className="content_box rounded-[10px]  p-[40px]  w-full min-h-[126px] flex justify-center items-center   ">
            {" "}
            <h4 className="text-[14px] font-[600] text-white text-center line-clamp-3">
              {getMilestone(7).About}
            </h4>
          </div>

          <div className="year_box w-[66px] h-[66px] flex justify-center items-center rounded-full  border-[5px] border-[#F8B900] text-[18px] font-[600] text-[#141414] bg-white">
            {getMilestone(7).Year}
          </div>
        </div>
        <div className="single_milestone flex justify-center items-center gap-[10px] flex-col max-w-[180px] w-full">
          <div className="content_box rounded-[10px]  p-[40px]  w-full min-h-[126px] flex justify-center items-center   ">
            {" "}
            <h4 className="text-[14px] font-[600] text-white text-center line-clamp-3">
              {getMilestone(8).About}
            </h4>
          </div>

          <div className="year_box w-[66px] h-[66px] flex justify-center items-center rounded-full  border-[5px] border-[#F8B900] text-[18px] font-[600] text-[#141414] bg-white">
            {getMilestone(8).Year}
          </div>
        </div>
      </div>
      {/* third step */}
      <div className="third_step_row relative z-[9] top-[8px] flex justify-start gap-[140px] items-start max-w-[1150px] w-full ml-auto">
        <div className="single_milestone flex justify-center items-center gap-[10px] flex-col max-w-[200px] w-full">
          <div className="content_box rounded-[10px]  p-[40px]  w-full min-h-[126px] flex justify-center items-center ">
            <h4 className="text-[14px] font-[600] text-white text-center line-clamp-3">
              {getMilestone(9).About}
            </h4>
          </div>

          <div className="year_box w-[66px] h-[66px] flex justify-center items-center rounded-full  border-[5px] border-[#F8B900] text-[18px] font-[600] text-[#141414] bg-white">
            {getMilestone(9).Year}
          </div>
        </div>
        <div className="single_milestone flex justify-center items-center gap-[10px] flex-col max-w-[200px] w-full">
          <div className="content_box rounded-[10px]  p-[40px]  w-full min-h-[126px] flex justify-center items-center   ">
            <h4 className="text-[14px] font-[600] text-white text-center line-clamp-3">
              {getMilestone(10).About}
            </h4>
          </div>

          <div className="year_box w-[66px] h-[66px] flex justify-center items-center rounded-full  border-[5px] border-[#F8B900] text-[18px] font-[600] text-[#141414] bg-white">
            {getMilestone(10).Year}
          </div>
        </div>
        <div className="single_milestone flex justify-center items-center gap-[10px] flex-col max-w-[200px] w-full">
          <div className="content_box rounded-[10px]  p-[40px]  w-full min-h-[126px] flex justify-center items-center   ">
            {" "}
            <h4 className="text-[14px] font-[600] text-white text-center line-clamp-3">
              {getMilestone(11).About}
            </h4>
          </div>

          <div className="year_box w-[66px] h-[66px] flex justify-center items-center rounded-full  border-[5px] border-[#F8B900] text-[18px] font-[600] text-[#141414] bg-white">
            {getMilestone(11).Year}
          </div>
        </div>
        <div className="single_milestone flex justify-center items-center gap-[10px] flex-col max-w-[200px] w-full">
          <div className="content_box rounded-[10px]  p-[40px]  w-full min-h-[126px] flex justify-center items-center   ">
            {" "}
            <h4 className="text-[14px] font-[600] text-white text-center line-clamp-3">
              {getMilestone(12).About}
            </h4>
          </div>

          <div className="year_box w-[66px] h-[66px] flex justify-center items-center rounded-full  border-[5px] border-[#F8B900] text-[18px] font-[600] text-[#141414] bg-white">
            {getMilestone(12).Year}
          </div>
        </div>
      </div>
      {/* fourth step  */}
      {/* <div className="fourth_step_row third_step_row relative z-[9] top-[8px] flex justify-start gap-[240px] items-start">
        <div className="single_milestone reverse_singlemilestone flex justify-center items-center gap-[10px] flex-col max-w-[200px] w-full">
          <div className="content_box rounded-[10px]  p-[40px]  w-full min-h-[126px] flex justify-center items-center   ">
            {" "}
            <h4 className="text-[14px] font-[600] text-white text-center line-clamp-3">
              {getMilestone(11).About}
            </h4>
          </div>

          <div className="year_box w-[66px] h-[66px] flex justify-center items-center rounded-full  border-[5px] border-[#F8B900] text-[18px] font-[600] text-[#141414] bg-white">
            {getMilestone(11).Year}
          </div>
        </div>
        <div className="single_milestone reverse_singlemilestone flex justify-center items-center gap-[10px] flex-col max-w-[200px] w-full">
          <div className="content_box rounded-[10px]  p-[40px]  w-full min-h-[126px] flex justify-center items-center   ">
            <h4 className="text-[14px] font-[600] text-white text-center line-clamp-3">
              {getMilestone(10).About}
            </h4>
          </div>

          <div className="year_box w-[66px] h-[66px] flex justify-center items-center rounded-full  border-[5px] border-[#F8B900] text-[18px] font-[600] text-[#141414] bg-white">
            {getMilestone(10).Year}
          </div>
        </div>
        <div className="single_milestone reverse_singlemilestone flex justify-center items-center gap-[10px] flex-col max-w-[200px] w-full">
          <div className="content_box rounded-[10px]  p-[40px]  w-full min-h-[126px] flex justify-center items-center ">
            <h4 className="text-[14px] font-[600] text-white text-center line-clamp-3">
              {getMilestone(9).About}
            </h4>
          </div>

          <div className="year_box w-[66px] h-[66px] flex justify-center items-center rounded-full  border-[5px] border-[#F8B900] text-[18px] font-[600] text-[#141414] bg-white">
            {getMilestone(9).Year}
          </div>
        </div>
      </div> */}
      {/* fifth step  */}
      {/* <div className="fifth_step_row  third_step_row relative z-[9] top-[8px] flex justify-start gap-[240px] items-start">
        <div className="single_milestone reverse_singlemilestone flex justify-center items-center gap-[10px] flex-col max-w-[200px] w-full">
          <div className="content_box rounded-[10px]  p-[40px]  w-full min-h-[126px] flex justify-center items-center ">
            <h4 className="text-[14px] font-[600] text-white text-center line-clamp-3">
              {getMilestone(12).About}
            </h4>
          </div>

          <div className="year_box w-[66px] h-[66px] flex justify-center items-center rounded-full  border-[5px] border-[#F8B900] text-[18px] font-[600] text-[#141414] bg-white">
            {getMilestone(12).Year}
          </div>
        </div>
        <div className="single_milestone reverse_singlemilestone flex justify-center items-center gap-[10px] flex-col max-w-[200px] w-full">
          <div className="content_box rounded-[10px]  p-[40px]  w-full min-h-[126px] flex justify-center items-center   ">
            <h4 className="text-[14px] font-[600] text-white text-center line-clamp-3">
              {getMilestone(13).About}
            </h4>
          </div>

          <div className="year_box w-[66px] h-[66px] flex justify-center items-center rounded-full  border-[5px] border-[#F8B900] text-[18px] font-[600] text-[#141414] bg-white">
            {getMilestone(13).Year}
          </div>
        </div>
        <div className="single_milestone reverse_singlemilestone flex justify-center items-center gap-[10px] flex-col max-w-[200px] w-full">
          <div className="content_box rounded-[10px]  p-[40px]  w-full min-h-[126px] flex justify-center items-center   ">
            {" "}
            <h4 className="text-[14px] font-[600] text-white text-center line-clamp-3">
              {getMilestone(14).About}
            </h4>
          </div>

          <div className="year_box w-[66px] h-[66px] flex justify-center items-center rounded-full  border-[5px] border-[#F8B900] text-[18px] font-[600] text-[#141414] bg-white">
            {getMilestone(14).Year}
          </div>
        </div>
      </div> */}
    </div>
  );
};

export default MilestoneCard;
