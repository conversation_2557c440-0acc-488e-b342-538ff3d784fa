"use client";

import React, { useState } from "react";
import Image from "next/image";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
  <PERSON>alogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogClose,
} from "@/components/ui/dialog";

export default function YouthDirectors({ directors }: any) {
  const [expanded, setExpanded] = useState<{ [key: number]: boolean }>({});

  const toggleReadMore = (id: number) => {
    setExpanded((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  return (
    <>
      {directors?.map((director: any) => (
        <div
          data-aos="fade-up"
          key={director?.id}
          className="director_box border border-[#a2d4ee] self-stretch overflow-hidden bg-[#e7f7ff] rounded-[20px] w-[calc(100%/4-12px)] max-xl:w-[calc(100%/3-12px)] max-md:w-[calc(100%/2-12px)] max-sm:w-full max-sm:max-w-[420px] max-sm:mx-auto"
        >
          {director?.Image?.url ? (
            <div className="director_image aspect-[308/310]">
              <Image
                className="w-full aspect-[308/310] h-full object-cover"
                src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${director?.Image?.url}`}
                alt={director?.name || "Director"}
                width={1000}
                height={1000}
              />
            </div>
          ) : (
            <div className="director_image aspect-[308/310] bg-[#008BD2] flex items-center justify-center">
              <Image
                src="/fallbacklogo.svg"
                alt="Company Logo"
                width={80}
                height={80}
                className="object-contain"
              />
            </div>
          )}
          <div className="director_details px-[14px] pb-[26px]">
            {director?.Name && (
              <h3 className="text-[18px] font-semibold text-[#141414] mt-[16px] mb-[6px] text-center">
                {director?.Name}
              </h3>
            )}
            {director?.Designation && (
              <h5 className="text-[16px] font-medium text-[#008BD2] mt-[10px] mb-[14px] text-center max-md:text-[14px]">
                {director?.Designation}
              </h5>
            )}
            {director?.Description && (
              <>
                <p
                  className={`text-[16px] leading-[22px] text-[#484848] font-normal text-center pt-[14px] border-t border-[#BCE2F5] ${
                    expanded[director?.id] ? "" : "line-clamp-2"
                  }`}
                >
                  {director?.Description}
                </p>
                {/* <span
                  className="font-bold text-[#008BD2] cursor-pointer block text-center"
                  onClick={() => toggleReadMore(director?.id)}
                >
                  {expanded[director?.id] ? " Show Less" : " Read More"}
                </span> */}
              </>
            )}
            <Dialog>
              <DialogTrigger className="w-full">
                <div className="read_morebtn flex justify-center">
                  <span className="text-[16px] font-[700] text-[#008BD2]">
                    Read more...
                  </span>
                </div>
              </DialogTrigger>

              <DialogContent className="bg-[#F2FBFF] p-[50px] border border-[#2193D133] max-w-[1250px] w-full rounded-[26px] max-md:p-[20px] max-md:h-[90vh]">
                <DialogClose asChild>
                  <div className="modal_closebtn cursor-pointer absolute right-[20px] top-[20px] z-10">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="lucide lucide-x h-4 w-4 text-white"
                    >
                      <path d="M18 6 6 18"></path>
                      <path d="m6 6 12 12"></path>
                    </svg>
                  </div>
                </DialogClose>

                <DialogHeader>
                  <DialogTitle></DialogTitle>
                  <DialogDescription>
                    <div className="wrapper flex justify-center items-start gap-[35px] max-md:flex-col max-md:gap-[25px]">
                      <div className="director_image rounded-[20px] max-w-[346px] max-md:max-w-[320px] max-md:m-auto">
                        {director?.Image?.url ? (
                          <Image
                            className="w-full object-cover rounded-[20px] aspect-[346/465] max-md:aspect-square object-top"
                            src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${director?.Image?.url}`}
                            alt="director image"
                            width={346}
                            height={465}
                          />
                        ) : (
                          <Image
                            className="w-full object-cover rounded-[20px] aspect-[346/465] max-md:aspect-square object-top"
                            src="/fallbacklogo.svg"
                            alt="director image"
                            width={346}
                            height={465}
                          />
                        )}
                      </div>
                      <div className="director_contents max-w-[calc(100%-380px)] w-full max-md:max-w-full">
                        <h3 className="text-[26px] font-[600] text-[#141414] leading-[35px]">
                          {director?.Name}
                        </h3>
                        <h4 className="text-[20px] font-[500] text-[#008BD2] mt-[18px] pb-[25px] leading-[30px] max-md:pb-[10px] max-md:mt-[12px]">
                          {director?.Designation}
                        </h4>
                        <div className="scrollable_contents max-h-[380px] overflow-y-auto max-md:max-h-[250px]">
                          {director?.Description && (
                            <p className="text-[18px] font-[400] text-[#141414] leading-[30px]">
                              {director?.Description}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  </DialogDescription>
                </DialogHeader>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      ))}
    </>
  );
}
