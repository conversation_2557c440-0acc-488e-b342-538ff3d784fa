import React from "react";

const MileStones = () => {
  const milestoneData = [
    [
      { year: "1994", content: "Started Operations" },
      { year: "1995", content: "Listed on BSE" },
      { year: "2008", content: "Entered the Two-Wheeler financing segment" },
    ],
    [
      { year: "2014", content: "MCSL ranks among Top 50 NBFC across India" },
      { year: "2016", content: "AUM crosses 1000 Cr", reverse: true },
      { year: "2018", content: "AUM crosses 500 Cr" },
    ],
    [
      { year: "2019", content: "AUM crosses 2000 Cr", reverse: true },
      { year: "2022", content: "Listed on BSE", reverse: true },
      { year: "2024", content: "AUM crosses 2500 Cr", reverse: true },
    ],
  ];

  return (
    <section className="milestone relative z-[9] py-[100px] pt-[20px]">
      <div className="container">
        <h3 className="text-[44px] font-[600] text-white text-center mb-[30px] max-sm:text-[35px]">
          Milestones
        </h3>
        <div className="milestone_mapwrapper h-[720px] relative pt-[40px] max-w-[1200px] w-full">
          {milestoneData.map((row, rowIndex) => (
            <div
              key={`row-${rowIndex}`}
              className={`${
                rowIndex === 0
                  ? "first_step_row"
                  : rowIndex === 1
                  ? "second_step_row"
                  : "third_step_row"
              } 
                relative z-[9] ${rowIndex === 1 ? "top-[20px]" : "top-[8px]"} 
                flex justify-start ${
                  rowIndex === 0 || rowIndex === 2
                    ? "gap-[240px]"
                    : "gap-[80px] max-w-[800px] mx-auto"
                } 
                items-start`}
            >
              {row.map((milestone, index) => (
                <div
                  key={`milestone-${rowIndex}-${index}`}
                  className={`single_milestone flex justify-center items-center gap-[10px] 
                    ${milestone.reverse ? "reverse_singlemilestone" : ""} 
                    flex-col max-w-[200px] w-full
                    ${
                      rowIndex === 1 && index === 1
                        ? "mt-[-134px] flex-col-reverse"
                        : ""
                    }`}
                >
                  <div className="content_box rounded-[10px] bg-[#003E7D] p-[40px] w-full min-h-[126px] flex justify-center items-center">
                    <h4 className="text-[14px] font-[600] text-white text-center line-clamp-3">
                      {milestone.content}
                    </h4>
                  </div>

                  <div className="year_box w-[66px] h-[66px] flex justify-center items-center rounded-full border-[5px] border-[#F8B900] text-[18px] font-[600] text-[#141414] bg-white">
                    {milestone.year}
                  </div>
                </div>
              ))}
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default MileStones;
