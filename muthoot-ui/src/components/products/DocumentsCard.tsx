import React from "react";

export default function DocumentsCard({ data }: any) {
  return (
    <div className="document_listing flex justify-center items-start flex-wrap gap-[40px] mt-[36px] max-lg:gap-[20px]">
      {data?.map((item: any, index: any) => (
        <div
          data-aos="fade-up"
          className="document_box bg-[#E7F7FF] rounded-[20px] p-[22px] flex justify-start items-start gap-[12px] w-[calc(100%/4-30px)] max-lg:w-[calc(100%/2-10px)] max-sm:w-full self-stretch"
          key={index}
        >
          <div className="icon">
            <svg
              className="mt-[5px]"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <circle cx="12" cy="12" r="12" fill="#008BD2" />
              <path
                d="M17.5759 8.67073C17.2662 8.36106 16.8017 8.36106 16.492 8.67073L10.763 14.4772L8.363 12.0772C8.05333 11.7675 7.51139 11.7675 7.27913 12.0772C6.96946 12.3869 6.96946 12.9288 7.27913 13.1611L10.2211 16.103C10.5307 16.4127 10.9953 16.4127 11.3049 16.103L17.6533 9.75461C17.8856 9.52235 17.8856 8.98041 17.5759 8.67073Z"
                fill="white"
              />
            </svg>
          </div>
          <div className="content">
            <h4 className="text-[24px] leading-[29px] font-semibold text-black max-md:text-[20px] max-md:leading-[26px]">
              {item?.Title}
            </h4>
            <p className="text-[16px] font-normal text-[#484848] leading-[22px] mt-[12px] max-md:text-[14px] max-md:leading-[20px]  ">
              {item?.Description}
            </p>
          </div>
        </div>
      ))}
    </div>
  );
}
