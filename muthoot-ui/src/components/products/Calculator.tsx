"use client";

import React, { useState } from "react";
import { Slider } from "@/components/ui/slider";
import Image from "next/image";
import toast from "react-hot-toast";

const showErrorToast = (message: string) => {
  toast.error(message, {
    duration: 3000,

    style: {
      // background: "#ef4444",
      // color: "#ffffff",
      fontWeight: "500",
      // border: "1px solid #dc2626",
      borderRadius: "8px",
      boxShadow:
        "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
    },
    icon: "⚠️",
  });
};
export default function Calculator() {
  const [loanAmountSlider, setLoanAmountSlider] = useState([33]);
  const [interestRateSlider, setInterestRateSlider] = useState([33]);
  const [tenureSlider, setTenureSlider] = useState([33]);

  // Input field states
  const [loanAmountInput, setLoanAmountInput] = useState("");
  const [interestRateInput, setInterestRateInput] = useState("");
  const [tenureInput, setTenureInput] = useState("");

  // Toast state
  const [toast, setToast] = useState({ show: false, message: "" });

  // Constants for validation
  const LOAN_AMOUNT_MIN = 10000;
  const LOAN_AMOUNT_MAX = 200000;
  const INTEREST_RATE_MIN = 10;
  const INTEREST_RATE_MAX = 32;
  const TENURE_MIN = 1;
  const TENURE_MAX = 84;

  // Calculate loan amount based on slider percentage
  const calculateLoanAmount = (percentage: number) => {
    const value =
      LOAN_AMOUNT_MIN +
      (percentage / 100) * (LOAN_AMOUNT_MAX - LOAN_AMOUNT_MIN);
    return Math.round(value);
  };

  // Calculate interest rate based on slider percentage
  const calculateInterestRate = (percentage: number) => {
    const value =
      INTEREST_RATE_MIN +
      (percentage / 100) * (INTEREST_RATE_MAX - INTEREST_RATE_MIN);
    return value.toFixed(2);
  };

  // Calculate tenure in months based on slider percentage
  const calculateTenure = (percentage: number) => {
    const value = TENURE_MIN + (percentage / 100) * (TENURE_MAX - TENURE_MIN);
    return Math.round(value);
  };

  // Show toast message
  const showToast = (message: string) => {
    setToast({ show: true, message });
    setTimeout(() => {
      setToast({ show: false, message: "" });
    }, 3000);
  };

  // Enhanced validation functions
  const validateLoanAmount = (value: string) => {
    const num = parseFloat(value);
    if (isNaN(num)) return "Please enter a valid loan amount";
    if (num < LOAN_AMOUNT_MIN)
      return `Loan amount must be at least ₹${LOAN_AMOUNT_MIN.toLocaleString()}`;
    if (num > LOAN_AMOUNT_MAX)
      return `Loan amount cannot exceed ₹${LOAN_AMOUNT_MAX.toLocaleString()}`;
    return null;
  };

  const validateInterestRate = (value: string) => {
    const num = parseFloat(value);
    if (isNaN(num)) return "Please enter a valid interest rate";
    if (num < INTEREST_RATE_MIN)
      return `Interest rate must be at least ${INTEREST_RATE_MIN}%`;
    if (num > INTEREST_RATE_MAX)
      return `Interest rate cannot exceed ${INTEREST_RATE_MAX}%`;
    return null;
  };

  const validateTenure = (value: string) => {
    const num = parseFloat(value);
    if (isNaN(num)) return "Please enter a valid tenure";
    if (num < TENURE_MIN) return `Tenure must be at least ${TENURE_MIN} month`;
    if (num > TENURE_MAX) return `Tenure cannot exceed ${TENURE_MAX} months`;
    return null;
  };

  // Percentage calculation functions
  const getLoanAmountPercentage = (amount: number) => {
    const clampedAmount = Math.max(
      LOAN_AMOUNT_MIN,
      Math.min(LOAN_AMOUNT_MAX, amount)
    );
    return (
      ((clampedAmount - LOAN_AMOUNT_MIN) /
        (LOAN_AMOUNT_MAX - LOAN_AMOUNT_MIN)) *
      100
    );
  };

  const getInterestRatePercentage = (rate: number) => {
    const clampedRate = Math.max(
      INTEREST_RATE_MIN,
      Math.min(INTEREST_RATE_MAX, rate)
    );
    return (
      ((clampedRate - INTEREST_RATE_MIN) /
        (INTEREST_RATE_MAX - INTEREST_RATE_MIN)) *
      100
    );
  };

  const getTenurePercentage = (tenure: number) => {
    const clampedTenure = Math.max(TENURE_MIN, Math.min(TENURE_MAX, tenure));
    return ((clampedTenure - TENURE_MIN) / (TENURE_MAX - TENURE_MIN)) * 100;
  };

  // Enhanced input change handlers with real-time validation
  const handleLoanAmountInputChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    let value = e.target.value;

    if (value === "") {
      setLoanAmountInput(value);
      return;
    }

    value = value.replace(/[^0-9.]/g, "");
    const decimalCount = (value.match(/\./g) || []).length;
    if (decimalCount > 1) return;

    const numValue = parseFloat(value);

    if (!isNaN(numValue) && numValue > 200000) {
      showErrorToast("Loan amount cannot exceed ₹2,00,000");
      return;
    }

    setLoanAmountInput(value);

    if (!isNaN(numValue) && numValue >= 10000) {
      const percentage = getLoanAmountPercentage(numValue);
      setLoanAmountSlider([percentage]);
    }
  };

  const handleInterestRateInputChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    let value = e.target.value;

    if (value === "") {
      setInterestRateInput(value);
      return;
    }

    value = value.replace(/[^0-9.]/g, "");
    const decimalCount = (value.match(/\./g) || []).length;
    if (decimalCount > 1) return;

    const numValue = parseFloat(value);

    if (!isNaN(numValue) && numValue > 32) {
      showErrorToast("Interest rate cannot exceed 32%");
      return;
    }

    setInterestRateInput(value);

    if (!isNaN(numValue) && numValue >= 10) {
      const percentage = getInterestRatePercentage(numValue);
      setInterestRateSlider([percentage]);
    }
  };

  const handleTenureInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value;

    if (value === "") {
      setTenureInput(value);
      return;
    }

    value = value.replace(/[^0-9]/g, "");
    const numValue = parseInt(value);

    if (!isNaN(numValue) && numValue > 84) {
      showErrorToast("Tenure cannot exceed 84 months");
      return;
    }

    setTenureInput(value);

    if (!isNaN(numValue) && numValue >= 1) {
      const percentage = getTenurePercentage(numValue);
      setTenureSlider([percentage]);
    }
  };

  // Handle input blur events for final validation
  const handleLoanAmountBlur = () => {
    if (loanAmountInput === "") return;

    const validation = validateLoanAmount(loanAmountInput);
    if (validation) {
      showToast(validation);
      // Reset to slider value if invalid
      setLoanAmountInput(calculateLoanAmount(loanAmountSlider[0]).toString());
    }
  };

  const handleInterestRateBlur = () => {
    if (interestRateInput === "") return;

    const validation = validateInterestRate(interestRateInput);
    if (validation) {
      showToast(validation);
      // Reset to slider value if invalid
      setInterestRateInput(calculateInterestRate(interestRateSlider[0]));
    }
  };

  const handleTenureBlur = () => {
    if (tenureInput === "") return;

    const validation = validateTenure(tenureInput);
    if (validation) {
      showToast(validation);
      // Reset to slider value if invalid
      setTenureInput(calculateTenure(tenureSlider[0]).toString());
    }
  };

  // Handle slider changes and update input fields
  const handleLoanAmountSliderChange = (newValue: number[]) => {
    setLoanAmountSlider(newValue);
    setLoanAmountInput(calculateLoanAmount(newValue[0]).toString());
  };

  const handleInterestRateSliderChange = (newValue: number[]) => {
    setInterestRateSlider(newValue);
    setInterestRateInput(calculateInterestRate(newValue[0]));
  };

  const handleTenureSliderChange = (newValue: number[]) => {
    setTenureSlider(newValue);
    setTenureInput(calculateTenure(newValue[0]).toString());
  };

  // Get current values for calculations (use slider values if input is empty)
  const getCurrentLoanAmount = () => {
    if (loanAmountInput === "") return calculateLoanAmount(loanAmountSlider[0]);
    const value = parseFloat(loanAmountInput);
    return isNaN(value) ? calculateLoanAmount(loanAmountSlider[0]) : value;
  };

  const getCurrentInterestRate = () => {
    if (interestRateInput === "")
      return parseFloat(calculateInterestRate(interestRateSlider[0]));
    const value = parseFloat(interestRateInput);
    return isNaN(value)
      ? parseFloat(calculateInterestRate(interestRateSlider[0]))
      : value;
  };

  const getCurrentTenure = () => {
    if (tenureInput === "") return calculateTenure(tenureSlider[0]);
    const value = parseFloat(tenureInput);
    return isNaN(value) ? calculateTenure(tenureSlider[0]) : value;
  };

  const formatCurrency = (amount: number) => {
    return amount.toLocaleString("en-IN");
  };

  // Calculate EMI, interest payable
  const calculateEMI = () => {
    const principal = getCurrentLoanAmount();
    const ratePerMonth = getCurrentInterestRate() / (12 * 100);
    const tenureMonths = getCurrentTenure();

    const emi =
      (principal * ratePerMonth * Math.pow(1 + ratePerMonth, tenureMonths)) /
      (Math.pow(1 + ratePerMonth, tenureMonths) - 1);

    const totalPayment = emi * tenureMonths;
    const interestPayable = totalPayment - principal;

    return {
      emi: Math.round(emi),
      interestPayable: Math.round(interestPayable),
    };
  };

  const { emi, interestPayable } = calculateEMI();

  return (
    <div className="calculator_box max-w-[1055px] w-full mx-auto p-[44px] rounded-[20px] bg-[#eaf8ff] border-[rgba(33,147,209,0.2)] border max-sm:px-[15px] max-sm:py-[30px] relative">
      {/* Toast Notification */}
      {toast.show && (
        <div className="fixed top-4 right-4 z-50 bg-red-500 text-white px-4 py-2 rounded-md shadow-lg transition-all duration-300">
          {toast.message}
        </div>
      )}
      <div className="calculation_area flex justify-between items-start gap-[40px] max-sm:gap-[20px] max-md:flex-col max-md:items-center">
        <div className="range_wrapper max-w-[484px] w-full max-md:max-w-full">
          {/* Loan Amount Section */}
          <div className="single_item mb-[20px]">
            <div className="wrapper flex justify-between items-start gap-[20px]">
              <h6 className="text-[16px] font-medium text-[#141414] mb-[20px] max-sm:text-[14px]">
                Required loan amount
              </h6>
              <div className="value-box max-w-[145px] w-full">
                <input
                  className="px-3 py-1 w-full rounded-md border border-[#2193D1] bg-white text-[14px] font-medium"
                  type="text"
                  placeholder="Enter amount"
                  value={loanAmountInput}
                  onChange={handleLoanAmountInputChange}
                />
              </div>
            </div>
            <div className="input_parent">
              <Slider
                value={loanAmountSlider}
                max={100}
                step={1}
                onValueChange={handleLoanAmountSliderChange}
              />
            </div>
            <div className="min_max_valuewrapper mt-[10px] flex justify-between items-start gap-[30px]">
              <div className="min_value text-[14px] text-[#484848] font-[400]">
                min ₹{LOAN_AMOUNT_MIN.toLocaleString()}
              </div>
              <div className="max_value text-[14px] text-[#484848] font-[400]">
                max ₹{(LOAN_AMOUNT_MAX / 100000).toFixed(0)} Lac
              </div>
            </div>
          </div>

          {/* Interest Rate Section */}
          <div className="single_item mb-[20px]">
            <div className="wrapper flex justify-between items-start gap-[20px]">
              <h6 className="text-[16px] font-medium text-[#141414] mb-[20px] max-sm:text-[14px]">
                Interest rate
              </h6>
              <div className="value-box max-w-[145px] w-full">
                <input
                  className="px-3 py-1 w-full rounded-md border border-[#2193D1] bg-white text-[14px] font-medium"
                  type="text"
                  placeholder="Enter rate"
                  value={interestRateInput}
                  onChange={handleInterestRateInputChange}
                />
              </div>
            </div>
            <div className="input_parent relative">
              <Slider
                value={interestRateSlider}
                max={100}
                step={1}
                onValueChange={handleInterestRateSliderChange}
              />
            </div>
            <div className="min_max_valuewrapper mt-[10px] flex justify-between items-start gap-[30px]">
              <div className="min_value text-[14px] text-[#484848] font-[400]">
                min {INTEREST_RATE_MIN}%
              </div>
              <div className="max_value text-[14px] text-[#484848] font-[400]">
                max {INTEREST_RATE_MAX}%
              </div>
            </div>
          </div>

          {/* Tenure Section */}
          <div className="single_item mb-[20px]">
            <div className="wrapper flex justify-between items-start gap-[20px]">
              <h6 className="text-[16px] font-medium text-[#141414] mb-[20px] max-sm:text-[14px]">
                Tenure up to
              </h6>
              <div className="value-box max-w-[145px] w-full">
                <input
                  className="px-3 py-1 w-full rounded-md border border-[#2193D1] bg-white text-[14px] font-medium"
                  type="text"
                  placeholder="Enter months"
                  value={tenureInput}
                  onChange={handleTenureInputChange}
                />
              </div>
            </div>
            <div className="input_parent relative">
              <Slider
                value={tenureSlider}
                max={100}
                step={1}
                onValueChange={handleTenureSliderChange}
              />
            </div>
            <div className="min_max_valuewrapper mt-[10px] flex justify-between items-start gap-[30px]">
              <div className="min_value text-[14px] text-[#484848] font-[400]">
                {TENURE_MIN} month
              </div>
              <div className="max_value text-[14px] text-[#484848] font-[400]">
                {TENURE_MAX} months
              </div>
            </div>
          </div>
        </div>

        {/* Results Section */}
        <div className="amount_wrapper max-w-[450px] w-full max-md:max-w-full">
          <div className="calculator_image max-w-[148px] w-full mx-auto">
            <Image
              className="w-full h-full object-contain"
              src="/money.svg"
              alt="calculator image"
              width={108}
              height={108}
            />
          </div>

          <div className="loan_amount_details mt-[30px]">
            <div className="single_point pb-[13px] flex justify-between items-center gap-[40px]">
              <div className="loan_label flex justify-start items-center gap-[5px]">
                <div className="dot w-[14px] h-[14px] rounded-full bg-[#2193D1]"></div>
                <h5 className="text-[16px] font-medium text-[#141414] max-sm:text-[14px]">
                  Loan Amount
                </h5>
              </div>
              <div className="loan_amount text-[16px] font-[700] text-[#141414] max-sm:text-[14px]">
                ₹ {formatCurrency(getCurrentLoanAmount())}
              </div>
            </div>
            <div className="single_point pb-[13px] flex justify-between items-center gap-[40px]">
              <div className="loan_label flex justify-start items-center gap-[5px]">
                <div className="dot w-[14px] h-[14px] rounded-full bg-[#88D2FA]"></div>
                <h5 className="text-[16px] font-medium text-[#141414] max-sm:text-[14px]">
                  Interest Payable
                </h5>
              </div>
              <div className="loan_amount text-[16px] font-[700] text-[#141414] max-sm:text-[14px]">
                ₹ {formatCurrency(interestPayable)}
              </div>
            </div>
          </div>
          <div className="total_amountwrapper">
            <div className="single_point border-t border-[#CDE0EA] pt-[13px] flex justify-between items-center gap-[40px]">
              <div className="loan_label flex justify-start items-center gap-[5px]">
                <h4 className="text-[20px] font-semibold text-[#141414] max-sm:text-[18px]">
                  Monthly EMI
                </h4>
              </div>
              <div className="loan_amount text-[20px] font-[700] text-[#141414] max-sm:text-[18px]">
                ₹ {formatCurrency(emi)}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* <div className="book_btn flex justify-center items-center mt-[26px]">
        <Button children="Book my loan" />
      </div> */}
    </div>
  );
}
