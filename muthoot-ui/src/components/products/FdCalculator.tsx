"use client";

import React, { useState, useEffect } from "react";
import Image from "next/image";
import Button from "../ui/Button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface InvestmentData {
  category: string;
  amountInvested: number;
  scheme: string;
  interestEarned: number;
  maturityValue: number;
}

interface FormErrors {
  name?: string;
  contactNumber?: string;
  amountInvested?: string;
}

export default function FdCalculator() {
  const MIN_AMOUNT = 100000;

  const [investmentData, setInvestmentData] = useState<InvestmentData>({
    category: "Senior citizen",
    amountInvested: 0,
    scheme: "Monthly", // Default scheme
    interestEarned: 0,
    maturityValue: 0,
  });

  const [errors, setErrors] = useState<FormErrors>({});

  // Calculate interest and maturity value
  const calculateInterest = () => {
    if (
      investmentData.amountInvested < MIN_AMOUNT &&
      investmentData.amountInvested !== 0
    ) {
      setErrors((prev) => ({
        ...prev,
        amountInvested: `Minimum investment amount is ₹${MIN_AMOUNT.toLocaleString()}`,
      }));
      setInvestmentData((prev) => ({
        ...prev,
        interestEarned: 0,
        maturityValue: 0,
      }));
      return;
    } else {
      setErrors((prev) => ({
        ...prev,
        amountInvested: undefined,
      }));
    }

    const { category, scheme, amountInvested } = investmentData;
    let interestRate = 0;

    // Handle both Senior citizen and General categories
    if (category === "Senior citizen") {
      switch (scheme) {
        case "Monthly":
          interestRate = 12.5;
          break;
        case "Annual":
          interestRate = 12.75;
          break;
        case "Maturity":
          // Doubling scheme (similar to Weekly in the previous version)
          setInvestmentData((prev) => ({
            ...prev,
            interestEarned: amountInvested,
            maturityValue: amountInvested * 2,
          }));
          return;
      }
    } else if (category === "General") {
      switch (scheme) {
        case "Monthly":
          interestRate = 12.5;
          break;
        case "Annual":
          interestRate = 12.75;
          break;
        case "Maturity":
          // Doubling scheme (similar to Weekly in the previous version)
          setInvestmentData((prev) => ({
            ...prev,
            interestEarned: amountInvested,
            maturityValue: amountInvested * 2,
          }));
          return;
      }
    }

    const interestEarned = amountInvested * (interestRate / 100) * 5;
    const maturityValue = amountInvested + interestEarned;

    setInvestmentData((prev) => ({
      ...prev,
      interestEarned,
      maturityValue,
    }));
  };

  // Recalculate when relevant inputs change
  useEffect(() => {
    calculateInterest();
  }, [
    investmentData.amountInvested,
    investmentData.category,
    investmentData.scheme,
  ]);

  // Handle input changes
  const handleInputChange = (field: keyof InvestmentData, value: any) => {
    if (field === "amountInvested") {
      const numValue = Number(value);
      if (numValue !== 0 && numValue < MIN_AMOUNT) {
        setErrors((prev) => ({
          ...prev,
          amountInvested: `Minimum investment amount is ₹${MIN_AMOUNT.toLocaleString()}`,
        }));
      } else {
        setErrors((prev) => ({
          ...prev,
          amountInvested: undefined,
        }));
      }
    }

    setInvestmentData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Format currency with smart abbreviations when text would overflow
  const formatCurrency = (amount: number, maxLength: number = 15) => {
    if (amount === 0) return "0";

    const fullFormat = amount.toLocaleString("en-IN");

    // If the formatted string is within acceptable length, show full numbers
    if (fullFormat.length <= maxLength) {
      return fullFormat;
    }

    // Otherwise, use abbreviations
    if (amount >= 10000000) {
      // 1 crore and above
      return `${(amount / 10000000).toFixed(2)} Cr`;
    } else if (amount >= 100000) {
      // 1 lakh and above
      return `${(amount / 100000).toFixed(2)} L`;
    } else if (amount >= 1000) {
      // 1 thousand and above
      return `${(amount / 1000).toFixed(2)} K`;
    } else {
      return fullFormat;
    }
  };

  // Special formatting for different display areas
  const formatForCircle = (amount: number) => formatCurrency(amount, 12);
  const formatForDetails = (amount: number) => formatCurrency(amount, 18);

  return (
    <div className="calculator_box max-w-[1055px] w-full mx-auto p-[44px] rounded-[20px] bg-[#eaf8ff] border-[rgba(33,147,209,0.2)] border max-sm:px-[15px] max-sm:py-[30px]">
      <div className="calculation_area flex justify-between items-center gap-[40px] max-lg:flex-col max-lg:items-center max-lg:gap-[60px]">
        <div className="range_wrapper max-w-[484px] w-full max-md:max-w-full max-lg:max-w-full">
          <div className="single_item mb-[28px]">
            <div className="label_wrapper flex justify-between gap-[30px] items-center max-sm:flex-col max-sm:items-start max-sm:gap-[10px]">
              <label
                className="text-[20px] font-[600] text-[#141414] max-sm:text-[18px]"
                htmlFor=""
              >
                Category
              </label>
              <div className="input_parent max-w-[260px] w-full max-sm:max-w-full">
                <Select
                  value={investmentData.category}
                  onValueChange={(value) =>
                    handleInputChange("category", value)
                  }
                >
                  <SelectTrigger className="rounded-[10px] w-full border border-[#008BD2] bg-white p-[15px] text-[#000000] text-[20px] font-[600] h-[62px] max-sm:p-[10px] max-sm:h-[52px] max-sm:text-[18px]">
                    <SelectValue placeholder="Senior citizen" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Senior citizen">
                      Senior citizen
                    </SelectItem>
                    <SelectItem value="General">General</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
          <div className="single_item mb-[28px]">
            <div className="label_wrapper flex justify-between gap-[30px] items-center max-sm:flex-col max-sm:items-start max-sm:gap-[10px]">
              <label
                className="text-[20px] font-[600] text-[#141414] max-sm:text-[18px]"
                htmlFor=""
              >
                Amount invested
              </label>
              <div className="input_parent max-w-[260px] w-full max-sm:max-w-full">
                <input
                  className={`rounded-[10px] w-full border ${
                    errors.amountInvested
                      ? "border-red-500"
                      : "border-[#008BD2]"
                  } bg-white p-[15px] text-[#000000] text-[20px] font-[600] max-sm:p-[10px] max-sm:text-[18px] [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none`}
                  type="number"
                  min={MIN_AMOUNT}
                  value={
                    investmentData.amountInvested === 0
                      ? ""
                      : investmentData.amountInvested
                  }
                  onChange={(e) =>
                    handleInputChange(
                      "amountInvested",
                      e.target.value === "" ? 0 : parseFloat(e.target.value)
                    )
                  }
                  placeholder="0"
                />
                {errors.amountInvested && (
                  <p className="text-red-500 text-[12px] mt-1">
                    {errors.amountInvested}
                  </p>
                )}
              </div>
            </div>
          </div>
          <div className="single_item ">
            <div className="label_wrapper flex justify-between gap-[30px] items-center max-sm:flex-col max-sm:items-start max-sm:gap-[10px]">
              <label
                className="text-[20px] font-[600] text-[#141414] max-sm:text-[18px]"
                htmlFor=""
              >
                Scheme
              </label>
              <div className="input_parent max-w-[260px] w-full max-sm:max-w-full">
                <Select
                  value={investmentData.scheme}
                  onValueChange={(value) => handleInputChange("scheme", value)}
                >
                  <SelectTrigger className="rounded-[10px] w-full border border-[#008BD2] bg-white p-[15px] text-[#000000] text-[20px] font-[600] h-[62px] max-sm:text-[18px] max-sm:h-[52px] max-sm:p-[10px]">
                    <SelectValue placeholder="Monthly" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Monthly">Monthly</SelectItem>
                    <SelectItem value="Annual">Annual</SelectItem>
                    <SelectItem value="Maturity">Maturity</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </div>
        <div className="amount_wrapper max-w-[450px] w-full max-md:max-w-full">
          <div className="calculator_image max-w-[207px] w-full mx-auto relative mb-[62px]">
            <Image
              className="w-full h-full object-contain"
              src="/fd_round.png"
              alt="calculator image"
              width={210}
              height={210}
            />
            <h6 className="text-[17px] font-[700] text-[#141414] absolute top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%] w-[140px] text-center leading-[1.1] break-words hyphens-auto px-1">
              ₹ {formatForCircle(investmentData.maturityValue)}
            </h6>
          </div>

          <div className="loan_amount_details mt-[30px]">
            <div className="single_point pb-[13px] flex justify-between items-center gap-[20px] mb-[15px] max-sm:gap-[10px]">
              <div className="loan_label flex justify-start items-center gap-[5px] flex-shrink-0">
                <div className="dot w-[14px] h-[14px] rounded-full bg-[#2193D1] flex-shrink-0"></div>
                <h5 className="text-[20px] font-semibold text-[#141414] max-sm:text-[14px]">
                  Interest Earned (5 years)
                </h5>
              </div>
              <div className="loan_amount text-[16px] font-[700] text-[#141414] max-sm:text-[14px] text-right break-words min-w-0 overflow-hidden">
                <span
                  className="block truncate"
                  title={`₹ ${investmentData.interestEarned.toLocaleString(
                    "en-IN"
                  )}`}
                >
                  ₹ {formatForDetails(investmentData.interestEarned)}
                </span>
              </div>
            </div>
            <div className="single_point pb-[13px] flex justify-between items-center gap-[20px] max-sm:gap-[10px]">
              <div className="loan_label flex justify-start items-center gap-[5px] flex-shrink-0">
                <div className="dot w-[14px] h-[14px] rounded-full bg-[#88D2FA] flex-shrink-0"></div>
                <h5 className="text-[20px] font-semibold text-[#141414] max-sm:text-[14px]">
                  Maturity Value
                </h5>
              </div>
              <div className="loan_amount text-[16px] font-[700] text-[#141414] max-sm:text-[14px] text-right break-words min-w-0 overflow-hidden">
                <span
                  className="block truncate"
                  title={`₹ ${investmentData.maturityValue.toLocaleString(
                    "en-IN"
                  )}`}
                >
                  ₹ {formatForDetails(investmentData.maturityValue)}
                </span>
              </div>
            </div>
          </div>
          <div className="total_amountwrapper">
            <div className="single_point border-t border-[#CDE0EA] pt-[13px] flex justify-between items-center gap-[40px]">
              <div className="loan_amount text-[20px] font-[700] text-[#141414] max-sm:text-[18px]">
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
