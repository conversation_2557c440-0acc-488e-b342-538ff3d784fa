import Image from "next/image";
import React from "react";

export default function EligibilityCard({ data }: any) {
  return (
    <>
      {data?.map((item: any, index: any) => (
        <div data-aos="fade-up" key={index} className="eligibility_wrapper">
          <div className="eligibility_wrapper">
            <div
              data-aos="fade-up"
              className="eligibility_box bg-[#ebf9ff] rounded-[20px] py-[15px] px-[45px]  border border-[#2193D166] mt-[20px] flex justify-between items-center gap-[77px] max-lg:gap-[40px] max-sm:flex-wrap max-sm:justify-start max-sm:px-[20px] "
            >
              <div className="content flex justify-start items-center gap-[77px] max-md:gap-[40px] max-sm:items-center max-sm:gap-[20px]">
                <h2 className="text-[#2193D166] text-[88px] font-bold leading-[108px] max-md:text-[55px] max-md:leading-[65px]">
                  {item?.Count}
                </h2>
                <h6 className="text-[20px] font-medium text-[#141414] max-w-[439px] w-full max-md:text-[17px] mxax-sm:max-w-full ">
                  {item?.Criteria_Text}
                </h6>
              </div>
              <div className="hovered_image max-w-[250px] w-full rotate-[5deg] max-lg:!opacity-[1] max-lg:!max-w-[150px] max-sm:mx-auto">
                <Image
                  className="w-full h-full object-cover rounded-[16px] "
                  src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${item?.Hover_Image?.url}`}
                  alt="hovered image"
                  width={250}
                  height={166}
                />
              </div>
            </div>
          </div>
        </div>
      ))}
    </>
  );
}
