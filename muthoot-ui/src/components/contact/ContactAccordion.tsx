import React from "react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

export default function ContactAccordion({ data }: any) {
  return (
    <main>
      <Accordion type="single" collapsible>
        {data &&
          data?.length > 0 &&
          data?.map((item: any, index: number) => (
            <AccordionItem
              key={index}
              data-aos="fade-up"
              className="!py-[28px] contact_accordion_item !border-t-0 border-b !px-0 border-[#B0E4FC] !bg-white !border-l-0 !mb-0 !border-r-0 !rounded-[0] max-md:!py-[20px]"
              value={`item-${index}`}
            >
              <AccordionTrigger className="!text-[#141414] !font-medium !text-[20px] max-md:!text-[17px]">
                {item?.Question}
              </AccordionTrigger>
              <AccordionContent>
                <p className="text-[#484848] text-[16px] font-normal mt-[17px] leading-[24px] max-md:!text-[14px] max-md:!leading-[20px]">
                  {item?.Answer ? (
                    <>
                      {item.Answer.split(/\b(https:\/\/\S+)\b/).map(
                        (part: any, i: any) =>
                          part.startsWith("https://") ? (
                            <a
                              key={i}
                              href={part}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-[#008BD2] hover:underline"
                            >
                              {part}
                            </a>
                          ) : (
                            part
                          )
                      )}
                    </>
                  ) : null}
                </p>
              </AccordionContent>
            </AccordionItem>
          ))}
      </Accordion>
    </main>
  );
}
