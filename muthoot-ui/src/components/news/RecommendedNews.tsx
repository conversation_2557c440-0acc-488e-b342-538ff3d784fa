import React from "react";
import Image from "next/image";
import Link from "next/link";

interface RecommendedNewsEventsProps {
  currentNewsEvents: any;
  allNewsEvents: any[];
}

const RecommendedNewsEvents = ({
  currentNewsEvents,
  allNewsEvents,
}: RecommendedNewsEventsProps) => {
  const getRecommendedNewsEvents = () => {
    // Safely get current tags with null checks
    const currentTags =
      currentNewsEvents?.Tags?.map((tag: any) => tag?.name?.toLowerCase()) ||
      [];

    // Filter with proper null checks
    const relatedNewsEvents = (allNewsEvents || []).filter(
      (newsEventsData: any) => {
        if (
          !newsEventsData ||
          newsEventsData?.documentId === currentNewsEvents?.documentId
        )
          return false;

        const newsEventsTags =
          newsEventsData?.Tags?.map((tag: any) => tag?.name?.toLowerCase()) ||
          [];

        return newsEventsTags.some((tag: string) => currentTags.includes(tag));
      }
    );

    return relatedNewsEvents.length > 0
      ? relatedNewsEvents.slice(0, 4)
      : (allNewsEvents || [])
          .filter(
            (newsEventsData) =>
              newsEventsData?.documentId !== currentNewsEvents?.documentId
          )
          .slice(0, 4);
  };

  const recommendednewsEventsDatas = getRecommendedNewsEvents();

  return (
    <div className="sub_contents max-w-[447px] w-full sticky top-[20px] max-lg:max-w-full max-lg:relative max-lg:top-0">
      <h3 className="text-[28px] font-semibold text-[#008BD2] max-sm:text-[22px]">
        Recommended News & Events
      </h3>
      <div className="sub_list mt-[24px]">
        {recommendednewsEventsDatas?.map((newsEventsData: any) => (
          <div
            key={newsEventsData?.documentId || Math.random().toString()}
            className="sub_cards flex justify-start items-center gap-[30px] mt-[22px] max-sm:flex-col max-sm:items-start max-sm:gap-[15px]"
          >
            <div className="sub_image max-w-[173px] w-full">
              <Image
                className="w-full h-full object-cover rounded-[10px] aspect-[173/124]"
                src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${
                  newsEventsData?.Thumbnail?.formats?.thumbnail?.url ||
                  newsEventsData?.Thumbnail?.url ||
                  "/placeholder.jpg"
                }`}
                alt={
                  newsEventsData?.Thumbnail?.alternativeText ||
                  newsEventsData?.Title ||
                  "News image"
                }
                width={173}
                height={124}
              />
            </div>
            <div className="content max-w-[244px] w-full max-lg:max-w-[calc(100%-173px)] max-lg:w-full max-sm:max-w-full">
              <h4 className="text-[20px] leading-[24px] font-medium text-[#141414] line-clamp-1">
                {newsEventsData?.Title || "Untitled News"}
              </h4>
              <p className="text-[14px] leading-[18px] font-normal text-[#3F3F3F] mt-[8px] line-clamp-2 my-[8px]">
                {newsEventsData?.Description?.[0]?.children?.[0]?.text?.slice(
                  0,
                  100
                ) || "No description available"}
                ...
              </p>
              <Link
                className="text-[16px] font-semibold text-[#008BD2] hover:underline"
                href={`/news/${newsEventsData?.documentId || ""}`}
              >
                Read Article
              </Link>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default RecommendedNewsEvents;
