import React from "react";
import Image from "next/image";
import Link from "next/link";

const NewsEventsBox = ({ data }: any) => {
  const formatDate = (dateString: any) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const getDescriptionText = (description: any) => {
    if (!description || !description[0]?.children) return "";
    return (
      description.find((item: any) => item.type === "paragraph")?.children[0]
        ?.text || ""
    );
  };

  const sortedNews = data?.sort((a: any, b: any) => {
    const orderA = a.NewsOrder ?? Number.MAX_SAFE_INTEGER;
    const orderB = b.NewsOrder ?? Number.MAX_SAFE_INTEGER;
    return orderA - orderB;
  });

  return (
    <>
      {sortedNews?.map((newsEventsData: any, index: any) => (
        <Link
          data-aos="fade-up"
          href={`/news/${newsEventsData.documentId}`}
          key={newsEventsData.id}
          className="newsEventsData_box w-[calc(100%/3-24px)] max-lg:w-[calc(100%/2-18px)] max-sm:w-full max-sm:max-w-[430px] max-sm:mx-auto"
        >
          {newsEventsData?.Thumbnail?.url && (
            <div className="newsEventsData_image">
              <Image
                className="w-full rounded-[10px] aspect-[402/248] h-full object-cover"
                src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${newsEventsData?.Thumbnail?.url}`}
                alt={newsEventsData?.Title}
                width={1000}
                height={1000}
              />
            </div>
          )}

          <div className="content mt-[14px]">
            <h6 className="text-[16px] font-medium text-[#484848] max-md:text-[14px]">
              {formatDate(newsEventsData.Date)}
            </h6>
            <h3 className="text-[22px] font-medium text-[#0F0F0F] mt-[7px] max-md:text-[19px] line-clamp-2">
              {newsEventsData.Title}
            </h3>
            <p className="text-[16px] font-normal text-[#3F3F3F] leading-[22px] mt-[10px] max-md:text-[14px] max-md:leading-[20px] line-clamp-2">
              {getDescriptionText(newsEventsData.Description)}
            </p>
          </div>
        </Link>
      ))}
    </>
  );
};

export default NewsEventsBox;
