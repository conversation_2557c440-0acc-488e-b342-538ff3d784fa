"use client";
import React from "react";
import Image from "next/image";
import Link from "next/link";
import { GetPolicies } from "@/lib/api/general";
import { formatDate } from "@/utils";
export default function PolicyBox({ policies }: any) {
  const sortedBlogs = policies?.sort((a: any, b: any) => {
    const orderA = a.PolicyOrder ?? Number.MAX_SAFE_INTEGER;
    const orderB = b.PolicyOrder ?? Number.MAX_SAFE_INTEGER;
    return orderA - orderB;
  });

  return (
    <>
      {sortedBlogs &&
        sortedBlogs?.map((policy: any, index: any) => {
          return (
            <div
              data-aos="fade-up"
              key={index}
              className="box rounded-[14px] border border-[#B7DFF4] bg-[#E7F7FF] self-stretch p-[30px] relative overflow-hidden w-[calc(100%/3-16px)] max-lg:w-[calc(100%/2-12px)] max-md:w-full max-md:p-[20px] hover:bg-[#abe3ff]"
            >
              <div className="policy_vector absolute top-0 right-0 z-[1]">
                <Image
                  className="w-full h-full object-cover"
                  src="/policy-vector.png"
                  alt={`policy-vector-${index + 1}`}
                  width={310}
                  height={238}
                  priority={index < 2}
                />
              </div>

              <div className="content relative z-[50] flex justify-between flex-col h-full">
                <div className="content_wrapper">
                  <h3 className="text-[20px] font-semibold text-[#141414] leading-[26px] relative z-[50]">
                    {policy?.Title}
                  </h3>
                  <p className="text-[16px] leading-[22px] text-[#484848] mt-[12px] relative z-[50]">
                    {policy?.Description}
                  </p>
                </div>
                <button className="view_all mt-[22px] text-[16px] w-fit font-bold text-[#008BD2] flex items-center gap-[4px] relative z-[50]">
                  <Link href={`/policy/${policy?.slug}`}>View All</Link>
                  <svg
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9 18L15 12L9 6"
                      stroke="#008BD2"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </button>
              </div>
            </div>
          );
        })}
    </>
  );
}
