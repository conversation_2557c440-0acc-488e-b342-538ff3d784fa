import React from "react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { log } from "console";

interface TextItem {
  text: string;
  type: string;
}

interface TabItem {
  __component?: string;
  id?: number;
  Title?: string;
  Description?: any[];
}

interface PolicyAccordionProps {
  data?: TabItem[];
}

export default function PolicyAccordion({ data }: PolicyAccordionProps) {
  if (!data || !Array.isArray(data)) {
    return null;
  }
  const renderDescription = (desc: any, descIndex: number) => {
    if (desc?.type === "paragraph") {
      const paragraphText = (desc.children as TextItem[])?.[0]?.text;
      if (!paragraphText) return null;

      return (
        <div
          key={`paragraph-${descIndex}`}
          className="title text-[18px] font-medium text-white pl-[31px] mt-[18px] max-md:pl-0 max-md:text-[16px]"
        >
          {paragraphText}
        </div>
      );
    }

    if (desc?.type === "list" && desc?.format === "unordered") {
      const listItems = desc.children as {
        type: string;
        children: TextItem[];
      }[];
      if (!Array.isArray(listItems)) return null;

      return (
        <ul key={`list-${descIndex}`} className="pl-[31px] max-md:pl-0">
          {listItems?.map((listItem, listItemIndex) => {
            const itemText = listItem?.children?.[0]?.text;
            if (!itemText) return null;

            return (
              <li
                key={`list-item-${listItemIndex}`}
                className="text-[16px] font-light text-white mt-[20px] flex items-start gap-[10px] max-md:text-[14px]"
              >
                <div className="dot w-[13px] h-[13px] mt-[2px] bg-white rounded-[50%]"></div>
                <span className="w-[calc(100%-50px)]">{itemText}</span>
              </li>
            );
          })}
        </ul>
      );
    }

    return null;
  };

  return (
    <main>
      <Accordion type="single" collapsible>
        {data?.map((tab, index) => {
          if (!tab?.Title) return null;

          return (
            <AccordionItem
              data-aos="fade-up"
              key={tab.id || `tab-${index}`}
              value={`item-${index + 1}`}
            >
              <AccordionTrigger>{tab.Title}</AccordionTrigger>
              <AccordionContent className="text-white">
                {Array.isArray(tab.Description) &&
                  tab?.Description.map((desc, descIndex) =>
                    renderDescription(desc, descIndex)
                  ).filter(Boolean)}
              </AccordionContent>
            </AccordionItem>
          );
        })}
      </Accordion>
    </main>
  );
}
