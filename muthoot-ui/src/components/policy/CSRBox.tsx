"use client";
import React from "react";
import Image from "next/image";
import Link from "next/link";
import { GetPolicies } from "@/lib/api/general";
import { formatDate } from "@/utils";
export default function CSRBox({ data: policies }: any) {
  // const { data: policies } = await GetPolicies();

  return (
    <>
      {policies &&
        policies.map((policy: any, index: any) => {
          const {
            Date = policy.Date,
            Description = policy.Description,
            File = policy.File,
          } = policy;

          return (
            <div
              data-aos="fade-up"
              key={index}
              className="box rounded-[14px] border border-[#B7DFF4] bg-[#E7F7FF] p-[30px] relative overflow-hidden w-[calc(100%/3-16px)] self-stretch max-lg:w-[calc(100%/2-12px)] max-md:w-full max-md:p-[20px] hover:bg-[#abe3ff]"
            >
              <div className="policy_vector absolute top-0 right-0 z-[1]">
                <Image
                  className="w-full h-full object-cover"
                  src="/policy-vector.png"
                  alt={`policy-vector-${index + 1}`}
                  width={310}
                  height={238}
                  priority={index < 2}
                />
              </div>

              <div className="content relative z-[50] flex flex-col justify-between h-full ">
                <div className="content_wrapper">
                  <h3 className="text-[20px] font-semibold text-[#141414] leading-[26px] relative z-[50]">
                    {formatDate(Date) || ""}
                  </h3>
                  <p className="text-[16px] leading-[22px] text-[#484848] mt-[12px] relative z-[50]">
                    {Description}
                  </p>
                </div>
                <button
                  onClick={() =>
                    window.open(
                      `${process.env.NEXT_PUBLIC_API_BASE_URL}${File?.url}`,
                      "_blank"
                    )
                  }
                  className="view_all mt-[22px] text-[16px] w-fit font-bold text-[#008BD2] flex items-center gap-[4px] relative z-[50]"
                >
                  Download Pdf
                  <svg
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9 18L15 12L9 6"
                      stroke="#008BD2"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </button>
              </div>
            </div>
          );
        })}
    </>
  );
}
