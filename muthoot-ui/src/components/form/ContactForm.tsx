"use client";
import React, { useState } from "react";
import Button from "../ui/Button";
import axios from "axios";
import toast from "react-hot-toast";
import endpoints from "@/endpoints";
import Button1 from "../ui/Button1";

const ContactForm = () => {
  const [formData, setFormData] = useState<any>({
    name: "",
    email: "",
    mobile: "",
    message: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<any>({});

  const handleChange = (e: any) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
    setErrors({ ...errors, [name]: undefined });
  };

  const validate = () => {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    const mobileRegex = /^\d{10}$/;
    let validationErrors: any = {};

    // Validate Name
    if (!formData.name) {
      validationErrors.name = "Name is required";
    } else if (formData.name.length < 3) {
      validationErrors.name = "Name must be at least 3 characters";
    }

    // Validate Email
    if (!formData.email) {
      validationErrors.email = "Email is required";
    } else if (!emailRegex.test(formData.email)) {
      validationErrors.email = "Invalid email address";
    }

    // Validate Mobile Number
    if (!formData.mobile) {
      validationErrors.mobile = "Mobile number is required";
    } else if (!mobileRegex.test(formData.mobile)) {
      validationErrors.mobile = "Enter a 10-digit mobile number";
    }

    // Validate Message
    if (!formData.message) {
      validationErrors.message = "Message is required";
    }

    setErrors(validationErrors);

    // If no errors, return true to submit form
    return Object.keys(validationErrors).length === 0;
  };

  const handleSubmit = async (e: any) => {
    e.preventDefault();
    setIsSubmitting(true);

    if (validate()) {
      const payload = {
        data: {
          Name: formData.name,
          Email: formData.email,
          Mobile_Number: formData.mobile,
          Message: formData.message,
        },
      };

      try {
        const response = await axios.post(
          `${process.env.NEXT_PUBLIC_API_URL}${endpoints.postContactForm}`,
          payload
        );

        if (response.status === 201) {
          toast.success("Thank you! We'll get back to you shortly.");
          setFormData({ name: "", email: "", mobile: "", message: "" });
        } else {
          toast.error("Something went wrong. Please try again later.");
        }
      } catch (error) {
        console.error("Error:", error);
        toast.error("An error occurred. Please try again.");
      } finally {
        setIsSubmitting(false);
      }
    } else {
      setIsSubmitting(false);
    }
  };

  return (
    <form
      className="p-[46px] max-w-[calc(100%-563px)] max-xl:max-w-[560px] w-full max-lg:max-w-full max-sm:px-[20px] border border-[#E6E6E6]"
      onSubmit={handleSubmit}
    >
      <h4 className="text-[48px] font-normal text-[#141414] mb-[28px] max-sm:text-[35px] max-sm:mb-[10px]">
        Let's Connect
      </h4>

      <div className="input_parent mt-[18px]">
        <input
          className={`w-full h-full py-[19px] px-[15px] border border-[#DADADA] rounded-[8px] text-[14px] font-normal placeholder:text-[#484848] text-[#484848] max-sm:p-[13px] ${
            errors.name ? "border-red-500" : ""
          }`}
          placeholder="Name"
          type="text"
          name="name"
          value={formData.name}
          onChange={(e) => {
            const value = e.target.value;
            if (/^[a-zA-Z\s]*$/.test(value)) {
              handleChange(e);
            }
          }}
        />
        {errors.name && (
          <p className="text-red-500 text-xs mt-1">{errors.name}</p>
        )}
      </div>

      <div className="input_parent mt-[18px]">
        <input
          className={`w-full h-full py-[19px] px-[15px] border border-[#DADADA] rounded-[8px] placeholder:text-[#484848] text-[14px] font-normal text-[#484848] max-sm:p-[13px] ${
            errors.email ? "border-red-500" : ""
          }`}
          placeholder="Email"
          type="email"
          name="email"
          value={formData.email}
          onChange={handleChange}
        />
        {errors.email && (
          <p className="text-red-500 text-xs mt-1">{errors.email}</p>
        )}
      </div>

      <div className="input_parent mt-[18px]">
        <input
          className={`w-full h-full py-[19px] px-[15px] border border-[#DADADA] rounded-[8px] placeholder:text-[#484848] text-[14px] font-normal text-[#484848] max-sm:p-[13px] [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none ${
            errors.mobile ? "border-red-500" : ""
          }`}
          placeholder="Mobile Number"
          type="number"
          name="mobile"
          value={formData.mobile}
          onChange={handleChange}
        />
        {errors.mobile && (
          <p className="text-red-500 text-xs mt-1">{errors.mobile}</p>
        )}
      </div>

      <div className="input_parent mt-[18px]">
        <textarea
          placeholder="Message"
          className={`w-full h-full py-[19px] min-h-[100px] px-[15px] border border-[#DADADA] placeholder:text-[#484848] rounded-[8px] text-[14px] font-normal text-[#484848] max-sm:p-[13px] ${
            errors.message ? "border-red-500" : ""
          }`}
          name="message"
          value={formData.message}
          onChange={handleChange}
        />
        {errors.message && (
          <p className="text-red-500 text-xs mt-1">{errors.message}</p>
        )}
      </div>

      <div className="btn_wrapper mt-[20px]">
        <Button1
          disabled={isSubmitting}
          name={
            isSubmitting ? (
              <div className="flex items-center justify-center gap-2">
                <svg
                  className="animate-spin h-5 w-5 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                <span>Submitting</span>
              </div>
            ) : (
              "Submit Enquiry"
            )
          }
          type="submit"
          icon={false}
        />
      </div>
    </form>
  );
};

export default ContactForm;
