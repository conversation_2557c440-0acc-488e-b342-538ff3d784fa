"use client";
import Image from "next/image";
import React, { useState, useEffect, useRef } from "react";
import Button1 from "../ui/Button1";
import { Checkbox } from "../ui/checkbox";
import { ChevronDown } from "lucide-react";
import axios from "axios";
import toast from "react-hot-toast";
import endpoints from "@/endpoints";
import { State, City } from "country-state-city";

const GetinTouchForm = () => {
  const [formData, setFormData] = useState<any>({
    name: "",
    email: "",
    phone: "",
    state: "",
    city: "",
    message: "",
  });
  const [isChecked, setIsChecked] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<any>({});
  const [states, setStates] = useState<any[]>([]);
  const [cities, setCities] = useState<any[]>([]);

  // Dropdown states
  const [isStateDropdownOpen, setIsStateDropdownOpen] = useState(false);
  const [isCityDropdownOpen, setIsCityDropdownOpen] = useState(false);
  const stateDropdownRef = useRef<HTMLDivElement>(null);
  const cityDropdownRef = useRef<HTMLDivElement>(null);

  // Load states on component mount
  useEffect(() => {
    const indianStates = State.getStatesOfCountry("IN");
    setStates(indianStates);
  }, []);

  // Update cities when state changes
  useEffect(() => {
    if (formData.state) {
      const stateCities = City.getCitiesOfState("IN", formData.state);
      setCities(stateCities);
    } else {
      setCities([]);
    }
  }, [formData.state]);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        stateDropdownRef.current &&
        !stateDropdownRef.current.contains(event.target as Node)
      ) {
        setIsStateDropdownOpen(false);
      }
      if (
        cityDropdownRef.current &&
        !cityDropdownRef.current.contains(event.target as Node)
      ) {
        setIsCityDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleChange = (e: any) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
    setErrors({ ...errors, [name]: undefined });
  };

  const handleSelectChange = (
    field: string,
    value: string,
    displayName?: string
  ) => {
    if (field === "state") {
      // Store both the state code and full name
      setFormData({
        ...formData,
        state: value,
        stateName:
          displayName || states.find((s) => s.isoCode === value)?.name || "",
        city: "", // Reset city when state changes
      });
    } else {
      setFormData({
        ...formData,
        [field]: value,
      });
    }
    setErrors({ ...errors, [field]: undefined });
  };

  const validate = () => {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    const phoneRegex = /^\d{10}$/;
    let validationErrors: any = {};

    // Validate Name
    if (!formData.name) {
      validationErrors.name = "Name is required";
    } else if (formData.name.length < 3) {
      validationErrors.name = "Name must be at least 3 characters";
    }

    // Validate Email
    if (!formData.email) {
      validationErrors.email = "Email is required";
    } else if (!emailRegex.test(formData.email)) {
      validationErrors.email = "Invalid email address";
    }

    // Validate Phone Number
    if (!formData.phone) {
      validationErrors.phone = "Mobile number is required";
    } else if (!phoneRegex.test(formData.phone)) {
      validationErrors.phone = "Enter a 10-digit mobile number";
    }

    // Validate State
    if (!formData.state) {
      validationErrors.state = "State is required";
    }

    // Validate City
    if (!formData.city) {
      validationErrors.city = "City is required";
    }

    // Validate Message
    if (!formData.message) {
      validationErrors.message = "Message is required";
    }

    // Validate Checkbox
    if (!isChecked) {
      validationErrors.terms = "You must agree to the terms";
    }

    setErrors(validationErrors);

    // If no errors, return true to submit form
    return Object.keys(validationErrors).length === 0;
  };

  const handleSubmit = async (e: any) => {
    e.preventDefault();
    setIsSubmitting(true);

    if (validate()) {
      const payload = {
        data: {
          Name: formData.name,
          Email: formData.email,
          Phone: formData.phone,
          State: formData.stateName || "", // Use the full state name
          City: formData.city,
          Message: formData.message,
        },
      };

      try {
        const response = await axios.post(
          `${process.env.NEXT_PUBLIC_API_URL}${endpoints.postGetinTouchForm}`,
          payload
        );

        if (response.status === 201) {
          toast.success("Thank you! We'll get back to you shortly.");
          setFormData({
            name: "",
            email: "",
            phone: "",
            state: "",
            city: "",
            message: "",
          });
          setIsChecked(false);
        } else {
          toast.error("Something went wrong. Please try again later.");
        }
      } catch (error) {
        console.error("Error:", error);
        toast.error("An error occurred. Please try again.");
      } finally {
        setIsSubmitting(false);
      }
    } else {
      setIsSubmitting(false);
    }
  };

  return (
    <div
      data-aos="fade-up"
      className="form_wrapper pl-[42px] max-w-[536px] w-full max-xl:flex max-xl:justify-center max-xl:gap-[40px] max-xl:max-w-full max-xl:pl-0 max-xl:pt-[70px] max-md:flex-col max-md:items-center max-md:pt-[40px]"
    >
      <form className="max-xl:pb-[60px] max-md:pb-0" onSubmit={handleSubmit}>
        <div className="single_field">
          <div className="input_parent">
            <input
              placeholder="Your Name"
              className={`bg-white w-full border border-[rgba(33,147,209,0.5)] rounded-[8px] p-[16px] h-[46px] text-[14px] font-normal text-[#6C727F] placeholder:text-[#6C727F] ${
                errors.name ? "border-red-500" : ""
              }`}
              type="text"
              name="name"
              value={formData.name}
              onChange={(e) => {
                const value = e.target.value;
                if (/^[a-zA-Z\s]*$/.test(value)) {
                  handleChange(e);
                }
              }}
            />
            {errors.name && (
              <p className="text-red-500 text-xs mt-1">{errors.name}</p>
            )}
          </div>
        </div>

        <div className="multi_input flex justify-start items-start gap-[25px] mt-[25px] max-sm:flex-col">
          <div className="input_parent w-[calc(100%/2-12px)] relative max-sm:w-full">
            <div className="mail_icon absolute right-[12px] top-[50%] -translate-y-[50%]">
              <svg
                width="18"
                height="18"
                viewBox="0 0 18 18"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  opacity="0.2"
                  d="M15.75 3.9375L9 10.125L2.25 3.9375H15.75Z"
                  fill="#6C727F"
                />
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M1.83535 3.55741C2.04527 3.3284 2.40109 3.31293 2.6301 3.52285L9 9.36193L15.3699 3.52285C15.5989 3.31293 15.9547 3.3284 16.1647 3.55741C16.3746 3.78641 16.3591 4.14223 16.1301 4.35215L9.3801 10.5397C9.16504 10.7368 8.83496 10.7368 8.61991 10.5397L1.86991 4.35215C1.6409 4.14223 1.62543 3.78641 1.83535 3.55741Z"
                  fill="#6C727F"
                />
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M1.6875 3.9375C1.6875 3.62684 1.93934 3.375 2.25 3.375H15.75C16.0607 3.375 16.3125 3.62684 16.3125 3.9375V13.5C16.3125 13.7984 16.194 14.0845 15.983 14.2955C15.772 14.5065 15.4859 14.625 15.1875 14.625H2.8125C2.51413 14.625 2.22798 14.5065 2.017 14.2955C1.80603 14.0845 1.6875 13.7984 1.6875 13.5V3.9375ZM2.8125 4.5V13.5H15.1875V4.5H2.8125Z"
                  fill="#6C727F"
                />
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M8.18409 8.61981C8.39407 8.84876 8.37868 9.20459 8.14973 9.41456L2.80598 14.3153C2.57702 14.5253 2.2212 14.5099 2.01123 14.281C1.80125 14.052 1.81664 13.6962 2.04559 13.4862L7.38934 8.58544C7.61829 8.37547 7.97412 8.39085 8.18409 8.61981Z"
                  fill="#6C727F"
                />
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M9.81591 8.61981C10.0259 8.39085 10.3817 8.37547 10.6107 8.58544L15.9544 13.4862C16.1834 13.6962 16.1988 14.052 15.9888 14.281C15.7788 14.5099 15.423 14.5253 15.194 14.3153L9.85028 9.41456C9.62132 9.20459 9.60594 8.84876 9.81591 8.61981Z"
                  fill="#6C727F"
                />
              </svg>
            </div>
            <input
              placeholder="<EMAIL>"
              className={`bg-white w-full border pr-[33px] border-[rgba(33,147,209,0.5)] rounded-[8px] p-[16px] h-[46px] text-[14px] font-normal text-[#6C727F] placeholder:text-[#6C727F] ${
                errors.email ? "border-red-500" : ""
              }`}
              type="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
            />
            {errors.email && (
              <p className="text-red-500 text-xs mt-1">{errors.email}</p>
            )}
          </div>
          <div className="input_parent w-[calc(100%/2-12px)] max-sm:w-full">
            <input
              placeholder="Mobile number"
              className={`bg-white w-full border border-[rgba(33,147,209,0.5)] rounded-[8px] p-[16px] h-[46px] text-[14px] font-normal text-[#6C727F] placeholder:text-[#6C727F] [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none ${
                errors.phone ? "border-red-500" : ""
              }`}
              type="number"
              name="phone"
              value={formData.phone}
              onChange={handleChange}
            />
            {errors.phone && (
              <p className="text-red-500 text-xs mt-1">{errors.phone}</p>
            )}
          </div>
        </div>
        <div className="multi_input flex justify-start items-start gap-[25px] mt-[25px] max-sm:flex-col">
          {/* Custom state and city dropdowns */}
          <div
            className="input_parent w-[calc(100%/2-12px)] max-sm:w-full"
            ref={stateDropdownRef}
          >
            <div
              className={`bg-white w-full border ${
                errors.state
                  ? "border-red-500"
                  : "border-[rgba(33,147,209,0.5)]"
              } rounded-[8px] p-[16px] h-[46px] text-[14px] font-normal text-[#6C727F] cursor-pointer flex justify-between items-center`}
              onClick={() => setIsStateDropdownOpen(!isStateDropdownOpen)}
            >
              <span
                className={formData.state ? "text-[#141414]" : "text-[#6C727F]"}
              >
                {formData.state
                  ? states.find((s) => s.isoCode === formData.state)?.name
                  : "Select State"}
              </span>
              <ChevronDown
                className={`w-4 h-4 transition-transform duration-200 ${
                  isStateDropdownOpen ? "transform rotate-180" : ""
                }`}
              />
            </div>

            {isStateDropdownOpen && (
              <div className="absolute z-10 w-[calc(100%/2-12px)] max-sm:w-full mt-1 bg-white border border-[#E5E7EB] rounded-[8px] shadow-lg max-h-[240px] overflow-y-auto custom-scrollbar">
                <div className="py-1">
                  {states.map((state) => (
                    <div
                      key={state.isoCode}
                      className={`px-4 py-2 text-sm hover:bg-[#F3F9FD] cursor-pointer ${
                        formData.state === state.isoCode
                          ? "bg-[#F3F9FD] text-[#008BD2] font-medium"
                          : ""
                      }`}
                      onClick={() => {
                        handleSelectChange("state", state.isoCode, state.name);
                        setIsStateDropdownOpen(false);
                      }}
                    >
                      {state.name}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {errors.state && (
              <p className="text-red-500 text-xs mt-1">{errors.state}</p>
            )}
          </div>

          {/* City dropdown */}
          <div
            className="input_parent w-[calc(100%/2-12px)] max-sm:w-full"
            ref={cityDropdownRef}
          >
            <div
              className={`bg-white w-full border ${
                errors.city ? "border-red-500" : "border-[rgba(33,147,209,0.5)]"
              } rounded-[8px] p-[16px] h-[46px] text-[14px] font-normal ${
                formData.state
                  ? "text-[#6C727F] cursor-pointer"
                  : "text-[#9CA3AF] opacity-50 cursor-not-allowed"
              } flex justify-between items-center`}
              onClick={() =>
                formData.state && setIsCityDropdownOpen(!isCityDropdownOpen)
              }
            >
              <span className={formData.city ? "text-[#141414]" : ""}>
                {formData.city || "Select City"}
              </span>
              <ChevronDown
                className={`w-4 h-4 transition-transform duration-200 ${
                  isCityDropdownOpen ? "transform rotate-180" : ""
                }`}
              />
            </div>

            {isCityDropdownOpen && formData.state && (
              <div className="absolute z-10 w-[calc(100%/2-12px)] max-sm:w-full mt-1 bg-white border border-[#E5E7EB] rounded-[8px] shadow-lg max-h-[240px] overflow-y-auto custom-scrollbar">
                <div className="py-1">
                  {cities.length > 0 ? (
                    cities.map((city) => (
                      <div
                        key={city.id}
                        className={`px-4 py-2 text-sm hover:bg-[#F3F9FD] cursor-pointer ${
                          formData.city === city.name
                            ? "bg-[#F3F9FD] text-[#008BD2] font-medium"
                            : ""
                        }`}
                        onClick={() => {
                          handleSelectChange("city", city.name);
                          setIsCityDropdownOpen(false);
                        }}
                      >
                        {city.name}
                      </div>
                    ))
                  ) : (
                    <div className="px-4 py-2 text-sm text-[#6C727F]">
                      No cities available
                    </div>
                  )}
                </div>
              </div>
            )}

            {errors.city && (
              <p className="text-red-500 text-xs mt-1">{errors.city}</p>
            )}
          </div>
        </div>
        <div className="single_field mt-[25px]">
          <div className="input_parent">
            <textarea
              placeholder="Write message"
              name="message"
              value={formData.message}
              onChange={handleChange}
              className={`bg-white w-full border border-[rgba(33,147,209,0.5)] rounded-[8px] p-[16px] h-[46px] text-[14px] font-normal text-[#6C727F] placeholder:text-[#6C727F] min-h-[100px] ${
                errors.message ? "border-red-500" : ""
              }`}
            ></textarea>
            {errors.message && (
              <p className="text-red-500 text-xs mt-1">{errors.message}</p>
            )}
          </div>
        </div>

        <div className="terms_wrapper flex justify-start items-start gap-[8px] mt-[16px]">
          <div className="check_boxwrapper">
            <Checkbox
              checked={isChecked}
              onCheckedChange={(checked) => {
                setIsChecked(checked as boolean);
                if (checked) {
                  setErrors((prevErrors: any) => ({
                    ...prevErrors,
                    terms: undefined,
                  }));
                }
              }}
            />
          </div>
          <div className="flex flex-col">
            <p className="text-[12px] leading-[16px] text-[#484848] font-normal">
              I authorize Muthoot Capital & other Muthoot Pappachan Group
              companies (including its Agents/representatives) to
              call/communicate with me on their product offerings/promotions
              through Telephone/Mobile/SMS/email ID/WhatsApp.
            </p>
            {errors.terms && (
              <p className="text-red-500 text-xs mt-1">{errors.terms}</p>
            )}
          </div>
        </div>

        <div className="btn_wrapper mt-[26px]">
          <Button1
            disabled={isSubmitting}
            name={
              isSubmitting ? (
                <div className="flex items-center justify-center gap-2">
                  <svg
                    className="animate-spin h-5 w-5 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5
                      8 8-8 0 1.714.716 3.394 1.944 4.41A5.962 5.962 0 0112 21c1.292 0 2.478-.292 3.438-.801a5.962 5.962 0 011.944-4.41z"
                    ></path>
                  </svg>
                  <span>Submitting</span>
                </div>
              ) : (
                "Submit Enquiry"
              )
            }
            type="submit"
            icon={false}
          />
        </div>
      </form>
    </div>
  );
};

export default GetinTouchForm;
