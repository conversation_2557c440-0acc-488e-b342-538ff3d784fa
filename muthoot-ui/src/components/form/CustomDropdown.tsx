"use client";
import React, { useState } from "react";
import { ChevronDown } from "lucide-react";

interface DropdownOption {
  id: string;
  name: string;
  value: string;
}

interface CustomDropdownProps {
  options: DropdownOption[];
  value: string;
  onChange: (value: string) => void;
  placeholder: string;
  disabled?: boolean;
  error?: string;
}

const CustomDropdown = ({
  options,
  value,
  onChange,
  placeholder,
  disabled = false,
  error,
}: CustomDropdownProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const selectedOption = options.find((option) => option.value === value);

  return (
    <div className="relative w-full">
      <button
        type="button"
        onClick={() => !disabled && setIsOpen(!isOpen)}
        className={`flex items-center justify-between w-full bg-white border ${
          error ? "border-red-500" : "border-[rgba(33,147,209,0.5)]"
        } rounded-[8px] p-[16px] h-[46px] text-[14px] font-normal ${
          disabled ? "bg-gray-50 cursor-not-allowed" : "cursor-pointer"
        }`}
        disabled={disabled}
      >
        <span className={value ? "text-[#141414]" : "text-[#6C727F]"}>
          {selectedOption ? selectedOption.name : placeholder}
        </span>
        <ChevronDown className="w-4 h-4 text-[#6C727F]" />
      </button>
      
      {isOpen && (
        <div className="absolute z-10 w-full mt-1 bg-white border border-[#E5E7EB] rounded-[8px] shadow-lg max-h-[240px] overflow-y-auto">
          {options.length > 0 ? (
            options.map((option) => (
              <div
                key={option.id}
                className="p-[12px] hover:bg-[#F3F9FD] cursor-pointer"
                onClick={() => {
                  onChange(option.value);
                  setIsOpen(false);
                }}
              >
                {option.name}
              </div>
            ))
          ) : (
            <div className="p-[12px] text-[#6C727F]">No options available</div>
          )}
        </div>
      )}
      
      {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
    </div>
  );
};

export default CustomDropdown;


