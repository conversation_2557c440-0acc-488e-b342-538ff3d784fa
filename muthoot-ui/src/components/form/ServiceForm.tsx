"use client";
import React, { useState, useEffect, useRef } from "react";
import axios from "axios";
import toast from "react-hot-toast";
import endpoints from "@/endpoints";
import useFetch from "@/hooks/useFetch";
import { ChevronDown, MessageSquarePlus, X } from "lucide-react";
import CommonButton from "../ui/CommonButton";
import WhiteButton from "../ui/WhiteButton";
import Button from "../ui/Button";

interface Errors {
  Name?: string;
  Email?: string;
  Phone?: string;
  Service?: string;
  Pincode?: string;
}

const ServiceForm = () => {
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [mobile, setMobile] = useState("");
  const [serviceName, setServiceName] = useState("");
  const [pincodee, setPincode] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [errors, setErrors] = useState<Errors>({});
  const [isVisible, setIsVisible] = useState(true);
  const [hasInteracted, setHasInteracted] = useState(false);
  const [isAnyInputActive, setIsAnyInputActive] = useState(false);
  const [isFormOpen, setIsFormOpen] = useState(true);
  const [hasScrolled, setHasScrolled] = useState(false);

  const { data: serviceData } = useFetch<any>(
    `${process.env.NEXT_PUBLIC_API_URL}${endpoints.getProductTitle}`,
    { cache: false }
  );
  // const { data: serviceData } = useFetch<any>(
  //   `${process.env.NEXT_PUBLIC_API_URL}${endpoints}`,
  //   { cache: false }
  // );
  const dropdownRef = useRef<HTMLDivElement>(null);
  const formRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleScroll = () => {
      if (!hasInteracted && !isAnyInputActive && window.scrollY > 0) {
        setHasScrolled(true);
        setIsFormOpen(false);
      }
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [hasScrolled, hasInteracted, isAnyInputActive]);

  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(e.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleInputChange = (field: keyof Errors, value: string) => {
    setHasInteracted(true);
    setIsVisible(true);

    if (field === "Name") {
      const nameRegex = /^[a-zA-Z\s]*$/;
      if (nameRegex.test(value)) {
        setName(value);
      }
    }
    if (field === "Email") setEmail(value);
    if (field === "Phone") setMobile(value);
    if (field === "Service") {
      setServiceName(value);
      setIsDropdownOpen(false);
    }
    if (field === "Pincode") setPincode(value);
    setErrors((prevErrors) => ({ ...prevErrors, [field]: undefined }));
  };

  const handleFocus = () => {
    setIsAnyInputActive(true);
  };

  const handleBlur = (event: React.FocusEvent<HTMLFormElement>) => {
    if (!event.currentTarget.contains(document.activeElement)) {
      setIsAnyInputActive(false);
    }
  };

  const validate = () => {
    const errors: Errors = {};
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const phoneRegex = /^\d{10}$/;
    const pincodeExactly6Digits = /^\d{6}$/;
    const pincodeStartsWithZero = /^0\d{5}$/;

    if (!name) errors.Name = "Name is required";
    else if (name.length < 3) errors.Name = "Name must be at least 3 characters";

    if (!email) {
      errors.Email = "Email is required";
    } else if (!emailRegex.test(email)) {
      errors.Email = "Enter a valid email address";
    }
    if (!mobile) {
      errors.Phone = "Mobile no is required";
    } else if (!phoneRegex.test(mobile)) {
      errors.Phone = "Enter a 10-digit mobile no";
    }
    if (!serviceName) errors.Service = "Select a service";
    if (!pincodee) {
      errors.Pincode = "Pincode is required";
    } else if (pincodeStartsWithZero.test(pincodee)) {
      errors.Pincode = "Pincode cannot start with 0";
    } else if (!pincodeExactly6Digits.test(pincodee)) {
      errors.Pincode = "Enter a valid 6-digit pincode";
    }
    return errors;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    const validationErrors = validate();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      setIsSubmitting(false);
      toast.error("Please fill all required fields correctly");
    } else {
      try {
        const apiUrl =
          "https://api-in21.leadsquared.com/v2/LeadManagement.svc/Lead.Capture?accessKey=u%24r1a694dc4e52d13c376ff37e3fac30111&secretKey=cb50b489ae36463ec43ae5d10eb9cc3b7bfc85fc";

        // Map service titles to their corresponding payload values
        const getServiceCode = (title: string) => {
          const serviceMap: { [key: string]: string } = {
            "Two Wheeler Loans": "TWL",
            "Used Car Loan": "UCL",
            "Loyalty Loans": "LLN",
            "Used Commercial Vehicle": "UCV",
            "Fixed deposits": "FXD",
          };
          return serviceMap[title] || title;
        };

        const leadsquaredPayload = [
          {
            Attribute: "FirstName",
            Value: name,
          },
          {
            Attribute: "LastName",
            Value: "",
          },
          {
            Attribute: "Phone",
            Value: mobile,
          },
          {
            Attribute: "SearchBy",
            Value: "Phone",
          },
          {
            Attribute: "mx_PinCode",
            Value: pincodee,
          },
          {
            Attribute: "mx_User_State",
            Value: "",
          },
          {
            Attribute: "mx_Type_of_products",
            Value: getServiceCode(serviceName) || "General Inquiry",
          },
        ];
        const response = await fetch(apiUrl, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(leadsquaredPayload),
        });

        const responseData = await response.json();
        if (response.ok && responseData.Status === "Success") {
          toast.success("Thank you! We'll get back to you shortly.");
          setName("");
          setEmail("");
          setMobile("");
          setServiceName("");
          setPincode("");
        } else {
          toast.error("Something went wrong. Please try again later.");
        }
      } catch (error) {
        console.error("API call error:", error);
        toast.error("An error occurred. Please try again.");
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  const toggleForm = () => {
    setIsFormOpen(!isFormOpen);
    if (!isFormOpen) {
      setIsVisible(true);
      setHasInteracted(true);
    } else {
      setName("");
      setEmail("");
      setMobile("");
      setServiceName("");
      setPincode("");
      setErrors({});
    }
  };

  return (
    <>
      {!isFormOpen && hasScrolled && (
        <button
          onClick={toggleForm}
          className="fixed bottom-6 md:right-6 right-4 bg-[#008BD2] hover:bg-[#007bb3] text-white rounded-full shadow-lg z-[100] transition-all duration-300 ease-in-out group"
          style={{
            width: "3.5rem",
            height: "3.5rem",
          }}
        >
          <div className="relative flex items-center justify-center w-full h-full">
            <MessageSquarePlus className="w-6 h-6 absolute" />
            <span className="absolute left-1/2 transform -translate-x-1/2 bottom-full mb-2 opacity-0 group-hover:opacity-100 bg-[#007bb3] text-white py-2 px-2 rounded-lg shadow-xl transition-all min-w-[80px] whitespace-nowrap text-xs font-medium duration-300 ease-in-out pointer-events-none">
              Get in Touch
            </span>
          </div>
        </button>
      )}

      <div
        ref={formRef}
        className={`enquiry_wrapper py-[13px] bg-[#008BD2]  rounded-t-lg fixed w-full bottom-0 z-[99] transition-all duration-300 ${
          isVisible ? "block" : "hidden"
        } ${isFormOpen ? "translate-y-0" : "translate-y-full"}`}
      >
        {isFormOpen && (
          <span
            onClick={toggleForm}
            className="absolute cursor-pointer -top-6 right-0 bg-[#008BD2] text-white  px-3 py-1 rounded-t transition-colors"
          >
            X
          </span>
        )}

        <div className="px-[43px] flex justify-center items-center gap-[38px] max-xl:flex-col max-xl:items-center max-xl:gap-[20px] max-lg:px-[20px]">
          <h5 className="text-[14px] font-bold text-white max-sm:text-center">
            Help us serve
            <br />
            you better
          </h5>

          <div className="input_form">
            <form
              className="flex gap-[50px] max-xl:gap-[20px] max-md:flex-col"
              onSubmit={handleSubmit}
              onFocus={handleFocus}
              onBlur={handleBlur}
            >
              <div className="input_group flex gap-[50px] flex-wrap max-xl:gap-[15px]">
                <div className="input_parent w-[calc(100%/5-20px)] max-xl:w-[calc(100%/5-12px)] max-md:w-[calc(100%/3-12px)]">
                  <input
                    className={`text-[14px] font-medium w-full text-[#FFFFFFE5] pb-[11px] border-b border-white bg-transparent placeholder:text-white focus:!border-b focus:!border-white focus:!border-t-0 focus:!border-l-0 focus:!border-r-0  ${
                      errors.Name ? "border-red-500" : ""
                    }`}
                    type="text"
                    placeholder="Name*"
                    value={name}
                    onChange={(e) => handleInputChange("Name", e.target.value)}
                  />
                  {errors.Name && (
                    <p className="text-red-800 text-sm mt-2">{errors.Name}</p>
                  )}
                </div>
                <div className="input_parent w-[calc(100%/5-50px)] max-xl:w-[calc(100%/5-12px)] max-md:w-[calc(100%/3-12px)]">
                  <input
                    className={`text-[14px] w-full font-medium text-[#FFFFFFE5] pb-[11px] border-b border-white bg-transparent placeholder:text-white focus:!border-b focus:!border-white focus:!border-t-0 focus:!border-l-0 focus:!border-r-0  ${
                      errors.Email ? "border-red-500" : ""
                    }`}
                    type="email"
                    placeholder="Email*"
                    value={email}
                    onChange={(e) => handleInputChange("Email", e.target.value)}
                  />
                  {errors.Email && (
                    <p className="text-red-800 text-sm mt-2">{errors.Email}</p>
                  )}
                </div>
                <div className="input_parent w-[calc(100%/5-50px)] max-xl:w-[calc(100%/5-12px)] max-md:w-[calc(100%/3-12px)]">
                  <input
                    className={`text-[14px] w-full font-medium text-[#FFFFFFE5] pb-[11px] border-b border-white bg-transparent placeholder:text-white focus:!border-b focus:!border-white focus:!border-t-0 focus:!border-l-0 focus:!border-r-0  ${
                      errors.Phone ? "border-red-500" : ""
                    } [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none`}
                    type="number"
                    placeholder="Mobile*"
                    value={mobile}
                    onChange={(e) => handleInputChange("Phone", e.target.value)}
                  />
                  {errors.Phone && (
                    <p className="text-red-800 text-sm mt-2">{errors.Phone}</p>
                  )}
                </div>
                <div className="input_parent w-[calc(100%/5-50px)] max-xl:w-[calc(100%/5-12px)] max-md:w-[calc(100%/2-12px)]">
                  <input
                    type="number"
                    className={`text-[14px] w-full font-medium text-[#FFFFFFE5] pb-[11px] border-b border-white bg-transparent placeholder:text-white focus:!border-b focus:!border-white focus:!border-t-0 focus:!border-l-0 focus:!border-r-0  ${
                      errors.Pincode ? "border-red-500" : ""
                    }[appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none`}
                    placeholder="Pincode*"
                    value={pincodee}
                    onChange={(e) =>
                      handleInputChange("Pincode", e.target.value)
                    }
                  />
                  {errors.Pincode && (
                    <p className="text-red-800 text-sm mt-2">
                      {errors.Pincode}
                    </p>
                  )}
                </div>
                <div
                  ref={dropdownRef}
                  className="input_parent mt-[2px] w-[calc(100%/5-50px)] max-xl:w-[calc(100%/5-12px)] max-md:w-[calc(100%/2-12px)] relative"
                >
                  <div
                    className="cursor-pointer flex items-center justify-between text-[14px] w-full font-medium text-[#FFFFFFE5] pb-[11px] border-b border-white"
                    onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                  >
                    <span className={serviceName ? "" : "text-[#ffffff]"}>
                      {serviceName || "Select Service*"}
                    </span>
                    <ChevronDown className="w-4 h-4 text-white" />
                  </div>

                  {isDropdownOpen && (
                    <div className="absolute bottom-full left-0 mb-2 bg-white w-[300px] rounded-md shadow-lg overflow-y-auto  max-h-[400px] scrollbar-custom z-50">
                      {serviceData?.map((serviceItem: any, index: number) => {
                        const getServiceCode = (title: string) => {
                          const serviceMap: { [key: string]: string } = {
                            "Two Wheeler Loans": "TWL",
                            "Used Car Loan": "UCL",
                            "Loyalty Loans": "LLN",
                            "Used Commercial Vehicle": "UCV",
                            "Fixed deposits": "FXD",
                          };
                          return serviceMap[title] || title;
                        };

                        return (
                          <div
                            key={index}
                            className="px-3 py-2 font-medium text-gray-800 hover:bg-gray-300 cursor-pointer"
                            onClick={() => {
                              setServiceName(serviceItem.Title);
                              const serviceCode = getServiceCode(
                                serviceItem.Title
                              );
                              setIsDropdownOpen(false);
                              const servicePayloadItem = {
                                Attribute: "mx_Type_of_products",
                                Value: serviceCode,
                              };

                              // Clear any validation errors
                              setErrors((prevErrors) => ({
                                ...prevErrors,
                                Service: undefined,
                              }));
                            }}
                          >
                            {serviceItem.Title}
                          </div>
                        );
                      })}
                    </div>
                  )}

                  {errors.Service && (
                    <p className="text-red-800 text-sm mt-2">
                      {errors.Service}
                    </p>
                  )}
                </div>
                {/* <div
                  ref={dropdownRef}
                  className="input_parent mt-[2px] w-[calc(100%/5-50px)] max-xl:w-[calc(100%/5-12px)] max-md:w-[calc(100%/2-12px)] relative"
                >
                  <div
                    className="cursor-pointer flex items-center justify-between text-[14px] w-full font-medium text-[#FFFFFFE5] pb-[11px] border-b border-white"
                    onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                  >
                    <span className={serviceName ? "" : "text-[#ffffff]"}>
                      {serviceName || "Select Service*"}
                    </span>
                    <ChevronDown className="w-4 h-4 text-white" />
                  </div>

                  {isDropdownOpen && (
                    <div className="absolute bottom-full left-0 mb-2 bg-white w-[300px] rounded-md shadow-lg overflow-y-auto max-h-[400px] scrollbar-custom z-50">
                      {[
                        "Gold Loan",
                        "Personal Loan",
                        "Business Loan",
                        "Home Loan",
                        "Vehicle Loan",
                        "Education Loan",
                        "Insurance",
                        "Savings Account",
                        "Current Account",
                        "Fixed Deposit",
                      ].map((service, index) => (
                        <div
                          key={index}
                          className="px-3 py-2 font-medium text-gray-800 hover:bg-gray-300 cursor-pointer"
                          onClick={() => handleInputChange("Service", service)}
                        >
                          {service}
                        </div>
                      ))}
                    </div>
                  )}

                  {errors.Service && (
                    <p className="text-red-800 text-sm mt-2">
                      {errors.Service}
                    </p>
                  )}
                </div> */}
              </div>
              <div className="btn_wraper">
                <WhiteButton
                  name={isSubmitting ? "Submitting..." : "Submit"}
                  type="submit"
                />
              </div>
            </form>
          </div>
        </div>
      </div>
    </>
  );
};

export default ServiceForm;
