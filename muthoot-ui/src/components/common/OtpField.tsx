"use client";
import React, { useState, useEffect } from "react";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp";
import { toast } from "react-hot-toast";
import axios from "axios";

const encryptData = (data: string, key: string = "MCStrlPApL112018") => {
  return btoa(data);
};

interface OtpFieldProps {
  mobileNumber: string;
  onVerificationSuccess?: () => void;
  empNo?: string;
  loanNo?: string;
  otpUID?: string;
  appId?: string;
}

export default function OtpField({
  mobileNumber,
  onVerificationSuccess,
  empNo = "MCSL104091",
  loanNo = "121212",
  otpUID = "USEROTP",
  appId = "2",
}: OtpFieldProps) {
  const [loading, setLoading] = useState(false);
  const [otp, setOtp] = useState("");
  const [timer, setTimer] = useState(60);
  const [isResending, setIsResending] = useState(false);

  useEffect(() => {
    generateOTP("F");
  }, []);

  useEffect(() => {
    if (timer > 0) {
      const interval = setInterval(() => {
        setTimer((prev) => prev - 1);
      }, 1000);
      return () => clearInterval(interval);
    }
  }, [timer]);

  const generateOTP = async (otpMode: "F" | "R") => {
    try {
      setLoading(true);
      if (otpMode === "R") {
        setIsResending(true);
      }
      const queryString = `EmpNo=${empNo}&LoanNo=${loanNo}&MobileNo=${mobileNumber}&OTPUID=${otpUID}&AppId=${appId}&OTPMode=${otpMode}`;
      const encryptedQuery = encryptData(queryString);

      const response = await axios.get(
        `https://otpsms.muthootcap.com/api/GenerateOTP?checkArgs=${encryptedQuery}`
      );

      if (response.data.StatusCode === "100") {
        toast.success("OTP sent successfully");
        setTimer(60);
      } else {
        toast.error(response.data.StatusMessage || "Failed to send OTP");
      }
    } catch (error) {
      console.error("Error generating OTP:", error);
      toast.error("Failed to send OTP. Please try again.");
    } finally {
      setLoading(false);
      setIsResending(false);
    }
  };

  const handleOtpChange = (value: string) => {
    setOtp(value);
  };

  const verifyOTP = async () => {
    if (otp.length !== 4) {
      toast.error("Please enter a valid 4-digit OTP");
      return;
    }

    try {
      setLoading(true);
      const queryString = `EmpNo=${empNo}&LoanNo=${loanNo}&OTPNo=${otp}&MobileNo=${mobileNumber}&OTPUID=${otpUID}&AppId=${appId}&OTPMode=F`;
      const encryptedQuery = encryptData(queryString);

      const response = await axios.post(
        `https://otpsms.muthootcap.com/api/VerifyOTP?checkArgs=${encryptedQuery}`
      );

      if (response.data.StatusCode === "803") {
        toast.success("OTP verified successfully");
        if (onVerificationSuccess) {
          onVerificationSuccess();
        }
      } else {
        toast.error(response.data.StatusMessage || "Invalid OTP");
      }
    } catch (error) {
      console.error("Error verifying OTP:", error);
      toast.error("Failed to verify OTP. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleResendOTP = () => {
    if (timer === 0) {
      generateOTP("R");
    }
  };

  return (
    <div className="otp_wrapper">
      <InputOTP
        maxLength={4}
        className="justify-center items-center"
        value={otp}
        onChange={handleOtpChange}
      >
        <InputOTPGroup className="flex gap-[30px] w-full justify-between">
          <InputOTPSlot index={0} />
          <InputOTPSlot index={1} />
          <InputOTPSlot index={2} />
          <InputOTPSlot index={3} />
        </InputOTPGroup>
      </InputOTP>
      <div className="resent_wraper flex justify-between items-center gap-[20px] mt-[12px]">
        <button
          className="text-[14px] font-semibold text-[#008BD2] disabled:text-gray-400"
          onClick={handleResendOTP}
          disabled={timer > 0 || isResending}
        >
          {isResending ? "Sending..." : "Resend OTP"}
        </button>
        <h6 className="text-[14px] font-semibold text-[#484848]">
          {timer > 0 ? `00:${timer.toString().padStart(2, "0")} Sec` : ""}
        </h6>
      </div>
      <div className="btn_wrapper mt-[20px]">
        <button
          className="w-full text-[16px] py-[13px] px-[33px] rounded-[7px] bg-[#008BD2] font-medium text-white disabled:bg-gray-400"
          onClick={verifyOTP}
          disabled={loading || otp.length !== 4}
        >
          {loading ? "Verifying..." : "Submit"}
        </button>
      </div>
    </div>
  );
}
