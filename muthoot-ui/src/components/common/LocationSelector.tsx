import React, { useState, useEffect } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const IndiaLocationSelector = () => {
  const [selectedState, setSelectedState] = useState("");
  const [selectedDistrict, setSelectedDistrict] = useState("");
  const [selectedLocation, setSelectedLocation] = useState("");
  const [selectedService, setSelectedService] = useState("");

  // Sample data - you would typically fetch this from an API
  const states: any = ["Maharashtra", "Karnataka", "Tamil Nadu", "Delhi"];

  const districts: any = {
    Maharashtra: ["Mumbai", "Pune", "Nagpur"],
    Karnataka: ["Bangalore", "Mysore", "Hubli"],
    "Tamil Nadu": ["Chennai", "Coimbatore", "Madurai"],
    Delhi: ["New Delhi", "North Delhi", "South Delhi"],
  };

  const locations: any = {
    Mumbai: ["Andheri", "Bandra", "Colaba"],
    Bangalore: ["Koramangala", "Indiranagar", "Whitefield"],
    Chennai: ["T Nagar", "Adyar", "Anna Nagar"],
  };

  const services: any = [
    "Healthcare",
    "Education",
    "Transportation",
    "Banking",
  ];

  const handleStateChange = (value: any) => {
    setSelectedState(value);
    setSelectedDistrict("");
    setSelectedLocation("");
  };

  const handleDistrictChange = (value: any) => {
    setSelectedDistrict(value);
    setSelectedLocation("");
  };

  const handleSearch = () => {};

  return (
    <div className="flex flex-col space-y-4 p-4 bg-blue-50 rounded-lg max-w-4xl">
      <div className="flex flex-wrap gap-4">
        <Select value={selectedState} onValueChange={handleStateChange}>
          <SelectTrigger className="w-[200px] bg-white">
            <SelectValue placeholder="Select State" />
          </SelectTrigger>
          <SelectContent>
            {states.map((state: any) => (
              <SelectItem key={state} value={state}>
                {state}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select
          value={selectedDistrict}
          onValueChange={handleDistrictChange}
          disabled={!selectedState}
        >
          <SelectTrigger className="w-[200px] bg-white">
            <SelectValue placeholder="Select District" />
          </SelectTrigger>
          <SelectContent>
            {selectedState &&
              districts[selectedState]?.map((district: any) => (
                <SelectItem key={district} value={district}>
                  {district}
                </SelectItem>
              ))}
          </SelectContent>
        </Select>

        <Select
          value={selectedLocation}
          onValueChange={setSelectedLocation}
          disabled={!selectedDistrict}
        >
          <SelectTrigger className="w-[200px] bg-white">
            <SelectValue placeholder="Select Location" />
          </SelectTrigger>
          <SelectContent>
            {selectedDistrict &&
              locations[selectedDistrict]?.map((location: any) => (
                <SelectItem key={location} value={location}>
                  {location}
                </SelectItem>
              ))}
          </SelectContent>
        </Select>

        <Select value={selectedService} onValueChange={setSelectedService}>
          <SelectTrigger className="w-[200px] bg-white">
            <SelectValue placeholder="Select Service" />
          </SelectTrigger>
          <SelectContent>
            {services.map((service: any) => (
              <SelectItem key={service} value={service}>
                {service}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <button
        onClick={handleSearch}
        className="px-6 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 w-24"
      >
        Search
      </button>
    </div>
  );
};

export default IndiaLocationSelector;
