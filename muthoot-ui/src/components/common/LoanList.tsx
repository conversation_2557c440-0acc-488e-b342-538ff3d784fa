"use client";

import React, { useState, useRef, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";

export default function LoanList({ data }: any) {
  // Sort data by SideBar_Order if it exists
  const sortedData = data
    ? [...data].sort(
        (a, b) => (a.SideBar_Order || 999) - (b.SideBar_Order || 999)
      )
    : [];

  const [isOpen, setIsOpen] = useState(false);
  const wrapperRef = useRef<HTMLDivElement>(null);

  // Function to handle clicks outside the box
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        wrapperRef.current &&
        !wrapperRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    }

    // document.addEventListener("mousedown", handleClickOutside);
    // return () => {
    //   document.removeEventListener("mousedown", handleClickOutside);
    // };
  }, []);

  return (
    <main>
      <div className="loan_mainwrapper">
        <div
          className="loan_fixedIcon fixed top-1/2 z-[70] -translate-y-[50%] right-[20px] cursor-pointer  hover:shadow-lg max-sm:right-[5px] max-sm:w-[35px] max-sm:h-[35px] max-sm:top-[60%]"
          onClick={() => setIsOpen(!isOpen)}
        >
          <svg
            width="30"
            height="30"
            viewBox="0 0 30 30"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M14.0625 18.7499C14.0625 18.0037 13.7663 17.2883 13.2384 16.7615C12.7116 16.2337 11.9963 15.9374 11.25 15.9374C9.38813 15.9374 6.54938 15.9374 4.6875 15.9374C3.94125 15.9374 3.22594 16.2337 2.69906 16.7615C2.17125 17.2883 1.875 18.0037 1.875 18.7499V25.3124C1.875 26.0587 2.17125 26.774 2.69906 27.3008C3.22594 27.8287 3.94125 28.1249 4.6875 28.1249H11.25C11.9963 28.1249 12.7116 27.8287 13.2384 27.3008C13.7663 26.774 14.0625 26.0587 14.0625 25.3124V18.7499ZM28.125 18.7499C28.125 18.0037 27.8288 17.2883 27.3009 16.7615C26.7741 16.2337 26.0588 15.9374 25.3125 15.9374C23.4506 15.9374 20.6119 15.9374 18.75 15.9374C18.0038 15.9374 17.2884 16.2337 16.7616 16.7615C16.2337 17.2883 15.9375 18.0037 15.9375 18.7499V25.3124C15.9375 26.0587 16.2337 26.774 16.7616 27.3008C17.2884 27.8287 18.0038 28.1249 18.75 28.1249H25.3125C26.0588 28.1249 26.7741 27.8287 27.3009 27.3008C27.8288 26.774 28.125 26.0587 28.125 25.3124V18.7499ZM24.0197 13.8683L27.9309 9.95709C29.0288 8.85928 29.0288 7.07802 27.9309 5.98021L24.0197 2.06896C22.9219 0.971152 21.1406 0.971152 20.0428 2.06896L16.1316 5.98021C15.0338 7.07802 15.0338 8.85928 16.1316 9.95709L20.0428 13.8683C21.1406 14.9662 22.9219 14.9662 24.0197 13.8683ZM14.0625 4.6874C14.0625 3.94115 13.7663 3.22584 13.2384 2.69896C12.7116 2.17115 11.9963 1.8749 11.25 1.8749C9.38813 1.8749 6.54938 1.8749 4.6875 1.8749C3.94125 1.8749 3.22594 2.17115 2.69906 2.69896C2.17125 3.22584 1.875 3.94115 1.875 4.6874V11.2499C1.875 11.9962 2.17125 12.7115 2.69906 13.2383C3.22594 13.7662 3.94125 14.0624 4.6875 14.0624H11.25C11.9963 14.0624 12.7116 13.7662 13.2384 13.2383C13.7663 12.7115 14.0625 11.9962 14.0625 11.2499V4.6874Z"
              fill="white"
            />
          </svg>
        </div>

        <div
          ref={wrapperRef}
          className={`loan_listingwrapper z-[95] overflow-hidden rounded-[10px] bg-[rgba(255,255,255,0.8)] px-[10px] py-[18px] pt-[30px] border border-[#2193D1] w-fit fixed top-[50%] translate-y-[-50%] right-[0] backdrop-blur-md transition-all duration-300 ${
            isOpen
              ? "opacity-100 visible right-[30px]"
              : "opacity-0 invisible right-0 "
          }`}
        >
          <div
            className="close_icon cursor-pointer"
            onClick={() => setIsOpen(false)}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="lucide lucide-x h-4 w-4 text-white"
            >
              <path d="M18 6 6 18"></path>
              <path d="m6 6 12 12"></path>
            </svg>
          </div>

          <div className="flex justify-center items-center flex-col gap-[24px]">
            {sortedData?.map((item: any, index: number) => (
              <div key={index} className="list_point">
                <Link
                  className="flex justify-center items-center gap-[4px] flex-col "
                  href={item.Link || ""}
                >
                  <Image
                    className="w-[24px] h-[24px] object-contain"
                    src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${item.Icon.url}`}
                    width={24}
                    height={24}
                    alt={item.Text || "icon"}
                    priority
                  />
                  <h6 className="text-[12px] font-semibold text-[#000000] line-clamp-1 max-w-[90px] w-full mx-auto text-center">
                    {item.Title || ""}
                  </h6>
                </Link>
              </div>
            ))}
          </div>
        </div>
      </div>
    </main>
  );
}
