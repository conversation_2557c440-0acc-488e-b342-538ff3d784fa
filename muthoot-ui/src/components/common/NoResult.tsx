import Image from "next/image";
import React from "react";

interface NoResultProps {
  image?: string;
  title?: string;
  description?: string;
}

const NoResult = ({
  image = "/noResult.svg",
  title = "No Results Found",
  description = "We could not find any results for the search term you entered. Please try again.",
}: NoResultProps) => {
  return (
    <div className="w-full text-center text-gray-500">
      <div className="empty_page max-w-[551px] pb-[10px] w-full mx-auto">
        <Image
          className="aspect-square max-w-[250px] w-full mx-auto"
          src={image}
          alt="empty"
          width={1000}
          height={1000}
        />
        <h3 className="text-[30px] text-[#0F0F0F] font-bold">{title}</h3>

        <p className="text-[16px] font-normal leading-[24px] mt-[12px] max-md:text-[14px] max-md:leading-[22px]">
          {description}
        </p>
      </div>
    </div>
  );
};

export default NoResult;
