import React from "react";

interface CommonTitleProps {
  title?: string;
  description?: string;
  highlightColor?: string;
  makeSplit?: boolean;
  defaultColor?: string;
}

const CommonTextBlue: React.FC<CommonTitleProps> = ({
  title = "",
  description,
  highlightColor = "#008BD2",
  defaultColor = "#141414",
  makeSplit = false,
}) => {
  if (!title) {
    return null;
  }

  const splittedTitle = title.split(/[\(\)]/);

  return (
    <div className="">
      {splittedTitle.map((part, index) =>
        part.trim() !== "" ? (
          <span
            key={index}
            className={
              index % 2 !== 0
                ? highlightColor.startsWith("#")
                  ? `text-[${highlightColor}]`
                  : highlightColor
                : defaultColor.startsWith("#")
                ? `text-[${defaultColor}]`
                : defaultColor
            }
          >
            {part.trim()} {makeSplit ? <br /> : null}
          </span>
        ) : null
      )}
      {description && (
        <p className="text-[16px] text-[#3C3C3C] font-light leading-[24px] md:mt-12 max-w-[422px] w-full max-xl:items-start max-xl:max-w-full">
          {description}
        </p>
      )}
    </div>
  );
};

export default CommonTextBlue;
