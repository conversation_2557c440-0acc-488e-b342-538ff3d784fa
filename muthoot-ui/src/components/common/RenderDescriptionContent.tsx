import Image from "next/image";

const RenderDescriptionContent = ({ item }: any) => {
  switch (item.type) {
    case "paragraph":
      return (
        <p className="text-[16px] font-normal text-[#484848] leading-[22px] mt-[14px] max-md:text-[14px]">
          {item.children.map((child: any, index: number) =>
            child.bold ? (
              <span
                key={index}
                className="text-[30px] font-[700] text-[#141414]"
              >
                {child.text}
              </span>
            ) : (
              <span key={index}>{child.text}</span>
            )
          )}
        </p>
      );
    case "quote":
      return (
        <h4 className="text-[28px] font-semibold text-[#008BD2] mt-[34px] max-sm:text-[28px]">
          {item.children[0].text}
        </h4>
      );
    case "image":
      return (
        <div className="img_wrapper  flex justify-start items-center gap-[21px] flex-wrap mt-[34px] max-sm:flex-col">
          <Image
            className="h-full object-cover  rounded-[10px] aspect-[373/267] w-[calc(100%/2-11px)] max-sm:w-full"
            src={`${item?.image?.url}`}
            alt={item.image.alternativeText || "blog image"}
            width={1000}
            height={1000}
          />
        </div>
      );
    default:
      return null;
  }
};

export default RenderDescriptionContent;
