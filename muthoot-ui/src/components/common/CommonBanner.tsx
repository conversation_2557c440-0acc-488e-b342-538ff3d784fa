import React from "react";
import Image from "next/image";
import <PERSON>anList from "./LoanList";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import CommonText from "./CommonText";
import CommonBannerText from "./CommonBannerText";
import Link from "next/link";

export default function CommonBanner({
  backgroundImage = "/careers_banner.png",
  title = "Careers Grow With Muthoot Capital",
  description = "",
  sideImage = "/careers_advertiseman.png",
  sideImageWidth = 421,
  sideImageHeight = 402,
  overlayColor = "rgba(0,84,157,0.9)",
  titleClassName = "text-[44px] font-semibold text-[#FECB05] line-clamp-3 max-sm:text-[35px]",
  descriptionClassName = "font-normal text-[#EAEAEA] text-[16px] mt-[16px] line-clamp-4 max-sm:text-[14px] max-sm:line-clamp-6 text-justify",
  showSideImage = true,
  underOverlay = true,
  sideImageClassName = "max-w-[520px] w-full h-full object-contain",
  gradient = true,
  backgroundImageClassName = "common_banner w-full h-full min-h-[489px]  bg-cover bg-no-repeat relative max-lg:h-full",
  backgroundImageWidth = 1920,
  backgroundImageHeight = 402,
  breadcrumbs = [{ label: "", href: "" }, { label: "" }],
  overlayColorClass = "",
}) {
  return (
    <div className={backgroundImageClassName}>
      <div className="loan_iconcomponentwrapper">{/* <LoanList /> */}</div>
      <div className="background_layer absolute inset-0 -z-[10]">
        <Image
          src={backgroundImage}
          alt="background image"
          layout="fill"
          objectFit="cover"
          priority
        />
      </div>
      <div
        className={`background_shade w-full h-full absolute top-0 left-0 -z-[5] ${overlayColorClass}`}
        style={
          overlayColor.includes(".svg") ||
          overlayColor.includes(".png") ||
          overlayColor.includes(".jpg") ||
          overlayColor.includes(".jpeg")
            ? {
                backgroundImage: `url(${overlayColor})`,
                backgroundSize: "cover",
              }
            : { backgroundColor: overlayColor }
        }
      ></div>
      {gradient && (
        <div className=" common_gradient bg-[linear-gradient(0.4deg,_#018BD2_15.46%,_rgba(7,99,172,0)_98.64%)] w-full h-full absolute top-0 left-0 -z-[5]"></div>
      )}
      <div className="container relative z-[35] min-h-[489px] h-auto flex justify-between items-start gap-[40px] max-lg:flex-col max-lg:items-start max-lg:gap-[40px] max-lg:h-full">
        <div className="content max-w-[680px] w-full  max-lg:max-w-[700px]">
          <div data-aos="fade-up" className="breadcrumbs_wrapper pt-[36px]">
            <Breadcrumb>
              <BreadcrumbList className="!gap-[9px]">
                <BreadcrumbItem className="mt-[-2px]">
                  <Link
                    className="text-[13px] font-[400] text-[#FFFFFF]"
                    href="/"
                  >
                    Home
                  </Link>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                {breadcrumbs?.map((crumb, index) => (
                  <React.Fragment key={index}>
                    <BreadcrumbItem className="mt-[-2px]">
                      {crumb.href ? (
                        <Link
                          className="text-[13px] font-[400] text-[#FFFFFF]"
                          href={crumb.href}
                        >
                          {crumb.label}
                        </Link>
                      ) : (
                        <BreadcrumbPage className="text-[13px] font-[400] text-[#FFFFFF]">
                          {crumb.label}
                        </BreadcrumbPage>
                      )}
                    </BreadcrumbItem>
                    {index < breadcrumbs.length - 1 && <BreadcrumbSeparator />}
                  </React.Fragment>
                ))}
              </BreadcrumbList>
            </Breadcrumb>
          </div>

          <div className="banner_title mt-[100px] pb-[60px] max-lg:mt-[30px] max-sm:pb-[10px]">
            <h2 data-aos="fade-up" className={titleClassName}>
              <CommonBannerText title={title} />
            </h2>
            <p data-aos="fade-up" className={descriptionClassName}>
              {description}
            </p>
          </div>
        </div>
        {showSideImage && sideImage && sideImage.trim() !== "" && (
          <div
            data-aos="fade-up"
            className="ad_banner self-end max-w-[520px] w-full h-full max-lg:max-w-[360px] max-lg:w-full max-sm:max-w-[310px] relative"
          >
            {underOverlay && (
              <div className="side_imagegradient bg-[linear-gradient(0.4deg,_rgb(1,139,210)_9.46%,_rgba(7,99,172,0)_32.64%)] w-full h-full absolute top-0 left-0 z-[5]"></div>
            )}
            <Image
              alt=""
              className={sideImageClassName}
              src={sideImage}
              width={sideImageWidth}
              height={sideImageHeight}
              priority
            />
          </div>
        )}
      </div>
    </div>
  );
}
