"use client"; // Error boundaries must be Client Components

import { useEffect } from "react";
import Image from "next/image";
import Link from "next/link";

export default function Error({
  error,
  reset,
}:{
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    console.error(error);
  }, [error]);

  return (
    <div className="flex flex-col items-center justify-center text-center pb-[125px] pt-[100px]">
      <Image
        src="/500.svg"
        alt="500"
        width={300}
        height={200}
        className="-mt-8"
      />
      <p className="md:text-[40px] text-[26px] font-bold text-[#0F0F0F]">
        Internal Server Error
      </p>
      <p className="mt-2 md:max-w-[500px] max-w-[300px] text-center md:text-[16px] text-[12px] font-light text-[#3C3C3C]">
        Oops! Something went wrong on our server. We're working to fix the
        issue.
      </p>
      <div className="mt-8 flex gap-4">
        <button
          onClick={() => reset()}
          className="px-6 py-2 bg-[#008BD2] text-white rounded-md hover:bg-[#0073b1] transition-colors"
        >
          Try Again
        </button>
        <Link
          href="/"
          className="px-6 py-2 border border-[#008BD2] text-[#008BD2] rounded-md hover:bg-[#e7f7ff] transition-colors"
        >
          Go Home
        </Link>
      </div>
    </div>
  );
}
