"use client";

import Image from "next/image";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useRef, useEffect, useState } from "react";

interface LanguageOption {
  value: string;
  label: string;
}

const LanguageSelector = ({
  isOpen,
  setIsOpen,
}: {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
}) => {
  const [selectedLang, setSelectedLang] = useState<string>("en|en");
  const [isTranslateReady, setIsTranslateReady] = useState(false);
  const [hasUserSelected, setHasUserSelected] = useState(false); // New state to track user selection
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  const options: LanguageOption[] = [
    { value: "en|en", label: "English" },
    { value: "en|bn", label: "বাংলা" },
    { value: "en|gu", label: "ગુજરાતી" },
    { value: "en|hi", label: "हिंदी" },
    { value: "en|kn", label: "ಕನ್ನಡ" },
    { value: "en|ml", label: "മലയാളം" },
    { value: "en|mr", label: "मराठी" },
    { value: "en|or", label: "ଓଡିଆ" },
    { value: "en|pa", label: "ਪੰਜਾਬੀ" },
    { value: "en|sd", label: "سنڌي" },
    { value: "en|ta", label: "தமிழ்" },
    { value: "en|te", label: "తెలుగు" },
    { value: "en|ur", label: "اردو" },
  ];

  useEffect(() => {
    const checkTranslateReady = () => {
      if (
        typeof window !== "undefined" &&
        ((window as any).doGTranslate ||
          document.querySelector(".goog-te-combo"))
      ) {
        setIsTranslateReady(true);
        return true;
      }
      return false;
    };

    if (!checkTranslateReady()) {
      const interval = setInterval(() => {
        if (checkTranslateReady()) {
          clearInterval(interval);
        }
      }, 500);

      return () => clearInterval(interval);
    }
  }, []);

  useEffect(() => {
    const storedLang = localStorage.getItem("lang");
    const storedUserSelection = localStorage.getItem("hasUserSelected"); // Check if user has previously selected

    if (storedLang && storedUserSelection === "true") {
      const matchingOption = options.find(
        (opt) => opt.value === `en|${storedLang}`
      );
      if (matchingOption) {
        setSelectedLang(matchingOption.value);
        setHasUserSelected(true); // User has previously selected a language
        if (storedLang === "ar") {
          document.body.classList.add("arabic");
        }
      }
    }
    setIsInitialized(true);
  }, [options]);

  const handleLanguageChange = (langValue: string) => {
    const langShort = langValue.split("|")[1];

    // Update state
    setSelectedLang(langValue);
    setHasUserSelected(true);
    setIsOpen(false);

    // Store in localStorage
    localStorage.setItem("lang", langShort);
    localStorage.setItem("hasUserSelected", "true");

    // Handle Arabic class
    document.body.classList.remove("arabic");
    if (langShort === "ar") {
      document.body.classList.add("arabic");
    }

    // Handle Google Translate
    if (typeof window !== "undefined") {
      setTimeout(() => {
        try {
          // Update Google Translate combo box
          const select = document.querySelector(
            ".goog-te-combo"
          ) as HTMLSelectElement;
          if (select) {
            if (langShort === "en") {
              // Reset to English (original state)
              select.value = "";
            } else {
              // Set to selected language
              select.value = langShort;
            }
            const event = new Event("change", { bubbles: true });
            select.dispatchEvent(event);
          }

          // Call doGTranslate if available
          if ((window as any).doGTranslate) {
            (window as any).doGTranslate(langValue);
          }
        } catch (error) {
          console.error("Error with translation:", error);
        }
      }, 100);
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [setIsOpen]);

  // Function to get display text
  const getDisplayText = () => {
    if (!hasUserSelected) {
      return "Switch Language";
    }
    return (
      options.find((opt) => opt.value === selectedLang)?.label ||
      "Switch Language"
    );
  };

  return (
    <div className="language_mobilwrapper max-sm:flex justify-between gap-[30px] items-center max-sm:mt-[5px]">
      <div className="language relative " ref={dropdownRef}>
        <div
          className="flex items-center cursor-pointer h-[34px] gap-[8px] px-4 rounded transition-colors duration-200 justify-start"
          onClick={() => setIsOpen(!isOpen)}
        >
          <div className="text-[13px] font-semibold text-white whitespace-nowrap">
            {getDisplayText()}
          </div>
          <Image
            src={"/drop.svg"}
            alt="language"
            width={10}
            height={10}
            className={`transition-transform duration-200 ${
              isOpen ? "rotate-180" : ""
            }`}
          />
        </div>

        <div
          className={`absolute z-[999] min-w-[110px] w-auto left-0 bg-white border border-gray-200 rounded-lg shadow-lg mt-2 overflow-hidden transition-all duration-200 max-h-[190px] overflow-y-auto ${
            isOpen
              ? "opacity-100 translate-y-0"
              : "opacity-0 translate-y-[-10px] pointer-events-none"
          }`}
        >
          {options.map((option) => {
            // Check if this option should be selected
            const isSelected = hasUserSelected
              ? option.value === selectedLang
              : option.value === "en|en"; // Default to English when no user selection

            // Check if this option should be clickable
            const isClickable = hasUserSelected
              ? true
              : option.value !== "en|en"; // English is not clickable when it's the default

            return (
              <div
                translate="no"
                key={option.value}
                className={`px-2 py-2 text-[14px] transition-all duration-200 ${
                  isSelected ? "bg-[#f0f9ff]" : "hover:bg-blue-50"
                } ${isClickable ? "cursor-pointer" : "cursor-default"}`}
                onClick={() => {
                  if (isClickable) {
                    handleLanguageChange(option.value);
                  }
                }}
              >
                <div className="flex items-center gap-1">
                  <div className="w-4 flex-shrink-0">
                    {isSelected && (
                      <svg
                        className="w-4 h-4 text-[#0078d4]"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                    )}
                  </div>
                  <span
                    className={`text-center ${
                      isSelected
                        ? "text-[#0078d4] font-medium"
                        : "text-gray-700"
                    } ${!isClickable ? "opacity-70" : ""}`}
                  >
                    {option.label}
                  </span>
                </div>
              </div>
            );
          })}
        </div>
      </div>
      <div className="mobile_scanner hidden  rounded-[3px] border border-[##FFFFFF] p-[3.5px] max-sm:flex justify-start items-center gap-[4px]">
        <Dialog>
          <DialogTrigger>
            {" "}
            <Image
              className="w-[27px] h-[28px]"
              src="/scanner.svg"
              alt="scanner"
              width={30}
              height={30}
            />
          </DialogTrigger>
          <DialogContent className="max-w-[350px] w-full max-sm:max-w-[250px] max-sm:rounded-[10px] max-sm:p-[15px]">
            <DialogHeader>
              <DialogDescription>
                <div className="qr_scanner max-w-[350px] w-full mx-auto max-sm:max-w-[250px]">
                  <Image
                    className="rounded-[10px  w-full  object-cover"
                    src="/scanner_qr.png"
                    alt="scanner"
                    width={400}
                    height={400}
                  />
                </div>
              </DialogDescription>
            </DialogHeader>
          </DialogContent>
        </Dialog>
        <h6 className="text-[12px] font-[700] text-white">Download app</h6>
      </div>
    </div>
  );
};

export default LanguageSelector;
