import React from "react";
import Image from "next/image";
import {
  Bread<PERSON>rumb,
  Bread<PERSON>rumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  <PERSON>readcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import Link from "next/link";

export default function SecondaryBanner({
  backgroundImage = "/csr_detailbanner.png",
  imageWidth = 1920,
  imageHeight = 1080,
  title = "Careers Grow With Muthoot Capital",
  bannerClass = "common_banner w-full py-[185px] bg-cover bg-no-repeat relative max-lg:h-full max-md:py-[100px] relative",
  description = "Adipiscing luctus aliquam interdum dolor aenean tellus. Ipsum et vulputate eget cursus enim. Malesuada dolor lorem",
  overlayColor = "rgb(0 0 0 / 70%)",
  titleClassName = "text-[44px] font-semibold text-white line-clamp-3 max-sm:text-[35px]",
  descriptionClassName = "font-normal text-[#EAEAEA] text-[16px] mt-[16px] line-clamp-4 max-sm:text-[14px] text-justify",
  breadcrumbs = [{ label: "", href: "" }, { label: "" }],
}) {
  return (
    <div className={bannerClass}>
      <Image
        src={backgroundImage}
        alt="Background"
        width={imageWidth}
        height={imageHeight}
        className="absolute top-0 left-0 w-full h-full object-cover"
        priority
      />

      <div
        className="background_shade w-full h-full absolute top-0 left-0"
        style={{ backgroundColor: overlayColor }}
      ></div>

      <div className="container absolute top-0 left-[50%] translate-x-[-50%] z-[9]">
        <div data-aos="fade-up" className="breadcrumbs_wrapper pt-[36px]">
          <Breadcrumb>
            <BreadcrumbList className="!gap-[9px]">
              <BreadcrumbItem className="mt-[-2px]">
                <Link
                  className="text-[13px] font-[400] text-[#FFFFFF]"
                  href="/"
                >
                  Home
                </Link>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              {breadcrumbs?.map((crumb, index) => (
                <React.Fragment key={index}>
                  <BreadcrumbItem className="mt-[-2px]">
                    {crumb.href ? (
                      <Link
                        className="text-[13px] font-[400] text-[#FFFFFF]"
                        href={crumb.href}
                      >
                        {crumb.label}
                      </Link>
                    ) : (
                      <BreadcrumbPage className="text-[13px] font-[400] text-[#FFFFFF]">
                        {crumb.label}
                      </BreadcrumbPage>
                    )}
                  </BreadcrumbItem>
                  {index < breadcrumbs.length - 1 && <BreadcrumbSeparator />}
                </React.Fragment>
              ))}
            </BreadcrumbList>
          </Breadcrumb>
        </div>
      </div>

      <div className="container relative z-[35] h-full flex justify-between items-center gap-[40px] max-lg:flex-col max-lg:items-start max-lg:gap-[40px] max-lg:h-full">
        <div className="content max-w-[698px] w-full ">
          <h2 data-aos="fade-up" className={titleClassName}>
            {title}
          </h2>
          <p data-aos="fade-up" className={descriptionClassName}>
            {description}
          </p>
        </div>
      </div>
    </div>
  );
}
