import React from "react";
import Image from "next/image";
import Link from "next/link";

export default function AwardBox({ data }: any) {
  const awards =
    data?.map((item: any) => ({
      imageSrc: item?.Thumbnail?.url
        ? `${process.env.NEXT_PUBLIC_API_BASE_URL}${item.Thumbnail.url}`
        : "/award1.png",
      imageWidth: 1000,
      imageHeight: 1000,
      title: item?.Title || "Award title not available",
      description: item?.Description,
      documentId: item?.documentId || "",
      order: item?.Award_Order || 999,
    })) || [];

  // Sort awards by order
  awards.sort((a: any, b: any) => a.order - b.order);

  return (
    <>
      {awards.map((award: any, index: number) => (
        <div
          data-aos="fade-up"
          key={index}
          className="awards_box w-[calc(100%/3-26px)] max-lg:w-[calc(100%/2-19px)] max-sm:max-w-[420px] max-sm:mx-auto max-sm:w-full"
        >
          <div className="award_image">
            <Image
              className="w-full rounded-[20px] aspect-[401/520] h-full object-cover"
              src={award.imageSrc}
              alt={`award-${index + 1}`}
              width={award.imageWidth}
              height={award.imageHeight}
              priority={index < 2}
            />
          </div>

          <div className="award_contents mt-[18px]">
            <h3 className="text-[22px] font-semibold text-[#141414] text-center">
              {award?.title}
            </h3>
            <p className="text-[16px] font-medium text-[#141414] leading-[24px] text-center mt-[16px]">
              {award?.description}
            </p>
          </div>
        </div>
      ))}
    </>
  );
}
