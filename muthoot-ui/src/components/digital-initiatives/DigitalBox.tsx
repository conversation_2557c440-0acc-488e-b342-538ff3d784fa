import React from "react";
import Image from "next/image";
import Link from "next/link";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

interface DigitalBoxItem {
  image: string;
  title: string;
  date: string;
  description: string;
  Thumbnail: any;
  url: any;
  DialogTitle?: string;
  Description?: string;
}

interface DigitalBoxProps {
  items: DigitalBoxItem[];
}

const DigitalBox: React.FC<DigitalBoxProps> = ({ items }: any) => {
  const renderRichText = (htmlContent: any) => {
    // Check if htmlContent exists and is a string
    if (!htmlContent || typeof htmlContent !== "string") {
      return (
        <div>
          <p className="font-normal text-[16px] text-[#3F3F3F] leading-[22px]">
            No description available.
          </p>
        </div>
      );
    }

    try {
      const formattedHTML = htmlContent
        .replace(/<ul>/g, '<ul class="mt-[20px] pl-[20px]">')
        .replace(
          /<li>/g,
          '<li class="font-normal list-disc text-[16px] text-[#3F3F3F] mt-[10px] leading-[22px]">'
        );

      return (
        <div
          dangerouslySetInnerHTML={{
            __html: formattedHTML,
          }}
        />
      );
    } catch (error) {
      console.error("Error processing rich text:", error);
      return (
        <div>
          <p className="font-normal text-[16px] text-[#3F3F3F] leading-[22px]">
            Error loading description.
          </p>
        </div>
      );
    }
  };

  // Ensure items is an array
  if (!items || !Array.isArray(items)) {
    return null;
  }

  return (
    <>
      {items.map((item: any, index: number) => (
        <Dialog key={index}>
          <DialogTrigger className="grid_box w-[calc(100%/3-24px)] max-lg:w-[calc(100%/2-18px)] max-sm:w-full max-sm:max-w-[420px] max-sm:mx-auto">
            <button data-aos="fade-up">
              <div className="img_wrapper relative">
                {/* <div className="date_tag py-[7px] px-[15px] rounded-[4px] bg-[rgba(0,149,218,0.6)] font-normal text-[14px] text-white w-fit absolute top-[20px] left-[20px]">
                  {item?.date || "No date"}
                </div> */}
                <Image
                  className="w-full h-full aspect-[402/248] object-cover rounded-[10px]"
                  src={
                    item?.Thumbnail?.url
                      ? `${process.env.NEXT_PUBLIC_API_BASE_URL}${item.Thumbnail.url}`
                      : "/digital_image.png"
                  }
                  alt={item?.title || "Digital initiative"}
                  width={1000}
                  height={1000}
                  priority
                />
              </div>
              <h3 className="text-[22px] font-medium text-[#0F0F0F] mt-[16px] line-clamp-1 text-left">
                {item?.title || "No title"}
              </h3>
            </button>
          </DialogTrigger>

          <DialogContent className="max-w-[700px] p-[40px]">
            <DialogHeader>
              <h2 className="text-[20px] font-bold text-black">
                {item?.DialogTitle || item?.title || "Digital Initiative"}
              </h2>
              <DialogDescription className="text-left">
                {renderRichText(item?.Description)}
              </DialogDescription>
            </DialogHeader>
          </DialogContent>
        </Dialog>
      ))}
    </>
  );
};

export default DigitalBox;
