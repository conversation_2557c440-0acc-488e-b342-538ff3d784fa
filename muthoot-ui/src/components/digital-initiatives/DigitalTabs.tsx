import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import DigitalBox from "./DigitalBox";
import { GetDigitalInitiativeCategories } from "@/lib/api/general";
import Image from "next/image";
import { DigitalBoxSkeleton } from "../skeleton/DigitalBoxSkeleton";
import NoResult from "../common/NoResult";

export default async function DigitalTabs() {
  const { data } = await GetDigitalInitiativeCategories();
  if (!data || data.length === 0) {
    return (
      <div>
        <NoResult
          title="No Results Found"
          description="No digital initiatives found"
        />
      </div>
    );
  }

  const formatDigitalItems = (category: any) => {
    if (!category?.digital_initiatives) return [];

    return category.digital_initiatives.map((initiative: any) => ({
      Thumbnail: initiative.Thumbnail,
      url: initiative.url,
      image: initiative.Thumbnail?.url || "/digital_image.png",
      title: initiative.Title,
      date: new Date(initiative.Date).toLocaleDateString("en-US", {
        day: "numeric",
        month: "long",
        year: "numeric",
      }),
      Description: initiative.Description,
      DialogTitle: initiative.DialogTitle,
      documentId: initiative.documentId,
    }));
  };

  return (
    <section className="w-full min-h-full ">
      <div className="w-full">
        <Tabs defaultValue={data[0]?.Name} className="w-full">
          <div className="w-full overflow-x-auto custom-scrollbar">
            <TabsList className="inline-flex w-auto min-w-full lg:grid lg:w-full lg:grid-cols-4 gap-[12px]">
              {data.map((category: any) => (
                <TabsTrigger
                  key={category.id}
                  value={category.Name}
                  className="whitespace-nowrap px-[15px] max-sm:text-[14px] max-sm:p-[12px]"
                >
                  {category.Name}
                </TabsTrigger>
              ))}
            </TabsList>
          </div>

          {data?.length > 0 ? (
            data?.map((category: any) => (
              <TabsContent key={category.id} value={category.Name}>
                <div className="flex flex-wrap gap-6 mt-8 justify-center">
                  <DigitalBox items={formatDigitalItems(category)} />
                </div>
              </TabsContent>
            ))
          ) : (
            <>
              <div className="flex flex-wrap gap-6 mt-8 justify-center">
                <DigitalBoxSkeleton />
                <DigitalBoxSkeleton />
                <DigitalBoxSkeleton />
                <DigitalBoxSkeleton />
                <DigitalBoxSkeleton />
                <DigitalBoxSkeleton />
              </div>
            </>
          )}
        </Tabs>
      </div>
    </section>
  );
}
