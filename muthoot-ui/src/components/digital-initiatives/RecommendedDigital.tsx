import React from "react";
import Image from "next/image";
import Link from "next/link";

interface Category {
  name: string;
}

interface Digital {
  documentId: string;
  Title: string;
  Description?: Array<{
    children?: Array<{
      text?: string;
    }>;
  }>;
  Thumbnail?: {
    formats?: {
      thumbnail?: {
        url: string;
      };
    };
    url?: string;
    alternativeText?: string;
  };
  Digital_initiative_categories?: Category[];
}

interface RecommendedDigitalProps {
  currentDigital: Digital;
  allDigital: Digital[];
}

const RecommendedDigital = ({
  currentDigital,
  allDigital,
}: RecommendedDigitalProps) => {
  const getRecommendedDigital = () => {
    const currentCategories =
      currentDigital?.Digital_initiative_categories?.filter(
        (category) => category?.name
      ).map((category) => category.name.toLowerCase()) || [];

    const relatedDigital = (allDigital || []).filter((digital) => {
      if (!digital || digital?.documentId === currentDigital?.documentId)
        return false;

      const digitalCategories =
        digital?.Digital_initiative_categories?.filter(
          (category) => category?.name
        ).map((category) => category.name.toLowerCase()) || [];

      return digitalCategories.some((category) =>
        currentCategories.includes(category)
      );
    });

    return relatedDigital.length > 0
      ? relatedDigital.slice(0, 4)
      : (allDigital || [])
          .filter(
            (digital) => digital?.documentId !== currentDigital?.documentId
          )
          .slice(0, 4);
  };

  const recommendedDigital = getRecommendedDigital();

  return (
    <div className="sub_contents max-w-[447px] w-full sticky top-[20px] max-lg:max-w-full max-lg:relative max-lg:top-0">
      <h3
        data-aos="fade-up"
        className="text-[32px] font-semibold text-[#008BD2] max-sm:text-[28px]"
      >
        Recommended Digital Initiatives
      </h3>
      <div className="sub_list mt-[24px]">
        {recommendedDigital?.map((digital: Digital) => (
          <div
            data-aos="fade-up"
            key={digital?.documentId || Math.random().toString()}
            className="sub_cards flex justify-start items-center gap-[30px] mt-[22px] max-sm:flex-col max-sm:items-start max-sm:gap-[15px]"
          >
            <div className="sub_image max-w-[173px] w-full">
              <Image
                className="w-full h-full object-cover rounded-[10px] aspect-[173/124]"
                src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${
                  digital?.Thumbnail?.formats?.thumbnail?.url ||
                  digital?.Thumbnail?.url ||
                  "/placeholder.jpg"
                }`}
                alt={
                  digital?.Thumbnail?.alternativeText ||
                  digital?.Title ||
                  "Digital initiative image"
                }
                width={173}
                height={124}
              />
            </div>
            <div className="content max-w-[244px] w-full max-lg:max-w-[calc(100%-173px)] max-lg:w-full max-sm:max-w-full">
              <h4 className="text-[20px] leading-[24px] font-medium text-[#141414] line-clamp-1">
                {digital?.Title || "Untitled Initiative"}
              </h4>
              <p className="text-[14px] leading-[18px] font-normal text-[#3F3F3F] mt-[8px] line-clamp-2 my-[8px]">
                {digital?.Description?.[0]?.children?.[0]?.text?.slice(
                  0,
                  100
                ) || "No description available"}
                ...
              </p>
              <Link
                className="text-[16px] font-semibold text-[#008BD2] hover:underline"
                href={`/digital-initiatives/${digital?.documentId || ""}`}
              >
                Read Now
              </Link>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default RecommendedDigital;
