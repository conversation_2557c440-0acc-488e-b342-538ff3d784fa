import React from "react";
import Image from "next/image";
import Link from "next/link";

export default async function InvesterListBox({ data }: any) {
  const sortedInvestors = data?.sort((a: any, b: any) => {
    const orderA = a.InvestorsOrder ?? Number.MAX_SAFE_INTEGER;
    const orderB = b.InvestorsOrder ?? Number.MAX_SAFE_INTEGER;
    return orderA - orderB;
  });
  return (
    <>
      {sortedInvestors?.map((item: any, index: any) => {
        return (
          <Link
            data-aos="fade-up"
            href={`/investors/${item?.documentId}`}
            key={item.id}
            className="awards_box w-[calc(100%/3-22px)] max-lg:w-[calc(100%/2-16px)] max-sm:max-w-[420px] max-sm:mx-auto max-sm:w-full"
          >
            <div className="award_image">
              <Image
                className="w-full rounded-[20px] aspect-[406/333] h-full object-cover"
                src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${item?.Thumbnail?.url}`}
                alt={item.Title || `document-${index + 1}`}
                width={1000}
                height={1000}
                priority={index < 2}
              />
            </div>
            <div className="award_contents mt-[18px]">
              <p className="text-[20px] font-semibold text-[#141414] leading-[26px] text-center w-[95%] mx-auto">
                {item.Title || "Document Title"}
              </p>
            </div>
          </Link>
        );
      })}
    </>
  );
}
