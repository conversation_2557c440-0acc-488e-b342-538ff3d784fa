"use client";
import React, { useState, useEffect } from "react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import ReportBox from "./ReportBox";
import NoResult from "../common/NoResult";

export default function ReportTab({ reportData }: any) {
  const [uniqueTabs, setUniqueTabs] = useState<any[]>([]);
  const [defaultTab, setDefaultTab] = useState<string>("");
  const [activeTab, setActiveTab] = useState<string>("");
  const [hasInvestorTabs, setHasInvestorTabs] = useState<boolean>(false);

  useEffect(() => {
    if (reportData?.Attachments) {
      // Check if any attachment has investors_tabs
      const hasTabsData = reportData.Attachments.some(
        (attachment: any) =>
          attachment.investors_tabs && attachment.investors_tabs.length > 0
      );

      setHasInvestorTabs(hasTabsData);

      if (hasTabsData) {
        // Original logic for when investors_tabs exist
        const allTabs = reportData.Attachments.flatMap(
          (attachment: any) => attachment.investors_tabs || []
        );

        const uniqueTabsMap = new Map();
        allTabs.forEach((tab: any) => {
          if (!uniqueTabsMap.has(tab.documentId)) {
            uniqueTabsMap.set(tab.documentId, tab);
          }
        });

        const tabsArray = Array.from(uniqueTabsMap.values());
        setUniqueTabs(tabsArray);

        if (tabsArray.length > 0) {
          const firstTabName = tabsArray[0].Tab_Name;
          setDefaultTab(firstTabName);
          setActiveTab(firstTabName);
        }
      } else {
        // Reset tab states when no investors_tabs
        setUniqueTabs([]);
        setDefaultTab("");
        setActiveTab("");
      }
    }
  }, [reportData]);

  const getFilteredAttachments = (tabName: string) => {
    if (!reportData?.Attachments) return [];

    return reportData.Attachments.filter((attachment: any) =>
      attachment.investors_tabs?.some((tab: any) => tab.Tab_Name === tabName)
    );
  };

  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };

  // If no attachments at all, show no result
  if (!reportData?.Attachments || reportData.Attachments.length === 0) {
    return (
      <NoResult
        title="No Results Available"
        description="There are no results to display at the moment. Please check back later."
      />
    );
  }

  // If no investors_tabs, show attachments directly without tabs
  if (!hasInvestorTabs) {
    return (
      <div className="annual_tab">
        <div className="annual_reportwrapper flex justify-center items-start gap-[32px] flex-wrap mt-[42px] max-xl:gap-[20px]">
          <ReportBox
            reportData={{
              Attachments: reportData.Attachments,
            }}
          />
        </div>
      </div>
    );
  }

  // If no active tab but has investor tabs, show no result
  if (!activeTab || uniqueTabs.length === 0) {
    return (
      <NoResult
        title="No Results Available"
        description="There are no results to display at the moment. Please check back later."
      />
    );
  }

  // Show tabs interface when investors_tabs exist
  return (
    <div className="annual_tab">
      <Tabs
        defaultValue={defaultTab}
        value={activeTab}
        onValueChange={handleTabChange}
        className="w-full"
      >
        <div className="flex justify-center w-full">
          <TabsList className="flex annual_trigger justify-start overflow-x-auto pb-[6px] ">
            {uniqueTabs.map((tab: any) => (
              <TabsTrigger
                className="max-lg:px-[30px]"
                key={tab.documentId}
                value={tab.Tab_Name}
              >
                {tab.Tab_Name}
              </TabsTrigger>
            ))}
          </TabsList>
        </div>

        {uniqueTabs.map((tab: any) => (
          <TabsContent key={tab.documentId} value={tab.Tab_Name}>
            <div className="annual_reportwrapper flex justify-start items-start gap-[32px] flex-wrap mt-[42px] max-xl:gap-[20px]">
              <ReportBox
                reportData={{
                  Attachments: getFilteredAttachments(tab.Tab_Name),
                }}
              />
            </div>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
}
