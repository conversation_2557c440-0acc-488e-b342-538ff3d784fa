import React from "react";
export default function ReportBox({ reportData }: any) {
  return (
    <>
      {reportData?.Attachments?.map((report: any) => (
        <div
          data-aos="fade-up"
          key={report.id}
          className="download_box  rounded-[14px] self-stretch border border-[#B7DFF4] bg-[#E7F7FF] p-[30px] w-[calc(100%/3-22px)] max-xl:w-[calc(100%/3-14px)] max-lg:w-[calc(100%/2-10px)]  max-md:w-full"
        >
          <div className="file_wrapper flex justify-start items-center gap-[24px] max-sm:gap-[12px]">
            <div className="file_icon max-w-[56px] w-full">
              <svg
                width="56"
                height="56"
                viewBox="0 0 56 56"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M44.8967 17.4999H46.4804C46.6925 17.5077 46.902 17.451 47.0813 17.3373C47.2605 17.2236 47.4011 17.0583 47.4845 16.8631C47.5678 16.6679 47.5901 16.452 47.5483 16.2439C47.5066 16.0358 47.4027 15.8453 47.2504 15.6974L36.8204 5.29364C36.6753 5.13684 36.4857 5.02808 36.277 4.98187C36.0684 4.93566 35.8506 4.95422 35.6528 5.03506C35.455 5.1159 35.2865 5.25517 35.17 5.43427C35.0534 5.61338 34.9943 5.82379 35.0004 6.03739V7.62114C35.0004 8.91991 35.2565 10.2059 35.7541 11.4056C36.2516 12.6053 36.9809 13.6951 37.9 14.6127C38.8192 15.5302 39.9103 16.2575 41.1109 16.7529C42.3114 17.2484 43.5979 17.5022 44.8967 17.4999Z"
                  fill="#008BD2"
                />
                <path
                  d="M30.4164 28.1137C30.1359 27.7775 29.7788 27.5134 29.3752 27.3437C28.8426 27.1387 28.2742 27.0435 27.7039 27.0637H26.5664V34.5799H27.7389C28.2681 34.5961 28.7958 34.516 29.2964 34.3437C29.704 34.193 30.0678 33.9434 30.3552 33.6174C30.6563 33.2561 30.8716 32.8314 30.9852 32.3749C31.1334 31.8305 31.2041 31.2679 31.1952 30.7037C31.1991 30.2106 31.1373 29.7192 31.0114 29.2424C30.8928 28.8293 30.6903 28.445 30.4164 28.1137Z"
                  fill="#008BD2"
                />
                <path
                  d="M44.8963 21C43.1379 21.0023 41.3963 20.658 39.771 19.9866C38.1458 19.3153 36.6689 18.3302 35.4247 17.0877C34.1805 15.8451 33.1935 14.3695 32.5201 12.7451C31.8466 11.1208 31.5 9.37966 31.5 7.62127C31.5 6.53127 31.0682 5.48565 30.2991 4.71327C29.53 3.94089 28.4862 3.50464 27.3962 3.50002H16.8962C15.5975 3.49771 14.311 3.75154 13.1104 4.24696C11.9099 4.74239 10.8188 5.46968 9.8996 6.38724C8.98042 7.3048 8.25119 8.3946 7.75365 9.59429C7.2561 10.794 7 12.08 7 13.3788V42.6213C7 43.92 7.2561 45.2061 7.75365 46.4057C8.25119 47.6054 8.98042 48.6952 9.8996 49.6128C10.8188 50.5303 11.9099 51.2576 13.1104 51.7531C14.311 52.2485 15.5975 52.5023 16.8962 52.5H39.1475C41.763 52.4931 44.2689 51.4492 46.1159 49.5973C47.9628 47.7455 49 45.2367 49 42.6213V25.1213C49 24.0313 48.5682 22.9857 47.7991 22.2133C47.03 21.4409 45.9862 21.0046 44.8963 21ZM22.295 30.31C22.1033 30.7762 21.8034 31.1902 21.42 31.5175C21.0225 31.8588 20.5573 32.1123 20.055 32.2613C19.4387 32.442 18.7984 32.5276 18.1562 32.515H17.2638V36.015C17.2659 36.0721 17.2472 36.1281 17.2113 36.1725C17.1621 36.2272 17.0984 36.2667 17.0275 36.2863C16.9164 36.3209 16.802 36.3443 16.6862 36.3563C16.3225 36.3907 15.9563 36.3907 15.5925 36.3563C15.4738 36.3449 15.3565 36.3215 15.2425 36.2863C15.171 36.2681 15.1069 36.2284 15.0587 36.1725C15.0235 36.1277 15.0049 36.072 15.0062 36.015V26.1013C14.9976 25.9916 15.0119 25.8813 15.048 25.7774C15.0841 25.6735 15.1414 25.5782 15.2162 25.4975C15.3662 25.3603 15.5644 25.288 15.7675 25.2963H18.3925C18.655 25.2963 18.9029 25.2963 19.1363 25.2963C19.4301 25.3242 19.7222 25.368 20.0113 25.4275C20.3624 25.5017 20.7012 25.6255 21.0175 25.795C21.3483 25.9635 21.645 26.192 21.8925 26.4688C22.1366 26.7445 22.3239 27.0656 22.4437 27.4138C22.5722 27.8061 22.6344 28.2172 22.6275 28.63C22.6284 29.2065 22.5154 29.7774 22.295 30.31ZM33.1187 33.25C32.8863 33.9166 32.496 34.517 31.9812 35C31.4626 35.4698 30.8429 35.814 30.17 36.0063C29.3443 36.2366 28.4896 36.3456 27.6325 36.33H25.0075C24.8349 36.3348 24.6666 36.2756 24.535 36.1638C24.4653 36.0928 24.412 36.0075 24.3788 35.9138C24.3455 35.8201 24.3331 35.7203 24.3425 35.6213V25.9963C24.3331 25.8973 24.3455 25.7974 24.3788 25.7037C24.412 25.61 24.4653 25.5247 24.535 25.4538C24.6666 25.342 24.8349 25.2828 25.0075 25.2875H27.8425C28.6929 25.2688 29.5408 25.387 30.3537 25.6375C31.0068 25.8425 31.605 26.1925 32.1037 26.6613C32.5871 27.1309 32.9554 27.7058 33.18 28.3413C33.422 29.0776 33.5345 29.8503 33.5125 30.625C33.5371 31.5164 33.4038 32.4051 33.1187 33.25ZM41.545 26.635C41.5359 26.7313 41.5123 26.8257 41.475 26.915C41.4517 26.9744 41.4159 27.0281 41.37 27.0725C41.3318 27.1073 41.2817 27.1261 41.23 27.125H37.625V30.1H41.02C41.0655 30.0853 41.1145 30.0853 41.16 30.1C41.2072 30.1397 41.2434 30.191 41.265 30.2488C41.3016 30.3352 41.3251 30.4266 41.335 30.52C41.3648 30.8109 41.3648 31.1041 41.335 31.395C41.3245 31.4911 41.3009 31.5853 41.265 31.675C41.2437 31.7354 41.2076 31.7896 41.16 31.8325C41.1201 31.8635 41.0704 31.879 41.02 31.8763H37.625V35.9975C37.627 36.0573 37.6084 36.116 37.5725 36.1638C37.5237 36.2188 37.4598 36.2584 37.3888 36.2775C37.275 36.3135 37.1576 36.337 37.0388 36.3475C36.675 36.382 36.3088 36.382 35.945 36.3475C35.826 36.3377 35.7086 36.3142 35.595 36.2775C35.5247 36.2567 35.4613 36.2174 35.4113 36.1638C35.374 36.1167 35.3553 36.0575 35.3587 35.9975V26.0138C35.3494 25.9148 35.3618 25.8149 35.395 25.7212C35.4283 25.6275 35.4816 25.5422 35.5513 25.4713C35.6829 25.3595 35.8511 25.3003 36.0237 25.305H41.23C41.2803 25.3031 41.3298 25.3185 41.37 25.3488C41.4173 25.3919 41.4533 25.446 41.475 25.5063C41.5121 25.5986 41.5357 25.6959 41.545 25.795C41.5534 25.9407 41.5534 26.0868 41.545 26.2325C41.553 26.3724 41.553 26.5126 41.545 26.6525V26.635Z"
                  fill="#008BD2"
                />
                <path
                  d="M19.5319 27.3086C19.336 27.2066 19.1219 27.1441 18.9019 27.1249C18.6757 27.0944 18.4476 27.0798 18.2194 27.0811H17.2656V30.8349H18.2719C18.5702 30.8396 18.8669 30.7892 19.1469 30.6861C19.3743 30.6011 19.5783 30.4631 19.7419 30.2836C19.8999 30.1028 20.0191 29.8913 20.0919 29.6624C20.1745 29.4081 20.2159 29.1423 20.2144 28.8749C20.2276 28.5452 20.1584 28.2174 20.0131 27.9211C19.9131 27.676 19.7463 27.4639 19.5319 27.3086Z"
                  fill="#008BD2"
                />
              </svg>
            </div>
            <h4 className="text-[20px] font-semibold leading-[26px] text-[#141414] max-w-[calc(100%-80px)] max-sm:max-w-[calc(100%-68px)] ">
              {report?.Title}
            </h4>
          </div>

          <p className="text-[16px] leading-[26px] font-light text-[#505050] mt-[11px]">
            {report?.Description}
          </p>
          <a
            href={process.env.NEXT_PUBLIC_API_BASE_URL + report?.File?.url}
            className="download_btn text-[14px] mt-[17px] font-semibold w-fit text-white bg-[#008BD2] py-[7px] px-[16px] rounded-[8px] flex items-center gap-[7px]"
            target="_blank"
          >
            Download{" "}
            <svg
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g clipPath="url(#clip0_0_12087)">
                <path
                  d="M20 10C20 4.48613 15.5139 0 10 0C4.48613 0 0 4.48613 0 10C0 15.5139 4.48613 20 10 20C15.5139 20 20 15.5139 20 10ZM0.999184 10C0.999184 5.03671 5.03671 0.999184 10 0.999184C14.9633 0.999184 19.0008 5.03671 19.0008 10C19.0008 14.9633 14.9633 19.0008 10 19.0008C5.03671 19.0008 0.999184 14.9633 0.999184 10Z"
                  fill="white"
                />
                <path
                  d="M10.3539 14.6983L13.1638 11.8883C13.3596 11.6926 13.3596 11.3785 13.1638 11.1828C12.9681 10.987 12.6541 10.987 12.4583 11.1828L10.5007 13.1403V5.65666C10.5007 5.37934 10.2764 5.15503 9.99908 5.15503C9.72176 5.15503 9.49745 5.37934 9.49745 5.65666V13.1363L7.53986 11.1787C7.34411 10.9829 7.03008 10.9829 6.83432 11.1787C6.63856 11.3744 6.63856 11.6885 6.83432 11.8842L9.64427 14.6942C9.74215 14.7921 9.86858 14.841 9.99908 14.841C10.1296 14.841 10.256 14.7961 10.3539 14.6983Z"
                  fill="white"
                />
              </g>
              <defs>
                <clipPath id="clip0_0_12087">
                  <rect width="20" height="20" fill="white" />
                </clipPath>
              </defs>
            </svg>
          </a>
        </div>
      ))}
    </>
  );
}
