"use client";
import React from "react";
import Image from "next/image";
import Link from "next/link";

export default function InvestorBox({ data }: any) {
  const sortedData = data?.sort((a: any, b: any) => {
    const orderA = a.InvestorsOrder ?? Number.MAX_SAFE_INTEGER;
    const orderB = b.InvestorsOrder ?? Number.MAX_SAFE_INTEGER;
    return orderA - orderB;
  });

  const groupedData = sortedData?.reduce((acc: any, item: any) => {
    const sectionTitle = item.investors_section_title?.SectionTitle || "Others";
    const sectionId = item.investors_section_title?.id || "others";

    if (!acc[sectionId]) {
      acc[sectionId] = {
        title: sectionTitle,
        items: [],
      };
    }

    acc[sectionId].items.push(item);
    return acc;
  }, {});

  const sectionsArray = Object.values(groupedData || {}).sort(
    (a: any, b: any) => {
      const firstItemOrderA =
        a.items[0]?.InvestorsOrder ?? Number.MAX_SAFE_INTEGER;
      const firstItemOrderB =
        b.items[0]?.InvestorsOrder ?? Number.MAX_SAFE_INTEGER;
      return firstItemOrderA - firstItemOrderB;
    }
  );

  return (
    <>
      {sectionsArray.map((section: any, sectionIndex: number) => (
        <div key={sectionIndex} className="section_group mb-[60px] last:mb-0">
          <h3 className="text-[25px] font-semibold text-[white] max-w-[400px] w-full mb-[20px]">
            {section.title}
          </h3>

          <div className="grid grid-cols-4 gap-[24px] max-lg:grid-cols-2 max-lg:gap-[18px] max-md:grid-cols-2 max-sm:grid-cols-1">
            {section.items.map((policy: any, index: any) => (
              <div
                data-aos="fade-up"
                key={policy.id || index}
                className="box rounded-[14px] border border-[#B7DFF4] bg-[#E7F7FF] self-stretch p-[30px] relative overflow-hidden max-md:p-[20px] hover:bg-[#abe3ff]"
              >
                <div className="policy_vector absolute top-0 right-0 z-[1]">
                  <Image
                    className="w-full h-full object-cover"
                    src="/policy-vector.png"
                    alt={`policy-vector-${sectionIndex}-${index + 1}`}
                    width={310}
                    height={238}
                    priority={sectionIndex === 0 && index < 2}
                  />
                </div>

                <div className="content relative z-[50] flex justify-between flex-col h-full">
                  <div className="content_wrapper">
                    <h3 className="text-[20px] font-semibold text-[#141414] leading-[26px] relative z-[50]">
                      {policy?.Title}
                    </h3>
                    {/* <p className="text-[16px] leading-[22px] text-[#484848] mt-[12px] relative z-[50]">
                      {policy?.Description}
                    </p> */}
                  </div>
                  <button className="view_all mt-[18px] text-[16px] w-fit font-bold text-[#008BD2] flex items-center gap-[4px] relative z-[50]">
                    <Link href={`/investors/${policy?.documentId}`}>
                      View All
                    </Link>
                    <svg
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M9 18L15 12L9 6"
                        stroke="#008BD2"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}
    </>
  );
}
