"use client";

const Loader = () => {
  return (
    <div className="flex h-screen items-center justify-center bg-gradient-to-br from-slate-50 via-white to-blue-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 fixed z-[101] top-0 w-full left-0">
      <div className="flex flex-col items-center space-y-12">
        {/* Modern Loader Ring */}
        <div className="relative">
          <div className="w-16 h-16 border-4 border-slate-200 dark:border-slate-700 rounded-full relative overflow-hidden">
            <div className="absolute inset-0 border-4 border-transparent border-t-[#008BD2] border-r-[#008BD2] rounded-full animate-spin"></div>
          </div>
          <div
            className="absolute inset-2 w-12 h-12 border-2 border-slate-100 dark:border-slate-600 border-b-blue-400 rounded-full animate-spin opacity-60"
            style={{ animationDirection: "reverse", animationDuration: "2s" }}
          ></div>
        </div>
      </div>
    </div>
  );
};

export default Loader;
