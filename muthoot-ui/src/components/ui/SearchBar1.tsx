"use client";
import React, { useState, useEffect, useRef } from "react";
import Image from "next/image";
import Link from "next/link";
import { debounce } from "lodash";
import { useRouter } from "next/navigation";
import CommonText from "../common/CommonText";
import {
  She<PERSON>,
  <PERSON>et<PERSON>ontent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
  SheetClose,
} from "./sheet";
import CommonTextBlue from "../common/CommonTextBlue";
interface ServiceItem {
  documentId: string;
  title: string;
  desc: string;
  thumbnail: {
    url: string;
    name: string;
  };
}

interface PolicyItem {
  documentId: string;
  title: string;
  desc: string;
  image?: {
    url: string;
    name: string;
  };
}

interface BranchData {
  documentId: string;
  header: {
    id: number;
    title: string;
    desc: string;
  };
}
interface serviceData {
  documentId: string;
  header: {
    id: number;
    title: string;
    desc: string;
  };
}
interface PolicyData {
  documentId: string;
  header: {
    id: number;
    title: string;
    desc: string;
  };
}

interface investmentCalculatorData {
  documentId: string;
  header: {
    id: number;
    title: string;
    desc: string;
  };
}
// interface AGMData {
//   documentId: string;
//   header: {
//     id: number;
//     title: string;
//     desc: string;
//   };
// }
interface balanceSheetData {
  documentId: string;
  header: {
    id: number;
    title: string;
    desc: string;
  };
}
interface presentationData {
  documentId: string;
  header: {
    id: number;
    title: string;
    desc: string;
  };
}
interface eventsData {
  documentId: string;
  header: {
    id: number;
    title: string;
    desc: string;
  };
}
interface csrData {
  documentId: string;
  header: {
    id: number;
    title: string;
    desc: string;
  };
}
interface insuranceData {
  documentId: string;
  header: {
    id: number;
    title: string;
    desc: string;
  };
}
interface atmData {
  documentId: string;
  header: {
    id: number;
    title: string;
    desc: string;
  };
}
interface galleryData {
  documentId: string;
  header: {
    id: number;
    title: string;
    desc: string;
  };
}
interface careerData {
  documentId: string;
  header: {
    id: number;
    title: string;
    desc: string;
  };
}
interface blogData {
  documentId: string;
  header: {
    id: number;
    title: string;
    desc: string;
  };
}
interface newsEventsData {
  documentId: string;
  header: {
    id: number;
    title: string;
    desc: string;
  };
}
interface productsData {
  documentId: string;
  header: {
    id: number;
    title: string;
    desc: string;
  };
}

interface SearchResults {
  service: ServiceItem[];
  policies: PolicyItem[];
  aboutHeader: any | null;
  productList: any | null;
  products: any[];
  awards: any | null;
  blogs: any | null;
  branch: BranchData | null;
  serviceHeader: serviceData | null;
  policyHeader: PolicyData | null;
  investmentCalculatorHeader: investmentCalculatorData | null;
  balanceSheetData: balanceSheetData | null;
  presentationData: presentationData | null;
  eventsData: eventsData | null;
  csrData: csrData | null;
  insuranceData: insuranceData | null;
  atmData: atmData | null;
  galleryData: galleryData | null;
  careerData: careerData | null;
  blogData: blogData | null;
  newsEventsData: newsEventsData | null;
  loading: boolean;
  error: string | null;
  showNoResults?: boolean;
}

type SearchBarProps = {
  showSheetTrigger?: boolean;
  handleSearchOpens: () => void;
};

const SearchBar1: React.FC<SearchBarProps> = ({
  showSheetTrigger = true,
  handleSearchOpens,
}) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const [searchActive, setSearchActive] = useState(false);
  const [searchResults, setSearchResults] = useState<any>({
    aboutHeader: null,
    productList: null,
    products: [],
    awards: null,
    blogs: null,
    branch: null,
    careers: null,
    contact: null,
    corporateEthos: null,
    csr: null,
    digital: null,
    director: null,
    history: null,
    investors: null,
    leader: null,
    Media: null,
    news: null,
    policy: null,
    promoter: null,
    testimonials: null,
    service: [],
    loading: false,
    error: null,
    showNoResults: false,
  });
  const [isMounted, setIsMounted] = useState(false);
  const router = useRouter();
  const [isSheetAnimating, setIsSheetAnimating] = useState(false);
  const desktopInputRef = useRef<HTMLInputElement>(null);
  const mobileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (isSheetOpen && desktopInputRef.current) {
      const timer = setTimeout(() => {
        desktopInputRef.current?.focus();
      }, 150);

      return () => clearTimeout(timer);
    }
  }, [isSheetOpen]);

  useEffect(() => {
    if (searchActive && mobileInputRef.current) {
      const timer = setTimeout(() => {
        mobileInputRef.current?.focus();
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [searchActive]);

  const handleSearchOpen = () => {
    setIsSheetAnimating(true);
    setIsSheetOpen(true);
    setSearchQuery("");
    setSearchResults({
      aboutHeader: null,
      productList: null,
      products: [],
      awards: null,
      blogs: null,
      branch: null,
      careers: null,
      contact: null,
      corporateEthos: null,
      csr: null,
      digital: null,
      director: null,
      history: null,
      investors: null,
      Media: null,
      leader: null,
      news: null,
      policy: null,
      promoter: null,
      testimonials: null,
      service: [],
      policies: [],
      loading: false,
      error: null,
      showNoResults: false,
    });
    setTimeout(() => {
      setIsSheetAnimating(false);
    }, 300);
  };

  const handleSheetClose = () => {
    setIsSheetAnimating(true);
    setIsSheetOpen(false);

    setTimeout(() => {
      setIsSheetAnimating(false);
      setSearchQuery("");
    }, 300);
  };

  const debouncedSearch = debounce(async (term: string) => {
    if (!term || term.length < 2) {
      setSearchResults((prev: any) => ({
        ...prev,
        aboutHeader: null,
        productList: null,
        products: [],
        awards: null,
        blogs: null,
        branch: null,
        careers: null,
        contact: null,
        corporateEthos: null,
        csr: null,
        digital: null,
        director: null,
        history: null,
        investors: null,
        leader: null,
        Media: null,
        news: null,
        policy: null,
        promoter: null,
        testimonials: null,
      }));
      return;
    }

    try {
      setSearchResults((prev: any) => ({
        ...prev,
        loading: true,
        error: null,
      }));
      const [
        aboutHeaderRes,
        productListRes,
        productsRes,
        awardsRes,
        blogsRes,
        branchRes,
        careersRes,
        contactRes,
        corporateEthosRes,
        csrRes,
        digitalRes,
        directorRes,
        historyRes,
        investorsRes,
        leaderRes,
        MediaRes,
        newsRes,
        policyRes,
        promoterRes,
        testimonialsRes,
      ] = await Promise.all([
        fetch(
          `${process.env.NEXT_PUBLIC_API_URL}about-us-page?populate=*&filters[SEO][SearchKeyword][$containsi]=${term}`
        ),
        fetch(
          `${process.env.NEXT_PUBLIC_API_URL}product-listing-page?populate=*&filters[SEO][SearchKeyword][$containsi]=${term}`
        ),
        fetch(
          `${process.env.NEXT_PUBLIC_API_URL}products?populate=Thumbnail&fields=Title&fields=Short_Description&fields=slug&fields=Breadcrumb_Name&filters[Title][$containsi]=${term}`
        ),
        fetch(
          `${process.env.NEXT_PUBLIC_API_URL}awards-and-recognition-page?populate=*&filters[SEO][SearchKeyword][$containsi]=${term}`
        ),
        fetch(
          `${process.env.NEXT_PUBLIC_API_URL}blog-page?populate=*&filters[SEO][SearchKeyword][$containsi]=${term}`
        ),
        fetch(
          `${process.env.NEXT_PUBLIC_API_URL}branch-page?populate=*&filters[SEO][SearchKeyword][$containsi]=${term}`
        ),
        fetch(
          `${process.env.NEXT_PUBLIC_API_URL}career-page?populate=*&filters[SEO][SearchKeyword][$containsi]=${term}`
        ),
        fetch(
          `${process.env.NEXT_PUBLIC_API_URL}contact-us-page?populate=*&filters[SEO][SearchKeyword][$containsi]=${term}`
        ),
        fetch(
          `${process.env.NEXT_PUBLIC_API_URL}corporate-ethos-page?populate=*&filters[SEO][SearchKeyword][$containsi]=${term}`
        ),
        fetch(
          `${process.env.NEXT_PUBLIC_API_URL}csr-page?populate=*&filters[SEO][SearchKeyword][$containsi]=${term}`
        ),
        fetch(
          `${process.env.NEXT_PUBLIC_API_URL}digital-initiative-page?populate=*&filters[SEO][SearchKeyword][$containsi]=${term}`
        ),
        fetch(
          `${process.env.NEXT_PUBLIC_API_URL}next-generation-page?populate=*&filters[SEO][SearchKeyword][$containsi]=${term}`
        ),
        fetch(
          `${process.env.NEXT_PUBLIC_API_URL}our-history-page?populate=*&filters[SEO][SearchKeyword][$containsi]=${term}`
        ),
        fetch(
          `${process.env.NEXT_PUBLIC_API_URL}inverstors-page?populate=*&filters[SEO][SearchKeyword][$containsi]=${term}`
        ),
        fetch(
          `${process.env.NEXT_PUBLIC_API_URL}Leadership-page?populate=*&filters[SEO][SearchKeyword][$containsi]=${term}`
        ),
        fetch(
          `${process.env.NEXT_PUBLIC_API_URL}media-page?populate=*&filters[SEO][SearchKeyword][$containsi]=${term}`
        ),
        fetch(
          `${process.env.NEXT_PUBLIC_API_URL}news-and-events-page?populate=*&filters[SEO][SearchKeyword][$containsi]=${term}`
        ),
        fetch(
          `${process.env.NEXT_PUBLIC_API_URL}policies-page?populate=*&filters[SEO][SearchKeyword][$containsi]=${term}`
        ),
        fetch(
          `${process.env.NEXT_PUBLIC_API_URL}our-director-page?populate=*&filters[SEO][SearchKeyword][$containsi]=${term}`
        ),
        fetch(
          `${process.env.NEXT_PUBLIC_API_URL}testimonial?populate=*&filters[SEO][SearchKeyword][$containsi]=${term}`
        ),
      ]);

      const [
        aboutHeader,
        productList,
        products,
        awards,
        blogs,
        branch,
        careers,
        contact,
        corporateEthos,
        csr,
        digital,
        director,
        history,
        investors,
        leadership,
        media,
        news,
        policy,
        promoter,
        testimonials,
      ] = await Promise.all([
        aboutHeaderRes.json(),
        productListRes.json(),
        productsRes.json(),
        awardsRes.json(),
        blogsRes.json(),
        branchRes.json(),
        careersRes.json(),
        contactRes.json(),
        corporateEthosRes.json(),
        csrRes.json(),
        digitalRes.json(),
        directorRes.json(),
        historyRes.json(),
        investorsRes.json(),
        leaderRes.json(),
        MediaRes.json(),
        newsRes.json(),
        policyRes.json(),
        promoterRes.json(),
        testimonialsRes.json(),
      ]);

      setSearchResults({
        aboutHeader: aboutHeader.data || null,
        productList: productList.data || null,
        products: products.data || [],
        awards: awards.data || null,
        blogs: blogs.data || null,
        branch: branch.data || null,
        careers: careers.data || null,
        contact: contact.data || null,
        corporateEthos: corporateEthos.data || null,
        director: director.data || null,
        csr: csr.data || null,
        digital: digital.data || null,
        investors: investors.data || null,
        leadership: leadership.data || null,
        history: history.data || null,
        media: media.data || null,
        news: news.data || null,
        policy: policy.data || null,
        promoter: promoter.data || null,
        testimonials: testimonials.data || null,
        loading: false,
        error: null,
      });
    } catch (error) {
      setSearchResults((prev: any) => ({
        ...prev,
        loading: false,
        error: "Failed to fetch search results",
      }));
    }
  }, 300);

  useEffect(() => {
    if (isMounted) {
      debouncedSearch(searchQuery);
    }

    return () => {
      debouncedSearch.cancel();
    };
  }, [searchQuery, isMounted]);

  const handleResultClick = () => {
    setIsSheetOpen(false);
    setSearchActive(false);
    setSearchQuery("");
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && searchQuery.trim()) {
      const hasResults =
        searchResults.service?.length > 0 ||
        searchResults.aboutHeader !== null ||
        searchResults.products?.length > 0 ||
        searchResults.productList !== null ||
        searchResults.awards !== null ||
        searchResults.blogs !== null ||
        searchResults.branch !== null ||
        searchResults.careers !== null ||
        searchResults.contact !== null ||
        searchResults.digital !== null ||
        searchResults.corporateEthos !== null ||
        searchResults.director !== null ||
        searchResults.history !== null ||
        searchResults.investors !== null ||
        searchResults.leadership !== null ||
        searchResults.media !== null ||
        searchResults.news !== null ||
        searchResults.policy !== null ||
        searchResults.promoter !== null ||
        searchResults.testimonials !== null ||
        searchResults.csr !== null;

      if (!hasResults) {
        setSearchResults((prev: any) => ({
          ...prev,
          showNoResults: true,
        }));
      } else {
        const firstResult = getFirstMatchingResult();
        if (firstResult) {
          setIsSheetOpen(false);
          setSearchActive(false);
          setSearchQuery("");
          router.push(firstResult.url);
        }
      }
    }
  };
  const getFirstMatchingResult = () => {
    // First check for products as they should have priority
    if (searchResults.products?.length > 0) {
      return { url: `/products/${searchResults.products[0].slug}` };
    }

    // Then check for product list
    if (searchResults.productList) {
      return { url: "/products" };
    }

    // Then check other results
    if (searchResults.aboutHeader) {
      return { url: "/about" };
    }
    if (searchResults.awards) {
      return { url: "/awards" };
    }
    if (searchResults.blogs) {
      return { url: "/blogs" };
    }
    if (searchResults.branch) {
      return { url: "/branch" };
    }
    if (searchResults.careers) {
      return { url: "/careers" };
    }
    if (searchResults.contact) {
      return { url: "/contact" };
    }
    if (searchResults.corporateEthos) {
      return { url: "/corporate-ethos" };
    }
    if (searchResults.csr) {
      return { url: "/csr" };
    }
    if (searchResults.digital) {
      return { url: "/digital-initiatives" };
    }
    if (searchResults.director) {
      return { url: "/directors" };
    }
    if (searchResults.history) {
      return { url: "/history" };
    }
    if (searchResults.investors) {
      return { url: "/investors" };
    }
    if (searchResults.leadership) {
      return { url: "/leaders" };
    }
    if (searchResults.media) {
      return { url: "/media" };
    }
    if (searchResults.news) {
      return { url: "/news" };
    }
    if (searchResults.policy) {
      return { url: "/policy" };
    }
    if (searchResults.promoter) {
      return { url: "/promoters" };
    }
    if (searchResults.testimonials) {
      return { url: "/testimonials" };
    }
    return null;
  };

  const renderResults = () => {
    if (searchResults.loading) {
      return (
        <div className="flex justify-center items-center p-8">
          <div className="relative w-16 h-16">
            <div className="absolute top-0 left-0 w-full h-full">
              <div className="w-16 h-16 rounded-full border-4 border-[rgba(0,149,218,0.1)]"></div>
              <div className="absolute top-0 left-0 w-16 h-16 rounded-full border-4 border-t-[#0095DA] animate-spin"></div>
            </div>
            <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center">
              <svg
                className="w-6 h-6 text-[#0095DA] animate-pulse"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
          </div>
        </div>
      );
    }
    if (searchResults.showNoResults) {
      return (
        <div className="flex flex-col items-center mt-8 justify-center text-center">
          <Image
            src="/noresultmuthoot.svg"
            alt="No results found"
            width={220}
            height={200}
            className="md:-mt-8 mt-[160px]"
          />
          <p className="md:text-[28px] text-[26px] font-bold text-[#0F0F0F] mb-8 md:-mt-6">
            No Results Found
          </p>
        </div>
      );
    }

    if (searchResults.error) {
      return <div className="p-4 text-red-500">{searchResults.error}</div>;
    }
    const hasResults =
      searchResults.service?.length > 0 ||
      searchResults.policies?.length > 0 ||
      searchResults.branch !== null ||
      searchResults.serviceHeader !== null ||
      searchResults.policyHeader !== null ||
      searchResults.awards !== null ||
      searchResults.blogs !== null ||
      searchResults.branch !== null ||
      searchResults.careers !== null ||
      searchResults.contact !== null ||
      searchResults.corporateEthos !== null ||
      searchResults.investmentCalculatorHeader !== null ||
      searchResults.newsEventsData !== null ||
      searchResults.digital !== null ||
      searchResults.director !== null ||
      searchResults.history !== null ||
      searchResults.investors !== null ||
      searchResults.media !== null ||
      searchResults.leadership !== null ||
      searchResults.news !== null ||
      searchResults.policy !== null ||
      searchResults.promoter !== null ||
      searchResults.testimonials !== null ||
      searchResults.csr !== null;

    if (!hasResults && searchQuery.length >= 2) {
      return <div className="p-4 text-gray-500">No results found</div>;
    }

    return (
      <div className="divide-y divide-gray-300">
        {searchResults?.products?.length > 0 && (
          <div className="p-4">
            {/* <h3 className="font-bold text-lg mb-2">
              <CommonTextBlue title={searchResults?.products?.[0]?.Title} />
            </h3>{" "} */}
            <ul className="space-y-2">
              {searchResults?.products?.map((products: any) => (
                <li
                  key={products.slug}
                  className="hover:bg-gray-200 border shadow-lg p-2 rounded"
                >
                  <Link
                    href={`/products/${products.slug}`}
                    className="flex items-center py-2"
                    onClick={(e) => {
                      handleResultClick();
                      setIsSheetOpen(false);
                    }}
                  >
                    <Image
                      src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${products?.Thumbnail.url}`}
                      alt={products.Thumbnail.name}
                      width={50}
                      height={50}
                      className="rounded"
                    />
                    <div className="ml-4">
                      <span className="text-xl font-semibold text-[#0095D9]">
                        {products.Title} <br />
                        <span className="text-sm font-light text-black">
                          {products.Short_Description}
                        </span>
                      </span>
                    </div>
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        )}
        {searchResults.productList && (
          <div className="p-4">
            <h3 className="font-bold text-lg mb-2">
              <CommonTextBlue title="Products & Services" />
            </h3>
            <div className="hover:bg-gray-100 border shadow-lg p-2 rounded">
              <Link
                href="/products"
                className="flex items-center py-2"
                onClick={(e) => {
                  handleResultClick();
                  setIsSheetOpen(false);
                }}
              >
                <div className="ml-4">
                  <span className="text-xl font-semibold text-[#0095D9]">
                    <CommonTextBlue
                      title={searchResults?.productList?.Banner?.Title}
                    />
                    <span className="text-sm font-light text-black">
                      {searchResults?.productList?.Banner?.Description}
                    </span>
                  </span>
                </div>
              </Link>
            </div>
          </div>
        )}
        {searchResults.aboutHeader && (
          <div className="p-4">
            <h3 className="font-bold text-lg mb-2">
              <CommonTextBlue title="About Us" />
            </h3>
            <div className="hover:bg-gray-100 border shadow-lg p-2 rounded">
              <Link
                href="/about"
                className="flex items-center py-2"
                onClick={(e) => {
                  handleResultClick();
                  setIsSheetOpen(false);
                }}
              >
                <div className="ml-4">
                  <span className="text-xl font-semibold text-[#0095D9]">
                    <CommonTextBlue
                      title={searchResults?.aboutHeader?.Header?.Title}
                    />
                    <span className="text-sm font-light text-black">
                      {searchResults?.aboutHeader?.Header?.Description}
                    </span>
                  </span>
                </div>
              </Link>
            </div>
          </div>
        )}
        {searchResults.awards && (
          <div className="p-4">
            <h3 className="font-bold text-lg mb-2">
              <CommonTextBlue title="Awards & Recognition" />
            </h3>
            <div className="hover:bg-gray-100 border shadow-lg p-2 rounded">
              <Link
                href="/awards"
                className="flex items-center py-2"
                onClick={(e) => {
                  handleResultClick();
                  setIsSheetOpen(false);
                }}
              >
                <div className="ml-4">
                  <span className="text-xl font-semibold text-[#0095D9]">
                    <CommonTextBlue
                      title={searchResults?.awards?.Header?.Title}
                    />
                    <span className="text-sm font-light text-black">
                      {searchResults?.awards?.Header?.Description}
                    </span>
                  </span>
                </div>
              </Link>
            </div>
          </div>
        )}
        {searchResults.blogs && (
          <div className="p-4">
            <h3 className="font-bold text-lg mb-2">
              <CommonTextBlue title="Blogs" />
            </h3>
            <div className="hover:bg-gray-100 border shadow-lg p-2 rounded">
              <Link
                href="/blogs"
                className="flex items-center py-2"
                onClick={(e) => {
                  handleResultClick();
                  setIsSheetOpen(false);
                }}
              >
                <div className="ml-4">
                  <span className="text-xl font-semibold text-[#0095D9]">
                    <CommonTextBlue
                      title={searchResults?.blogs?.Header?.Title}
                    />
                    <span className="text-sm font-light text-black">
                      {searchResults?.blogs?.Header?.Description}
                    </span>
                  </span>
                </div>
              </Link>
            </div>
          </div>
        )}
        {searchResults.branch && (
          <div className="p-4">
            <h3 className="font-bold text-lg mb-2">
              <CommonTextBlue title="Branches" />
            </h3>
            <div className="hover:bg-gray-100 border shadow-lg p-2 rounded">
              <Link
                href="/branch"
                className="flex items-center py-2"
                onClick={(e) => {
                  handleResultClick();
                  setIsSheetOpen(false);
                }}
              >
                <div className="ml-4">
                  <span className="text-xl font-semibold text-[#0095D9]">
                    <CommonTextBlue
                      title={searchResults?.branch?.Header?.Title}
                    />
                    <span className="text-sm font-light text-black">
                      {searchResults?.branch?.Header?.Description}
                    </span>
                  </span>
                </div>
              </Link>
            </div>
          </div>
        )}
        {searchResults.careers && (
          <div className="p-4">
            <h3 className="font-bold text-lg mb-2">
              <CommonTextBlue title="Careers" />
            </h3>
            <div className="hover:bg-gray-100 border shadow-lg p-2 rounded">
              <Link
                href="/careers"
                className="flex items-center py-2"
                onClick={(e) => {
                  handleResultClick();
                  setIsSheetOpen(false);
                }}
              >
                <div className="ml-4">
                  <span className="text-xl font-semibold text-[#0095D9]">
                    <CommonTextBlue
                      title={searchResults?.careers?.Header?.Title}
                    />
                    <span className="text-sm font-light text-black">
                      {searchResults?.careers?.Header?.Description}
                    </span>
                  </span>
                </div>
              </Link>
            </div>
          </div>
        )}
        {searchResults.contact && (
          <div className="p-4">
            <h3 className="font-bold text-lg mb-2">
              <CommonTextBlue title="Contact Us" />
            </h3>
            <div className="hover:bg-gray-100 border shadow-lg p-2 rounded">
              <Link
                href="/contact"
                className="flex items-center py-2"
                onClick={(e) => {
                  handleResultClick();
                  setIsSheetOpen(false);
                }}
              >
                <div className="ml-4">
                  <span className="text-xl font-semibold text-[#0095D9]">
                    <CommonTextBlue
                      title={searchResults?.contact?.Header?.Title}
                    />
                    <span className="text-sm font-light text-black">
                      {searchResults?.contact?.Header?.Description}
                    </span>
                  </span>
                </div>
              </Link>
            </div>
          </div>
        )}
        {searchResults.corporateEthos && (
          <div className="p-4">
            <h3 className="font-bold text-lg mb-2">
              <CommonTextBlue title="Corporate Ethos" />
            </h3>
            <div className="hover:bg-gray-100 border shadow-lg p-2 rounded">
              <Link
                href="/corporate-ethos"
                className="flex items-center py-2"
                onClick={(e) => {
                  handleResultClick();
                  setIsSheetOpen(false);
                }}
              >
                <div className="ml-4">
                  <span className="text-xl font-semibold text-[#0095D9]">
                    <CommonTextBlue
                      title={searchResults?.corporateEthos?.Banner?.Title}
                    />
                    <span className="text-sm font-light text-black">
                      {searchResults?.corporateEthos?.Banner?.Description}
                    </span>
                  </span>
                </div>
              </Link>
            </div>
          </div>
        )}
        {searchResults.csr && (
          <div className="p-4">
            <h3 className="font-bold text-lg mb-2">
              <CommonTextBlue title="Corporate Social Responsibility" />
            </h3>
            <div className="hover:bg-gray-100 border shadow-lg p-2 rounded">
              <Link
                href="/csr"
                className="flex items-center py-2"
                onClick={(e) => {
                  handleResultClick();
                  setIsSheetOpen(false);
                }}
              >
                <div className="ml-4">
                  <span className="text-xl font-semibold text-[#0095D9]">
                    <CommonTextBlue title={searchResults?.csr?.Header?.Title} />
                    <span className="text-sm font-light text-black">
                      {searchResults?.csr?.Header?.Description}
                    </span>
                  </span>
                </div>
              </Link>
            </div>
          </div>
        )}
        {searchResults.digital && (
          <div className="p-4">
            <h3 className="font-bold text-lg mb-2">
              <CommonTextBlue title="Digital Initiatives" />
            </h3>
            <div className="hover:bg-gray-100 border shadow-lg p-2 rounded">
              <Link
                href="/digital-initiatives"
                className="flex items-center py-2"
                onClick={(e) => {
                  handleResultClick();
                  setIsSheetOpen(false);
                }}
              >
                <div className="ml-4">
                  <span className="text-xl font-semibold text-[#0095D9]">
                    <CommonTextBlue
                      title={searchResults?.digital?.Header?.Title}
                    />
                    <span className="text-sm font-light text-black">
                      {searchResults?.digital?.Header?.Description}
                    </span>
                  </span>
                </div>
              </Link>
            </div>
          </div>
        )}
        {searchResults.director && (
          <div className="p-4">
            <h3 className="font-bold text-lg mb-2">
              <CommonTextBlue title="Directors" />
            </h3>
            <div className="hover:bg-gray-100 border shadow-lg p-2 rounded">
              <Link
                href="/directors"
                className="flex items-center py-2"
                onClick={(e) => {
                  handleResultClick();
                  setIsSheetOpen(false);
                }}
              >
                <div className="ml-4">
                  <span className="text-xl font-semibold text-[#0095D9]">
                    <CommonTextBlue
                      title={searchResults?.director?.Header?.Title}
                    />
                    <span className="text-sm font-light text-black">
                      {searchResults?.director?.Header?.Description}
                    </span>
                  </span>
                </div>
              </Link>
            </div>
          </div>
        )}
        {searchResults.history && (
          <div className="p-4">
            <h3 className="font-bold text-lg mb-2">
              <CommonTextBlue title="History" />
            </h3>
            <div className="hover:bg-gray-100 border shadow-lg p-2 rounded">
              <Link
                href="/history"
                className="flex items-center py-2"
                onClick={(e) => {
                  handleResultClick();
                  setIsSheetOpen(false);
                }}
              >
                <div className="ml-4">
                  <span className="text-xl font-semibold text-[#0095D9]">
                    <CommonTextBlue
                      title={searchResults?.history?.Header?.Title}
                    />
                    <span className="text-sm font-light text-black">
                      {searchResults?.history?.Header?.Description}
                    </span>
                  </span>
                </div>
              </Link>
            </div>
          </div>
        )}
        {searchResults.investors && (
          <div className="p-4">
            <h3 className="font-bold text-lg mb-2">
              <CommonTextBlue title="Investors" />
            </h3>
            <div className="hover:bg-gray-100 border shadow-lg p-2 rounded">
              <Link
                href="/investors"
                className="flex items-center py-2"
                onClick={(e) => {
                  handleResultClick();
                  setIsSheetOpen(false);
                }}
              >
                <div className="ml-4">
                  <span className="text-xl font-semibold text-[#0095D9]">
                    <CommonTextBlue
                      title={searchResults?.investors?.Header?.Title}
                    />
                    <span className="text-sm font-light text-black">
                      {searchResults?.investors?.Header?.Description}
                    </span>
                  </span>
                </div>
              </Link>
            </div>
          </div>
        )}
        {searchResults.leadership && (
          <div className="p-4">
            <h3 className="font-bold text-lg mb-2">
              <CommonTextBlue title="Leadership" />
            </h3>
            <div className="hover:bg-gray-100 border shadow-lg p-2 rounded">
              <Link
                href="/leaders"
                className="flex items-center py-2"
                onClick={(e) => {
                  handleResultClick();
                  setIsSheetOpen(false);
                }}
              >
                <div className="ml-4">
                  <span className="text-xl font-semibold text-[#0095D9]">
                    <CommonTextBlue
                      title={searchResults?.leadership?.Header?.Title}
                    />
                    <span className="text-sm font-light text-black">
                      {searchResults?.leadership?.Header?.Description}
                    </span>
                  </span>
                </div>
              </Link>
            </div>
          </div>
        )}
        {searchResults.media && (
          <div className="p-4">
            <h3 className="font-bold text-lg mb-2">
              <CommonTextBlue title="Media" />
            </h3>
            <div className="hover:bg-gray-100 border shadow-lg p-2 rounded">
              <Link
                href="/media"
                className="flex items-center py-2"
                onClick={(e) => {
                  handleResultClick();
                  setIsSheetOpen(false);
                }}
              >
                <div className="ml-4">
                  <span className="text-xl font-semibold text-[#0095D9]">
                    <CommonTextBlue
                      title={searchResults?.media?.Banner?.Title}
                    />
                    <span className="text-sm font-light text-black">
                      {searchResults?.media?.Banner?.Description}
                    </span>
                  </span>
                </div>
              </Link>
            </div>
          </div>
        )}
        {searchResults.news && (
          <div className="p-4">
            <h3 className="font-bold text-lg mb-2">
              <CommonTextBlue title="News & Events" />
            </h3>
            <div className="hover:bg-gray-100 border shadow-lg p-2 rounded">
              <Link
                href="/news"
                className="flex items-center py-2"
                onClick={(e) => {
                  handleResultClick();
                  setIsSheetOpen(false);
                }}
              >
                <div className="ml-4">
                  <span className="text-xl font-semibold text-[#0095D9]">
                    <CommonTextBlue
                      title={searchResults?.news?.Header?.Title}
                    />
                    <span className="text-sm font-light text-black">
                      {searchResults?.news?.Header?.Description}
                    </span>
                  </span>
                </div>
              </Link>
            </div>
          </div>
        )}
        {searchResults.policy && (
          <div className="p-4">
            <h3 className="font-bold text-lg mb-2">
              <CommonTextBlue title="Policies" />
            </h3>
            <div className="hover:bg-gray-100 border shadow-lg p-2 rounded">
              <Link
                href="/policy"
                className="flex items-center py-2"
                onClick={(e) => {
                  handleResultClick();
                  setIsSheetOpen(false);
                }}
              >
                <div className="ml-4">
                  <span className="text-xl font-semibold text-[#0095D9]">
                    <CommonTextBlue
                      title={searchResults?.policy?.Header?.Title}
                    />
                    <span className="text-sm font-light text-black">
                      {searchResults?.policy?.Header?.Description}
                    </span>
                  </span>
                </div>
              </Link>
            </div>
          </div>
        )}
        {searchResults.promoter && (
          <div className="p-4">
            <h3 className="font-bold text-lg mb-2">
              <CommonTextBlue title="Promoters" />
            </h3>
            <div className="hover:bg-gray-100 border shadow-lg p-2 rounded">
              <Link
                href="/promoters"
                className="flex items-center py-2"
                onClick={(e) => {
                  handleResultClick();
                  setIsSheetOpen(false);
                }}
              >
                <div className="ml-4">
                  <span className="text-xl font-semibold text-[#0095D9]">
                    <CommonTextBlue
                      title={searchResults?.promoter?.Banner?.Title}
                    />
                    <span className="text-sm font-light text-black">
                      {searchResults?.promoter?.Banner?.Description}
                    </span>
                  </span>
                </div>
              </Link>
            </div>
          </div>
        )}
        {searchResults.testimonials && (
          <div className="p-4">
            <h3 className="font-bold text-lg mb-2">
              <CommonTextBlue title="Testimonials" />
            </h3>
            <div className="hover:bg-gray-100 border shadow-lg p-2 rounded">
              <Link
                href="/testimonials"
                className="flex items-center py-2"
                onClick={(e) => {
                  handleResultClick();
                  setIsSheetOpen(false);
                }}
              >
                <div className="ml-4">
                  <span className="text-xl font-semibold text-[#0095D9]">
                    <CommonTextBlue
                      title={searchResults?.testimonials?.Header?.Title}
                    />
                    <span className="text-sm font-light text-black">
                      {searchResults?.testimonials?.Header?.Description}
                    </span>
                  </span>
                </div>
              </Link>
            </div>
          </div>
        )}
      </div>
    );
  };

  if (!isMounted) {
    return null;
  }

  return (
    <>
      {/* Desktop search button */}
      <div className="searchBtn_wrapper flex justify-end items-center gap-[20px] max-xl:hidden">
        <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
          {showSheetTrigger && (
            <SheetTrigger onClick={handleSearchOpen}>
              <div className="cursor-pointer searchBtn">
                <Image
                  src="/search-Bar.svg"
                  alt="searchicon"
                  width={20}
                  height={20}
                />
              </div>
            </SheetTrigger>
          )}

          <SheetContent side="top">
            {/* Desktop search content */}
            <SheetHeader>
              <SheetTitle>
                <div
                  className={`
                  container transition-all duration-300 ease-out
                  ${
                    isSheetOpen && !isSheetAnimating
                      ? "opacity-100 translate-y-0"
                      : "opacity-0 -translate-y-4"
                  }
                `}
                >
                  {" "}
                  <div className="search_wrapper flex justify-center items-center max-sm:flex-col max-sm:gap-[15px]">
                    <div className="logo pr-[30px] max-sm:pr-0 max-sm:max-w-[100px] max-sm:w-full">
                      <Image
                        src="/logo.svg"
                        alt="logo"
                        width={120}
                        height={67}
                        className="transition-all duration-300 hover:scale-105"
                      />
                    </div>

                    <div className="search_bar pl-[30px] w-full max-sm:pl-0 max-w-[750px] max-lg:mr-[50px] max-sm:mr-0">
                      <input
                        ref={desktopInputRef}
                        className={`
                          w-full py-[10px] px-[20px] text-[16px] font-normal 
                          border border-[#D3D3D3] rounded-[6px]
                          transition-all duration-300 ease-out
                          focus:border-[#0095DA] focus:ring-2 focus:ring-[#0095DA]/20 focus:outline-none
                          ${
                            isSheetOpen && !isSheetAnimating
                              ? "scale-100 opacity-100"
                              : "scale-95 opacity-70"
                          }
                        `}
                        placeholder="Search..."
                        type="text"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        onKeyDown={handleKeyDown}
                        autoFocus
                      />
                    </div>
                  </div>
                </div>
              </SheetTitle>
              <SheetDescription />
            </SheetHeader>

            {searchQuery.length >= 2 && (
              <div
                className={`
                bg-white searchdrop container1 mb-10 shadow-lg rounded-md 
                ring-1 ring-black ring-opacity-5 max-h-[500px] overflow-y-auto custom-scrollbar
                transition-all duration-300 ease-out delay-200
                ${
                  searchQuery.length >= 2 && isSheetOpen
                    ? "opacity-100 translate-y-0 scale-100"
                    : "opacity-0 translate-y-4 scale-95"
                }
              `}
              >
                {" "}
                {renderResults()}
              </div>
            )}
          </SheetContent>
        </Sheet>
      </div>

      {/* Mobile search button */}
      <div className="mobile_search hidden max-lg:block absolute right-[70px] top-1/2 transform -translate-y-1/2 z-10 max-sm:right-[60px]">
        <button
          className="cursor-pointer"
          onClick={() => setSearchActive(true)}
          aria-label="Search"
        >
          <Image
            src="/searchhhhhh.svg"
            alt="searchicon"
            width={23}
            height={23}
          />
        </button>
      </div>

      {/* Mobile search overlay */}
      {searchActive && (
        <div className="fixed inset-0 bg-white z-[9999] flex flex-col">
          <div className="sticky top-0 left-0 right-0 bg-[#008BD2] z-[10000]">
            <div className="h-14 px-4 flex items-center">
              <button
                onClick={() => {
                  setSearchActive(false);
                  setSearchQuery("");
                }}
                className="mr-3 text-white"
                aria-label="Back"
              >
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M15 19L8 12L15 5"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </button>

              <div className="flex-1 flex items-center bg-white rounded-md h-10">
                <input
                  className="flex-1 h-full px-3 text-sm border-none rounded-md focus:outline-none"
                  placeholder="Search..."
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={handleKeyDown}
                  autoFocus
                />
              </div>
            </div>
          </div>

          <div className="flex-1 overflow-y-auto custom-scrollbar p-4">
            {searchQuery.length >= 2 && renderResults()}
          </div>
        </div>
      )}
    </>
  );
};

export default SearchBar1;
