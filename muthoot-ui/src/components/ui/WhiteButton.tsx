import React from "react";
import Image from "next/image";

interface Props {
  name: any;
  classnames?: string;
  type?: "button" | "submit" | "reset";
  onClick?: () => void;
  disabled?: boolean;
}

export default function WhiteButton({
  name,
  classnames,
  type = "button",
  onClick,
  disabled,
}: Props) {
  return (
    <button
      disabled={disabled}
      type={type}
      onClick={onClick}
      className={`flex w-full text-center items-center justify-center gap-[10px] transition-all duration-300 rounded-[6px] hover:rounded-[5px] group ${
        classnames || "bg-[#ffffff] text-[#2193D1]"
      } py-[9px] px-[14px] text-white relative overflow-hidden`}
    >
      <span className="absolute inset-0 bg-white opacity-10 scale-0 rounded-full transition-transform duration-500 hover:scale-150"></span>
      <span className="relative flex text-[14px] font-semibold text-[#2193D1] items-center gap-[10px] z-10">
        {name}
      </span>
    </button>
  );
}
