import React from "react";
import Image from "next/image";

interface Props {
  name: any;
  classnames?: string;
  type?: "button" | "submit" | "reset";
  icon?: boolean;
  iconSrc?: string;
  onClick?: () => void;
  disabled?: boolean;
  isLoading?: boolean;
  className?: string;
}

export default function Button1({
  name,
  classnames,
  type = "button",
  icon = true,
  iconSrc = "/contact-arrow.svg",
  onClick,
  disabled,
  isLoading,
  className,
}: Props) {
  return (
    <button
      disabled={disabled}
      type={type}
      onClick={onClick}
      className={`w-full flex items-center justify-center gap-2 py-[19px] px-[68px] bg-[#008BD2] text-[16px] font-medium text-white rounded-[10px] 
      max-md:text-[14px] max-md:py-[14px] max-md:px-[40px] hover:bg-[#004a91] transition ease-in-out duration-300 
      ${disabled || isLoading ? "opacity-50 cursor-not-allowed" : ""} 
      ${className}`}
    >
      <span className="absolute inset-0 bg-white opacity-10 scale-0 rounded-full transition-transform duration-500 hover:scale-150"></span>
      <span className="relative flex items-center gap-[10px] z-10">
        {name}
        {icon && iconSrc && (
          <Image
            className="w-[12px] h-[13px] transition-transform duration-300 transform hover:translate-x-1 group-hover:translate-x-2"
            src={iconSrc}
            alt="Button Icon"
            width={12}
            height={13}
          />
        )}
      </span>
    </button>
  );
}
