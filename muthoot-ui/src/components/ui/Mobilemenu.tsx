"use client";
import React, { useEffect, useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { usePathname } from "next/navigation";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  She<PERSON><PERSON>eader,
  Sheet<PERSON><PERSON>le,
  SheetTrigger,
} from "./sheet";
import useFetch from "@/hooks/useFetch";
import endpoints from "@/endpoints";

export default function MobileNavigation({ datas }: { datas: any }) {
  const { data, error, loading } = useFetch<any>(
    ` ${process.env.NEXT_PUBLIC_API_URL}${endpoints.getFooter}`,
    { cache: false }
  );

  const [expandedItems, setExpandedItems] = useState<{
    [key: string]: boolean;
  }>({});
  const [open, setOpen] = useState(false);
  const pathname = usePathname();

  useEffect(() => {
    setOpen(false);
  }, [pathname]);

  const toggleExpand = (itemId: string, e: React.MouseEvent) => {
    e.preventDefault();
    setExpandedItems((prev) => ({
      ...prev,
      [itemId]: !prev[itemId],
    }));
  };

  return (
    <div className="mobile_togglewrapper">
      <Sheet open={open} onOpenChange={setOpen}>
        <SheetTrigger>
          <div className="toggle_menu">
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g clipPath="url(#clip0_734_7105)">
                <path
                  d="M21.1667 12.8161H2.83333C2.37333 12.8161 2 12.4427 2 11.9827C2 11.5227 2.37333 11.1494 2.83333 11.1494H21.1667C21.6267 11.1494 22 11.5227 22 11.9827C22 12.4427 21.6267 12.8161 21.1667 12.8161Z"
                  fill="black"
                />
                <path
                  d="M21.1667 6.42704H2.83333C2.37333 6.42704 2 6.05371 2 5.59371C2 5.13371 2.37333 4.76038 2.83333 4.76038H21.1667C21.6267 4.76038 22 5.13371 22 5.59371C22 6.05371 21.6267 6.42704 21.1667 6.42704Z"
                  fill="black"
                />
                <path
                  d="M21.1667 19.2048H2.83333C2.37333 19.2048 2 18.8314 2 18.3714C2 17.9114 2.37333 17.5381 2.83333 17.5381H21.1667C21.6267 17.5381 22 17.9114 22 18.3714C22 18.8314 21.6267 19.2048 21.1667 19.2048Z"
                  fill="black"
                />
              </g>
              <defs>
                <clipPath id="clip0_734_7105">
                  <rect
                    width="20"
                    height="20"
                    fill="white"
                    transform="translate(2 2)"
                  />
                </clipPath>
              </defs>
            </svg>
          </div>
        </SheetTrigger>
        <SheetContent className="mobile_menuwrapper p-0 h-full !border-none">
          <SheetHeader>
            <SheetTitle className="sticky top-0 bg-white p-[16px] pt-[20px] pb-[18px]">
              <div className="logo">
                <Link href="/">
                  <Image
                    src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${datas?.Logo?.Logo?.url}`}
                    alt="Logo"
                    width={1000}
                    height={1000}
                    className="object-contain w-[182px] h-[62px] object-left"
                  />
                </Link>
              </div>
            </SheetTitle>

            <SheetDescription className="max-h-[100vh] h-full overflow-y-auto custom-scrollbar px-[16px] pt-[24px] pb-[30px] mobile_sheet !mt-[0] relative">
              {/* <div className="mobile_particle max-w-[210px] w-full aspect-square absolute z-[95] top-[30%] left-[50%] translate-x-[-50%]">
                <Image
                  className="w-full h-full object-contain"
                  src="/mobile_partcile.png"
                  alt="particle"
                  width={350}
                  height={350}
                />
              </div> */}

              <div className="mobile_megamenu flex flex-col justify-between h-full ">
                <ul className="relative z-[98] max-h-[calc(100vh-185px)] overflow-y-auto overflow-x-hidden">
                  {datas?.Header &&
                    datas?.Header.sort(
                      (a: any, b: any) => a.Menu_Order - b.Menu_Order
                    ).map((item: any, index: any) => (
                      <li key={index} className="my-[20px]">
                        {item.Type === "SubMenuType2" ||
                        item.Type === "SubMenu" ? (
                          <>
                            <Link
                              className="flex items-center collection_menu text-[16px] font-medium text-white justify-between"
                              href={item.Link || ""}
                              onClick={(e) =>
                                toggleExpand(`header-${index}`, e)
                              }
                            >
                              {item.Label}
                              <svg
                                className={`ml-[2px] mt-[3px] w-[20px] transition-transform ${
                                  expandedItems[`header-${index}`]
                                    ? "rotate-180"
                                    : ""
                                }`}
                                width="24"
                                height="25"
                                viewBox="0 0 24 25"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <g clipPath="url(#clip0_1086_1769)">
                                  <path
                                    d="M7 10.925L12 15.925"
                                    stroke="white"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                  />
                                  <path
                                    d="M12 15.925L17 10.925"
                                    stroke="white"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                  />
                                </g>
                              </svg>
                            </Link>

                            {item.Type === "SubMenuType2" && (
                              <div
                                className={`main_category ${
                                  expandedItems[`header-${index}`]
                                    ? "block"
                                    : "hidden"
                                }`}
                              >
                                <ul className="pl-[16px]">
                                  {item.SubMenu_Type_2 &&
                                    item.SubMenu_Type_2.map(
                                      (submenu: any, subIdx: any) => (
                                        <li key={subIdx} className="my-[14px]">
                                          <div
                                            className="main_category flex items-center flex-wrap text-[16px] justify-between font-normal cursor-pointer text-white"
                                            onClick={(e) =>
                                              toggleExpand(
                                                `submenu-${index}-${subIdx}`,
                                                e
                                              )
                                            }
                                          >
                                            {submenu.Title}
                                            <svg
                                              className={`ml-[2px] mt-[3px] w-[20px] transition-transform ${
                                                expandedItems[
                                                  `submenu-${index}-${subIdx}`
                                                ]
                                                  ? "rotate-180"
                                                  : ""
                                              }`}
                                              width="24"
                                              height="25"
                                              viewBox="0 0 24 25"
                                              fill="none"
                                              xmlns="http://www.w3.org/2000/svg"
                                            >
                                              <g clipPath="url(#clip0_1086_1769)">
                                                <path
                                                  d="M7 10.925L12 15.925"
                                                  stroke="white"
                                                  strokeWidth="2"
                                                  strokeLinecap="round"
                                                  strokeLinejoin="round"
                                                />
                                                <path
                                                  d="M12 15.925L17 10.925"
                                                  stroke="white"
                                                  strokeWidth="2"
                                                  strokeLinecap="round"
                                                  strokeLinejoin="round"
                                                />
                                              </g>
                                            </svg>
                                          </div>
                                          <div
                                            className={`sub_category ${
                                              expandedItems[
                                                `submenu-${index}-${subIdx}`
                                              ]
                                                ? "block w-full"
                                                : "hidden w-full"
                                            }`}
                                          >
                                            <ul className="pl-[25px]">
                                              {submenu.SubMenu &&
                                                submenu.SubMenu.map(
                                                  (
                                                    subItem: any,
                                                    subItemIdx: any
                                                  ) => (
                                                    <li
                                                      key={subItemIdx}
                                                      className="my-[14px] text-[16px] font-normal text-white"
                                                    >
                                                      <Link
                                                        href={
                                                          subItem.Link || "#"
                                                        }
                                                        className="text-[16px] font-medium text-white"
                                                      >
                                                        {subItem.Title}
                                                      </Link>
                                                    </li>
                                                  )
                                                )}
                                            </ul>
                                          </div>
                                        </li>
                                      )
                                    )}
                                </ul>
                              </div>
                            )}

                            {item.Type === "SubMenu" && (
                              <div
                                className={`main_category ${
                                  expandedItems[`header-${index}`]
                                    ? "block"
                                    : "hidden"
                                }`}
                              >
                                <ul className="pl-[16px]">
                                  {item.Sub_Menu?.Submenu_Navigators?.map(
                                    (submenu: any, subIdx: any) => (
                                      <li key={subIdx} className="my-[14px]">
                                        <Link
                                          href={submenu.Link || "#"}
                                          className="text-[16px] font-medium text-white"
                                        >
                                          {submenu.Label}
                                        </Link>
                                      </li>
                                    )
                                  )}
                                </ul>
                              </div>
                            )}
                          </>
                        ) : (
                          <Link
                            className="text-[16px] font-medium text-white"
                            href={item.Link || ""}
                          >
                            {item.Label}
                          </Link>
                        )}
                      </li>
                    ))}
                </ul>

                <div className="social_mediawrapper pt-[25px]">
                  <div className="social_mediaWrapper flex justify-start items-center gap-[15px] mt-[41px] max-sm:mt-[20px] max-sm:justify-start">
                    {data?.socialMedia &&
                      data?.socialMedia?.length > 0 &&
                      data?.socialMedia?.map((media: any) => (
                        <Link
                          key={media?.id}
                          href={media?.Link}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="social_icon border border-[#ffffff85] p-[11px] flex justify-center items-center rounded-[50%] w-[35px] h-[35px] hover:bg-white group"
                        >
                          <div className="">
                            <Image
                              src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${media?.Icon?.url}`}
                              alt={
                                media?.Icon?.alternativeText ||
                                media?.Icon?.name
                              }
                              width={media?.Icon?.width || 35}
                              height={media?.Icon?.height || 35}
                              className=""
                            />
                          </div>
                        </Link>
                      ))}
                  </div>

                  <div className="privacy_wrapper mt-[20px]">
                    <div className="privacy_points flex justify-start items-center gap-[20px]">
                      <div className="item">
                        <Link
                          className="text-[14px] font-[400] text-[white]"
                          href="/privacy-policy"
                        >
                          Privacy Policy
                        </Link>
                      </div>
                      <div className="item">
                        <div className="dot w-[6px] h-[6px] bg-[white] rounded-full"></div>
                      </div>
                      <div className="item">
                        <Link
                          className="text-[14px] font-[400] text-[white]"
                          href="/terms-condition"
                        >
                          Terms of Service
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </SheetDescription>
          </SheetHeader>
        </SheetContent>
      </Sheet>
    </div>
  );
}
