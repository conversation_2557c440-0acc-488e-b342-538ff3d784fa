"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
type SearchBarProps = {
  showSheetTrigger?: boolean;
  handleSearchOpens: () => void;
};

const SearchBar: React.FC<SearchBarProps> = ({
  showSheetTrigger = true,
  handleSearchOpens,
}) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const router = useRouter();
  const handleKeyDown = (e: any) => {
    if (e.key === "Enter" && searchQuery.trim()) {
      router.push(`/collections?query=${encodeURIComponent(searchQuery)}`);
      setIsSearchOpen(false);
      setSearchQuery("");
    }
  };

  return (
    <div className="searchBtn_wrapper flex justify-start items-center gap-[20px]  ">
      <Sheet>
        <SheetTrigger onClick={() => setIsSearchOpen(true)}>
          <div
            className="
          cursor-pointer
          searchBtn     "
          >
            <Image
              src="/search-Bar.svg"
              alt="searchicon"
              width={20}
              height={20}
            />
          </div>
        </SheetTrigger>

        {isSearchOpen && (
          <SheetContent side="top">
            <SheetHeader>
              <SheetTitle>
                <div className="container">
                  <div className="search_wrapper flex justify-center items-center max-sm:flex-col max-sm:gap-[15px]">
                    <div className="logo pr-[30px] max-sm:pr-0 max-sm:max-w-[100px] max-sm:w-full">
                      <Image
                        src="/Logo.svg"
                        alt="logo"
                        width={120}
                        height={67}
                      />
                    </div>

                    <div className="search_bar pl-[30px] w-full max-sm:pl-0 max-w-[750px] max-lg:mr-[50px] max-sm:mr-0">
                      <input
                        className="w-full py-[10px] px-[20px] text-[16px]  font-normal border border-[#D3D3D3] rounded-[6px]"
                        placeholder="Search..."
                        type="text"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        onKeyDown={handleKeyDown}
                      />
                    </div>
                  </div>
                </div>
              </SheetTitle>
              <SheetDescription />
            </SheetHeader>
          </SheetContent>
        )}
      </Sheet>
    </div>
  );
};

export default SearchBar;
