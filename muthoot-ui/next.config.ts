import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  experimental: {
    optimizePackageImports: ["lucide-react"],
  },
  images: {
    domains: [
      "images.unsplash.com",
      "localhost",
      "lh3.googleusercontent.com",
      "muthootcap-admin.s424.previewbay.com",
      "localhost:1337undefined",
      "muthootcap-admin.s426.previewbay.com",
      "muthootcap-admin.s426.previewbay.comundefined",
      "uatweb.muthootcap.com:1337",
      "uatweb.muthootcap.com",
    ],
  },
};

const withBundleAnalyzer = require("@next/bundle-analyzer")({
  enabled: process.env.ANALYZE === "true",
});

export default withBundleAnalyzer(nextConfig);
