{"name": "muthoot-capital", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 6004", "build": "next build", "analyse": "ANALYZE=true npm run build", "start": "next start -p 6004", "lint": "next lint"}, "dependencies": {"@next/bundle-analyzer": "^15.1.6", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.2", "aos": "^2.3.4", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "country-state-city": "^3.2.1", "crypto-js": "^4.2.0", "framer-motion": "^11.18.1", "input-otp": "^1.4.2", "lodash": "^4.17.21", "lucide-react": "^0.469.0", "next": "^15.1.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.5.1", "react-phone-input-2": "^2.15.1", "sass": "^1.83.1", "swiper": "^11.2.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/aos": "^3.0.7", "@types/crypto-js": "^4.2.2", "@types/locomotive-scroll": "^4.1.4", "@types/lodash": "^4.17.16", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.4", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}