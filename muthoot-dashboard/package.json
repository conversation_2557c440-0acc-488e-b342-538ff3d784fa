{"name": "muthoot-dashboard", "version": "0.1.0", "private": true, "description": "A Strapi application", "scripts": {"build": "strapi build", "deploy": "strapi deploy", "develop": "strapi develop", "start": "strapi start", "strapi": "strapi"}, "dependencies": {"@_sh/strapi-plugin-ckeditor": "^5.0.1", "@offset-dev/strapi-calendar": "^1.0.0", "@strapi/plugin-cloud": "5.7.0", "@strapi/plugin-seo": "^2.0.4", "@strapi/plugin-users-permissions": "5.7.0", "@strapi/strapi": "5.7.0", "mysql": "^2.18.1", "mysql2": "3.9.8", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "^6.0.0", "sharp": "^0.34.2", "strapi-plugin-country-select": "^2.0.0", "strapi-plugin-multi-select": "^2.1.1", "strapi-plugin-tagsinput": "^2.0.2", "styled-components": "^6.0.0", "tunnel-ssh": "^5.2.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "typescript": "^5"}, "engines": {"node": ">=18.0.0 <=22.x.x", "npm": ">=6.0.0"}, "strapi": {"uuid": "************************************"}}