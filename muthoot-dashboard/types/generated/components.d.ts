import type { Schema, Struct } from '@strapi/strapi';

export interface AboutUsChairmanCard extends Struct.ComponentSchema {
  collectionName: 'components_about_us_chairman_cards';
  info: {
    displayName: 'Chairman-Card';
  };
  attributes: {
    About: Schema.Attribute.Text & Schema.Attribute.Required;
    Designation: Schema.Attribute.String & Schema.Attribute.Required;
    Image: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    Name: Schema.Attribute.String & Schema.Attribute.Required;
  };
}

export interface AboutUsCorporateEthosCard extends Struct.ComponentSchema {
  collectionName: 'components_about_us_corporate_ethos_cards';
  info: {
    description: '';
    displayName: 'Corporate Ethos-Card';
  };
  attributes: {
    Description: Schema.Attribute.Text;
    Description2: Schema.Attribute.String;
    Image: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    SRK_Image: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    Title: Schema.Attribute.String;
    Title2: Schema.Attribute.String;
    Values: Schema.Attribute.Component<'common.section-td', true>;
  };
}

export interface AboutUsDirectorCard extends Struct.ComponentSchema {
  collectionName: 'components_about_us_director_cards';
  info: {
    description: '';
    displayName: 'Director-Card';
  };
  attributes: {
    About: Schema.Attribute.Component<'about.about', true>;
    Designation: Schema.Attribute.String;
    Image: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    Name: Schema.Attribute.String;
  };
}

export interface AboutUsGenerationPropleCard extends Struct.ComponentSchema {
  collectionName: 'components_about_us_generation_prople_cards';
  info: {
    description: '';
    displayName: 'Generation-Prople-Card';
  };
  attributes: {
    Description_Rich: Schema.Attribute.Blocks;
    Designation: Schema.Attribute.String;
    Image: Schema.Attribute.Media<'images'>;
    Name: Schema.Attribute.String;
  };
}

export interface AboutUsMilstoneCard extends Struct.ComponentSchema {
  collectionName: 'components_about_us_milstone_cards';
  info: {
    displayName: 'Milstone-Card';
  };
  attributes: {
    About: Schema.Attribute.Text;
    Year: Schema.Attribute.Integer;
  };
}

export interface AboutUsOperatingPhilosophyCard extends Struct.ComponentSchema {
  collectionName: 'components_about_us_operating_philosophy_cards';
  info: {
    description: '';
    displayName: 'Operating-Philosophy-Card';
  };
  attributes: {
    Description: Schema.Attribute.Text & Schema.Attribute.Required;
    FrameWorks: Schema.Attribute.Component<'common.header-td-colored', true> &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMax<
        {
          max: 4;
        },
        number
      >;
    SRK_Image: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    Title: Schema.Attribute.String & Schema.Attribute.Required;
  };
}

export interface AboutUsPillarsCard extends Struct.ComponentSchema {
  collectionName: 'components_about_us_pillars_cards';
  info: {
    description: '';
    displayName: 'Pillars-Card';
  };
  attributes: {
    Description: Schema.Attribute.Text;
    Folks: Schema.Attribute.Component<'common.memorial-card', true> &
      Schema.Attribute.SetMinMax<
        {
          max: 2;
        },
        number
      >;
    Title: Schema.Attribute.String;
  };
}

export interface AboutUsPrinciplesCard extends Struct.ComponentSchema {
  collectionName: 'components_about_us_principles_cards';
  info: {
    displayName: 'Principles-Card';
  };
  attributes: {
    Description: Schema.Attribute.Text;
    Principle_Cards: Schema.Attribute.Component<'common.section-its', true>;
    SRK_Image: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    Title: Schema.Attribute.String;
  };
}

export interface AboutAbout extends Struct.ComponentSchema {
  collectionName: 'components_about_abouts';
  info: {
    displayName: 'About';
  };
  attributes: {
    About: Schema.Attribute.Text;
  };
}

export interface AboutMilestoneCard extends Struct.ComponentSchema {
  collectionName: 'components_about_milestone_cards';
  info: {
    displayName: 'Milestone-Card';
    icon: 'book';
  };
  attributes: {
    Milestones: Schema.Attribute.Component<'about-us.milstone-card', true>;
    Title: Schema.Attribute.String;
  };
}

export interface CareerCareerCard extends Struct.ComponentSchema {
  collectionName: 'components_career_career_cards';
  info: {
    description: '';
    displayName: 'Career-Card';
  };
  attributes: {
    Designation: Schema.Attribute.String;
    Name: Schema.Attribute.String;
    Quote: Schema.Attribute.Text & Schema.Attribute.Required;
    User_Image: Schema.Attribute.Media<'images'>;
  };
}

export interface CareerCareerHeaderCard extends Struct.ComponentSchema {
  collectionName: 'components_career_career_header_cards';
  info: {
    displayName: 'Career-Header-Card';
  };
  attributes: {
    Button: Schema.Attribute.Component<'components.button', false>;
    Description: Schema.Attribute.Text & Schema.Attribute.Required;
    SRK_Image: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    Sub_Title: Schema.Attribute.String & Schema.Attribute.Required;
    Title: Schema.Attribute.String & Schema.Attribute.Required;
  };
}

export interface CareerChairmanCard extends Struct.ComponentSchema {
  collectionName: 'components_career_chairman_cards';
  info: {
    displayName: 'Chairman-Card';
  };
  attributes: {
    About: Schema.Attribute.Text & Schema.Attribute.Required;
    Designation: Schema.Attribute.String & Schema.Attribute.Required;
    Name: Schema.Attribute.String & Schema.Attribute.Required;
    Video: Schema.Attribute.Media<'videos'> & Schema.Attribute.Required;
  };
}

export interface CareerHeaderCard extends Struct.ComponentSchema {
  collectionName: 'components_career_header_cards';
  info: {
    displayName: 'Header-Card';
  };
  attributes: {
    Button: Schema.Attribute.Component<'components.button', false>;
    Description: Schema.Attribute.Text;
    SRK_Image: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    SubTitle: Schema.Attribute.String & Schema.Attribute.Required;
    Title: Schema.Attribute.String & Schema.Attribute.Required;
  };
}

export interface CareerOurMuthootees extends Struct.ComponentSchema {
  collectionName: 'components_career_our_muthootees';
  info: {
    description: '';
    displayName: 'Our_Muthootees';
  };
  attributes: {
    Header: Schema.Attribute.Component<'common.section-td', false>;
    Image1: Schema.Attribute.Media<'images' | 'videos'> &
      Schema.Attribute.Required;
    Image2: Schema.Attribute.Media<'images' | 'videos'> &
      Schema.Attribute.Required;
    Image3: Schema.Attribute.Media<'images' | 'videos'> &
      Schema.Attribute.Required;
    Image4: Schema.Attribute.Media<'images' | 'videos'> &
      Schema.Attribute.Required;
    Image5: Schema.Attribute.Media<'images' | 'videos'> &
      Schema.Attribute.Required;
  };
}

export interface CommonAwardCard extends Struct.ComponentSchema {
  collectionName: 'components_common_award_cards';
  info: {
    description: '';
    displayName: 'Award-Card';
  };
  attributes: {
    Description: Schema.Attribute.Text;
    Image: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    Title: Schema.Attribute.String;
  };
}

export interface CommonBannerBtd extends Struct.ComponentSchema {
  collectionName: 'components_common_banner_btds';
  info: {
    displayName: 'Banner-BTD';
  };
  attributes: {
    Banner: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    Description: Schema.Attribute.Text & Schema.Attribute.Required;
    Title: Schema.Attribute.String & Schema.Attribute.Required;
  };
}

export interface CommonBannerItd extends Struct.ComponentSchema {
  collectionName: 'components_common_banner_itds';
  info: {
    description: '';
    displayName: 'Banner-BTDS';
    icon: 'alien';
  };
  attributes: {
    Banner: Schema.Attribute.Media<'images'>;
    Description: Schema.Attribute.Text;
    SRK_Image: Schema.Attribute.Media<'images'>;
    Title: Schema.Attribute.String;
  };
}

export interface CommonBannerTds extends Struct.ComponentSchema {
  collectionName: 'components_common_banner_tds';
  info: {
    displayName: 'Banner-TDS';
  };
  attributes: {
    Description: Schema.Attribute.Text & Schema.Attribute.Required;
    SRK_Image: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    Title: Schema.Attribute.String & Schema.Attribute.Required;
  };
}

export interface CommonCardAbout extends Struct.ComponentSchema {
  collectionName: 'components_common_card_abouts';
  info: {
    displayName: 'Card-About';
  };
  attributes: {
    About: Schema.Attribute.Text;
  };
}

export interface CommonCardIt extends Struct.ComponentSchema {
  collectionName: 'components_common_card_its';
  info: {
    displayName: 'Card-IT';
    icon: 'arrowUp';
  };
  attributes: {
    Image: Schema.Attribute.Media<'images'>;
    Title: Schema.Attribute.String;
  };
}

export interface CommonCardItd extends Struct.ComponentSchema {
  collectionName: 'components_common_card_itds';
  info: {
    displayName: 'Card-ITD';
    icon: 'collapse';
  };
  attributes: {
    Description: Schema.Attribute.Text & Schema.Attribute.Required;
    Image: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    Title: Schema.Attribute.String & Schema.Attribute.Required;
  };
}

export interface CommonCardItl extends Struct.ComponentSchema {
  collectionName: 'components_common_card_itls';
  info: {
    description: '';
    displayName: 'Card-ITV';
    icon: 'arrowUp';
  };
  attributes: {
    Icon: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    Title: Schema.Attribute.String & Schema.Attribute.Required;
    Value: Schema.Attribute.Text & Schema.Attribute.Required;
  };
}

export interface CommonCardT extends Struct.ComponentSchema {
  collectionName: 'components_common_card_ts';
  info: {
    description: '';
    displayName: 'Card-T';
    icon: 'italic';
  };
  attributes: {
    Image: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    Title: Schema.Attribute.String;
  };
}

export interface CommonCardTd extends Struct.ComponentSchema {
  collectionName: 'components_common_card_tds';
  info: {
    description: '';
    displayName: 'Card-TD';
  };
  attributes: {
    Description: Schema.Attribute.Text;
    Title: Schema.Attribute.String;
  };
}

export interface CommonCardTdf extends Struct.ComponentSchema {
  collectionName: 'components_common_card_tdfs';
  info: {
    displayName: 'Card-TDF';
  };
  attributes: {
    Description: Schema.Attribute.Text;
    File: Schema.Attribute.Media<'files'> & Schema.Attribute.Required;
    Title: Schema.Attribute.String;
  };
}

export interface CommonCsrCard extends Struct.ComponentSchema {
  collectionName: 'components_common_csr_cards';
  info: {
    description: '';
    displayName: 'CSR-Card';
  };
  attributes: {
    Date: Schema.Attribute.Date;
    Description: Schema.Attribute.Text & Schema.Attribute.Required;
    File: Schema.Attribute.Media<'files'> & Schema.Attribute.Required;
  };
}

export interface CommonDownloadCard extends Struct.ComponentSchema {
  collectionName: 'components_common_download_cards';
  info: {
    description: '';
    displayName: 'Download-Card';
  };
  attributes: {
    Description: Schema.Attribute.Text;
    File: Schema.Attribute.Media<'files'> & Schema.Attribute.Required;
    investors_tabs: Schema.Attribute.Relation<
      'oneToMany',
      'api::investors-tab.investors-tab'
    >;
    Title: Schema.Attribute.String;
  };
}

export interface CommonHeader extends Struct.ComponentSchema {
  collectionName: 'components_common_headers';
  info: {
    description: '';
    displayName: 'Header';
  };
  attributes: {
    Label: Schema.Attribute.String;
    Link: Schema.Attribute.String & Schema.Attribute.DefaultTo<'/'>;
    Menu_Order: Schema.Attribute.Integer;
    Sub_Menu: Schema.Attribute.Component<'common.sub-menu', false>;
    SubMenu_Type_2: Schema.Attribute.Component<'common.sub-menu-type-2', true>;
    SubMenu_Type_2_Srk_Image: Schema.Attribute.Media<'images'>;
    Type: Schema.Attribute.Enumeration<['SubMenu', 'SubMenuType2']>;
  };
}

export interface CommonHeaderTd extends Struct.ComponentSchema {
  collectionName: 'components_common_header_tds';
  info: {
    displayName: 'Header-TD';
  };
  attributes: {
    Description: Schema.Attribute.Text;
    Title: Schema.Attribute.String;
  };
}

export interface CommonHeaderTdColored extends Struct.ComponentSchema {
  collectionName: 'components_common_header_td_colored_s';
  info: {
    description: '';
    displayName: 'Header-TD(colored)';
  };
  attributes: {
    Description: Schema.Attribute.Text & Schema.Attribute.Required;
    Title: Schema.Attribute.String & Schema.Attribute.Required;
  };
}

export interface CommonHeaderTop extends Struct.ComponentSchema {
  collectionName: 'components_common_header_tops';
  info: {
    displayName: 'HeaderTop';
  };
  attributes: {
    Icon: Schema.Attribute.Media<'images'>;
    Phone: Schema.Attribute.String;
    Text: Schema.Attribute.String;
  };
}

export interface CommonHeader2 extends Struct.ComponentSchema {
  collectionName: 'components_common_header2s';
  info: {
    displayName: 'Header2';
  };
  attributes: {
    Link: Schema.Attribute.String;
    Title: Schema.Attribute.String;
  };
}

export interface CommonItl extends Struct.ComponentSchema {
  collectionName: 'components_common_itls';
  info: {
    description: '';
    displayName: 'ITL';
  };
  attributes: {
    Icon: Schema.Attribute.Media<'images'>;
    Link: Schema.Attribute.String & Schema.Attribute.DefaultTo<'/'>;
    Title: Schema.Attribute.String;
  };
}

export interface CommonLandScapeImages extends Struct.ComponentSchema {
  collectionName: 'components_common_land_scape_images';
  info: {
    displayName: 'LandScapeImages';
  };
  attributes: {
    Image: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    Title: Schema.Attribute.String;
  };
}

export interface CommonLinkTl extends Struct.ComponentSchema {
  collectionName: 'components_common_link_tls';
  info: {
    description: '';
    displayName: 'Link-LL';
    icon: 'hashtag';
  };
  attributes: {
    Label: Schema.Attribute.String;
    Link: Schema.Attribute.String;
  };
}

export interface CommonMainHeader extends Struct.ComponentSchema {
  collectionName: 'components_common_main_headers';
  info: {
    displayName: 'Main_Header';
  };
  attributes: {
    Header1: Schema.Attribute.Component<'header.header1', true>;
  };
}

export interface CommonMemorialCard extends Struct.ComponentSchema {
  collectionName: 'components_common_memorial_cards';
  info: {
    description: '';
    displayName: 'Memorial-Card';
  };
  attributes: {
    About: Schema.Attribute.Text & Schema.Attribute.Required;
    Era: Schema.Attribute.String & Schema.Attribute.Required;
    Image: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    Name: Schema.Attribute.String & Schema.Attribute.Required;
  };
}

export interface CommonSectionIts extends Struct.ComponentSchema {
  collectionName: 'components_common_section_its';
  info: {
    description: '';
    displayName: 'Section-ITD';
    icon: 'alien';
  };
  attributes: {
    Description: Schema.Attribute.Text & Schema.Attribute.Required;
    Image: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    Title: Schema.Attribute.String;
  };
}

export interface CommonSectionTd extends Struct.ComponentSchema {
  collectionName: 'components_common_section_tds';
  info: {
    displayName: 'Section-TD';
  };
  attributes: {
    Description: Schema.Attribute.Text;
    Title: Schema.Attribute.String & Schema.Attribute.Required;
  };
}

export interface CommonSectionTv extends Struct.ComponentSchema {
  collectionName: 'components_common_section_tvs';
  info: {
    displayName: 'Section-TV';
    icon: 'check';
  };
  attributes: {
    Text: Schema.Attribute.String;
    Value: Schema.Attribute.String;
  };
}

export interface CommonSeo extends Struct.ComponentSchema {
  collectionName: 'components_common_seos';
  info: {
    description: '';
    displayName: 'SEO';
  };
  attributes: {
    Description: Schema.Attribute.Text;
    Keywords: Schema.Attribute.Text;
    SearchKeyword: Schema.Attribute.Text;
    Title: Schema.Attribute.String;
  };
}

export interface CommonSubMenu extends Struct.ComponentSchema {
  collectionName: 'components_common_sub_menus';
  info: {
    displayName: 'Sub_Menu';
  };
  attributes: {
    SRK_Image: Schema.Attribute.Media<'images'>;
    Submenu_Navigators: Schema.Attribute.Component<
      'common.submenu-navigators',
      true
    >;
    SubMenu_Title: Schema.Attribute.String;
  };
}

export interface CommonSubMenuType2 extends Struct.ComponentSchema {
  collectionName: 'components_common_sub_menu_type_2s';
  info: {
    description: '';
    displayName: 'Sub_Menu_Type_2';
  };
  attributes: {
    Description: Schema.Attribute.Text;
    SubMenu: Schema.Attribute.Component<'common.itl', true>;
    Title: Schema.Attribute.String;
  };
}

export interface CommonSubmenuNavigators extends Struct.ComponentSchema {
  collectionName: 'components_common_submenu_navigators';
  info: {
    description: '';
    displayName: 'Submenu_Navigators';
  };
  attributes: {
    Description: Schema.Attribute.Text;
    Icon: Schema.Attribute.Media<'images'>;
    Label: Schema.Attribute.String;
    Link: Schema.Attribute.String & Schema.Attribute.DefaultTo<'/'>;
  };
}

export interface ComponentsAwardCard extends Struct.ComponentSchema {
  collectionName: 'components_components_award_cards';
  info: {
    description: '';
    displayName: 'Award-Card';
    icon: 'archive';
  };
  attributes: {
    Description: Schema.Attribute.String & Schema.Attribute.Required;
    Thumbnail: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    Title: Schema.Attribute.String & Schema.Attribute.Required;
  };
}

export interface ComponentsButton extends Struct.ComponentSchema {
  collectionName: 'components_components_buttons';
  info: {
    displayName: 'Button';
  };
  attributes: {
    Label: Schema.Attribute.String;
    Link: Schema.Attribute.String;
  };
}

export interface ComponentsCardTrichd extends Struct.ComponentSchema {
  collectionName: 'components_components_card_trichds';
  info: {
    description: '';
    displayName: 'Card-TRICHD';
  };
  attributes: {
    Description: Schema.Attribute.Blocks;
    Title: Schema.Attribute.String;
  };
}

export interface ComponentsCompaniesBox extends Struct.ComponentSchema {
  collectionName: 'components_components_companies_boxes';
  info: {
    description: '';
    displayName: 'CompaniesBox';
  };
  attributes: {
    Company_Name: Schema.Attribute.String;
    Company_Order: Schema.Attribute.Integer;
    Icon: Schema.Attribute.Media<'images'>;
    RedirectionLink: Schema.Attribute.String;
  };
}

export interface ComponentsDownloadButton extends Struct.ComponentSchema {
  collectionName: 'components_components_download_buttons';
  info: {
    description: '';
    displayName: 'Download-Button';
  };
  attributes: {
    AppStoreLink: Schema.Attribute.String & Schema.Attribute.DefaultTo<'/'>;
    Label: Schema.Attribute.String & Schema.Attribute.Required;
    PlayStoreLink: Schema.Attribute.String & Schema.Attribute.DefaultTo<'/'>;
    QR_Image: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
  };
}

export interface ComponentsFaq extends Struct.ComponentSchema {
  collectionName: 'components_components_faqs';
  info: {
    description: '';
    displayName: 'FAQ';
  };
  attributes: {
    Answer: Schema.Attribute.Text;
    Question: Schema.Attribute.Text;
  };
}

export interface ComponentsMediaListCard extends Struct.ComponentSchema {
  collectionName: 'components_components_media_list_cards';
  info: {
    displayName: 'Media-list-card';
  };
  attributes: {
    Button: Schema.Attribute.Component<'components.button', false>;
    Header: Schema.Attribute.Component<'common.header-td', false>;
  };
}

export interface ComponentsNavbar extends Struct.ComponentSchema {
  collectionName: 'components_components_navbars';
  info: {
    description: '';
    displayName: 'Navbar';
  };
  attributes: {
    Label: Schema.Attribute.String & Schema.Attribute.Required;
    Link: Schema.Attribute.String & Schema.Attribute.Required;
    SubMenu: Schema.Attribute.Component<'sub-menu.submenu', false>;
  };
}

export interface ComponentsSocialMedia extends Struct.ComponentSchema {
  collectionName: 'components_components_social_medias';
  info: {
    displayName: 'Social Media';
    icon: 'cursor';
  };
  attributes: {
    Icon: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    Link: Schema.Attribute.String;
  };
}

export interface ContactConnectCard extends Struct.ComponentSchema {
  collectionName: 'components_contact_connect_cards';
  info: {
    description: '';
    displayName: 'Connect-Card';
  };
  attributes: {
    Button: Schema.Attribute.Component<'contact.connect-card-button', true> &
      Schema.Attribute.SetMinMax<
        {
          max: 2;
        },
        number
      >;
    Description: Schema.Attribute.Text;
    SRK_Image: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    Title: Schema.Attribute.String & Schema.Attribute.Required;
  };
}

export interface ContactConnectCardButton extends Struct.ComponentSchema {
  collectionName: 'components_contact_connect_card_buttons';
  info: {
    displayName: 'Connect-Card-Button';
  };
  attributes: {
    Image: Schema.Attribute.Media<'images'>;
    Label: Schema.Attribute.String;
    Link: Schema.Attribute.String;
  };
}

export interface ContactContactAddressCard extends Struct.ComponentSchema {
  collectionName: 'components_contact_contact_address_cards';
  info: {
    description: '';
    displayName: 'Contact Address Card';
  };
  attributes: {
    Icon: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    Title: Schema.Attribute.String & Schema.Attribute.Required;
    Value1: Schema.Attribute.Text & Schema.Attribute.Required;
    value2: Schema.Attribute.Text;
  };
}

export interface FooterAddressCard extends Struct.ComponentSchema {
  collectionName: 'components_footer_address_cards';
  info: {
    description: '';
    displayName: 'Address-Card';
    icon: 'check';
  };
  attributes: {
    Address: Schema.Attribute.Text;
    LegalNumbers: Schema.Attribute.Component<'common.section-tv', true> &
      Schema.Attribute.SetMinMax<
        {
          max: 2;
        },
        number
      >;
  };
}

export interface FooterContactCard extends Struct.ComponentSchema {
  collectionName: 'components_footer_contact_cards';
  info: {
    description: '';
    displayName: 'Contact-card';
    icon: 'arrowLeft';
  };
  attributes: {
    Mail: Schema.Attribute.String;
    Phone1: Schema.Attribute.BigInteger;
    Phone2: Schema.Attribute.BigInteger;
    Title: Schema.Attribute.String;
    TollFreeNumber: Schema.Attribute.BigInteger;
  };
}

export interface FooterLinkCard extends Struct.ComponentSchema {
  collectionName: 'components_footer_link_cards';
  info: {
    description: '';
    displayName: 'Link-Card';
    icon: 'link';
  };
  attributes: {
    Links: Schema.Attribute.Component<'common.link-tl', true>;
    Title: Schema.Attribute.String;
  };
}

export interface FooterStockLogos extends Struct.ComponentSchema {
  collectionName: 'components_footer_stock_logos';
  info: {
    displayName: 'Stock-Logos';
    icon: 'picture';
  };
  attributes: {
    Logo: Schema.Attribute.Media<
      'images' | 'files' | 'videos' | 'audios',
      true
    >;
  };
}

export interface HeaderHeader1 extends Struct.ComponentSchema {
  collectionName: 'components_header_header1s';
  info: {
    displayName: 'Header1';
  };
  attributes: {
    Label: Schema.Attribute.String;
    Link: Schema.Attribute.String;
  };
}

export interface HeaderLogo extends Struct.ComponentSchema {
  collectionName: 'components_header_logos';
  info: {
    description: '';
    displayName: 'Logo';
  };
  attributes: {
    Link: Schema.Attribute.String & Schema.Attribute.DefaultTo<'/'>;
    Logo: Schema.Attribute.Media<'images'>;
  };
}

export interface HomePageAboutUsSection extends Struct.ComponentSchema {
  collectionName: 'components_home_page_about_us_sections';
  info: {
    description: '';
    displayName: 'About-Us-Section';
  };
  attributes: {
    Button: Schema.Attribute.Component<'components.button', false>;
    Counts_Bottom: Schema.Attribute.Component<'home-page.counts', false>;
    Counts_Top: Schema.Attribute.Component<'home-page.counts', false>;
    Description: Schema.Attribute.Text;
    Logo: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    SRK_Image: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    Title: Schema.Attribute.String & Schema.Attribute.Required;
  };
}

export interface HomePageBlogSection extends Struct.ComponentSchema {
  collectionName: 'components_home_page_blog_sections';
  info: {
    displayName: 'Blog-Section';
  };
  attributes: {
    Button: Schema.Attribute.Component<'components.button', false>;
    Description: Schema.Attribute.Text;
    Title: Schema.Attribute.String;
  };
}

export interface HomePageBlogsSection extends Struct.ComponentSchema {
  collectionName: 'components_home_page_blogs_sections';
  info: {
    displayName: 'Blogs_Section';
  };
  attributes: {
    Button: Schema.Attribute.Component<'components.button', false>;
    Header: Schema.Attribute.Component<'common.header-td', false>;
  };
}

export interface HomePageBranchSection extends Struct.ComponentSchema {
  collectionName: 'components_home_page_branch_sections';
  info: {
    displayName: 'Branch-Section';
  };
  attributes: {
    Button: Schema.Attribute.Component<'components.button', false>;
    Description: Schema.Attribute.Text;
    Title: Schema.Attribute.String;
  };
}

export interface HomePageBranchesSection extends Struct.ComponentSchema {
  collectionName: 'components_home_page_branches_sections';
  info: {
    displayName: 'Branches_Section';
  };
  attributes: {
    Button: Schema.Attribute.Component<'components.button', false>;
    Title: Schema.Attribute.String;
  };
}

export interface HomePageCalculatorSection extends Struct.ComponentSchema {
  collectionName: 'components_home_page_calculator_sections';
  info: {
    description: '';
    displayName: 'Calculator-Section';
  };
  attributes: {
    Calculators: Schema.Attribute.Component<'home-page.calculators', true>;
    Title: Schema.Attribute.String;
  };
}

export interface HomePageCalculators extends Struct.ComponentSchema {
  collectionName: 'components_home_page_calculators';
  info: {
    displayName: 'Calculators';
  };
  attributes: {
    Button: Schema.Attribute.Component<'components.button', false>;
    Description: Schema.Attribute.Text;
    Title: Schema.Attribute.String;
  };
}

export interface HomePageCibilSection extends Struct.ComponentSchema {
  collectionName: 'components_home_page_cibil_sections';
  info: {
    displayName: 'CIBIL-Section';
  };
  attributes: {
    Button: Schema.Attribute.Component<'components.button', false>;
    Description: Schema.Attribute.String;
    SRK_Image: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    Title: Schema.Attribute.String;
  };
}

export interface HomePageClientsTestimonialsSection
  extends Struct.ComponentSchema {
  collectionName: 'components_home_page_clients_testimonials_sections';
  info: {
    displayName: 'Clients_Testimonials_section';
  };
  attributes: {
    Button: Schema.Attribute.Component<'components.button', false>;
    Description: Schema.Attribute.Text;
    Title: Schema.Attribute.String;
  };
}

export interface HomePageContactSection extends Struct.ComponentSchema {
  collectionName: 'components_home_page_contact_sections';
  info: {
    description: '';
    displayName: 'Contact-Section';
  };
  attributes: {
    Button: Schema.Attribute.Component<'components.button', false>;
    Description: Schema.Attribute.Text;
    Mail: Schema.Attribute.Email & Schema.Attribute.Required;
    Privacy_Text: Schema.Attribute.Text;
    SRK_Image: Schema.Attribute.Media<'images'>;
    Title: Schema.Attribute.String;
  };
}

export interface HomePageCounterSection extends Struct.ComponentSchema {
  collectionName: 'components_home_page_counter_sections';
  info: {
    displayName: 'Counter_Section';
  };
  attributes: {
    Icon: Schema.Attribute.Media<'images'>;
    Text: Schema.Attribute.String;
    Value: Schema.Attribute.String;
  };
}

export interface HomePageCounts extends Struct.ComponentSchema {
  collectionName: 'components_home_page_counts';
  info: {
    displayName: 'Counts';
  };
  attributes: {
    Count: Schema.Attribute.String;
    Icon: Schema.Attribute.Media<'images'>;
    Text: Schema.Attribute.String;
  };
}

export interface HomePageCsrSection extends Struct.ComponentSchema {
  collectionName: 'components_home_page_csr_sections';
  info: {
    displayName: 'CSR-Section';
  };
  attributes: {
    Button: Schema.Attribute.Component<'components.button', false>;
    Description: Schema.Attribute.Text;
    Title: Schema.Attribute.String;
  };
}

export interface HomePageCsrSectionHome extends Struct.ComponentSchema {
  collectionName: 'components_home_page_csr_section_homes';
  info: {
    displayName: 'CSR_Section_Home';
  };
  attributes: {
    Button: Schema.Attribute.Component<'components.button', false>;
    Header: Schema.Attribute.Component<'common.header-td', false>;
  };
}

export interface HomePageDigitalInitiativeSection
  extends Struct.ComponentSchema {
  collectionName: 'components_home_page_digital_initiative_sections';
  info: {
    displayName: 'Digital-Initiative-Section';
  };
  attributes: {
    Button: Schema.Attribute.Component<'components.button', false>;
    Description: Schema.Attribute.Text;
    Title: Schema.Attribute.String;
  };
}

export interface HomePageDigitalInitiatives extends Struct.ComponentSchema {
  collectionName: 'components_home_page_digital_initiatives';
  info: {
    displayName: 'Digital_Initiatives';
  };
  attributes: {
    Button: Schema.Attribute.Component<'components.button', false>;
    Header: Schema.Attribute.Component<'common.header-td', false>;
  };
}

export interface HomePageFaqSection extends Struct.ComponentSchema {
  collectionName: 'components_home_page_faq_sections';
  info: {
    displayName: 'FAQ-Section';
  };
  attributes: {
    Description: Schema.Attribute.Text;
    Title: Schema.Attribute.String;
  };
}

export interface HomePageFeedbackSection extends Struct.ComponentSchema {
  collectionName: 'components_home_page_feedback_sections';
  info: {
    description: '';
    displayName: 'Feedback-Section';
  };
  attributes: {
    Branch: Schema.Attribute.String;
    Feedback: Schema.Attribute.Blocks;
    Image: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    LoanFor: Schema.Attribute.String & Schema.Attribute.Required;
    Name: Schema.Attribute.String & Schema.Attribute.Required;
    Reagion: Schema.Attribute.String;
  };
}

export interface HomePageGetinTouchFormSection extends Struct.ComponentSchema {
  collectionName: 'components_home_page_getin_touch_form_sections';
  info: {
    displayName: 'Getin_Touch_Form_Section';
  };
  attributes: {
    Description: Schema.Attribute.Text;
    Mail: Schema.Attribute.String;
    Text: Schema.Attribute.String;
    Title: Schema.Attribute.String;
  };
}

export interface HomePageHomeBanner extends Struct.ComponentSchema {
  collectionName: 'components_home_page_home_banner_s';
  info: {
    displayName: 'homeBanner ';
  };
  attributes: {
    Title: Schema.Attribute.String;
  };
}

export interface HomePageHomeBanners extends Struct.ComponentSchema {
  collectionName: 'components_home_page_home_banners';
  info: {
    description: '';
    displayName: 'Home_Banners';
  };
  attributes: {
    BannerOrder: Schema.Attribute.Integer;
    Description: Schema.Attribute.Text;
    ImageOrVideo: Schema.Attribute.Media<'images' | 'videos'>;
    mobileBanner: Schema.Attribute.Media<'images' | 'videos'>;
    RedirectLink: Schema.Attribute.String;
    Title: Schema.Attribute.String;
  };
}

export interface HomePageLoanSection extends Struct.ComponentSchema {
  collectionName: 'components_home_page_loan_sections';
  info: {
    displayName: 'Loan-Section';
  };
  attributes: {
    Button: Schema.Attribute.Component<'components.button', false>;
    Description: Schema.Attribute.Text;
    SRK_Image: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    Title: Schema.Attribute.String;
  };
}

export interface HomePageProductsSections extends Struct.ComponentSchema {
  collectionName: 'components_home_page_products_sections';
  info: {
    description: '';
    displayName: 'Products-Sections';
  };
  attributes: {
    SRK_Image: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    Title: Schema.Attribute.String;
  };
}

export interface HomePageShowcaseBrandSection extends Struct.ComponentSchema {
  collectionName: 'components_home_page_showcase_brand_sections';
  info: {
    displayName: 'Showcase_Brand_Section';
  };
  attributes: {
    Showcase_Section_Title: Schema.Attribute.String;
    ShowcaseVideos: Schema.Attribute.Component<
      'home-page.showcase-videos',
      true
    >;
  };
}

export interface HomePageShowcaseSection extends Struct.ComponentSchema {
  collectionName: 'components_home_page_showcase_sections';
  info: {
    description: '';
    displayName: 'Showcase-Section';
  };
  attributes: {
    Button: Schema.Attribute.Component<'components.button', false>;
    Title: Schema.Attribute.String;
  };
}

export interface HomePageShowcaseVideos extends Struct.ComponentSchema {
  collectionName: 'components_home_page_showcase_videos';
  info: {
    description: '';
    displayName: 'ShowcaseVideos';
  };
  attributes: {
    Text: Schema.Attribute.String;
    Thumbnail: Schema.Attribute.Media<'images'>;
    Video: Schema.Attribute.Media<'videos'>;
    Video_Link: Schema.Attribute.Text;
  };
}

export interface IndependentDirectorsCardIndependentDirectorsCard
  extends Struct.ComponentSchema {
  collectionName: 'components_independent_directors_card_independent_directors_cards';
  info: {
    displayName: 'Independent_Directors_Card';
  };
  attributes: {
    Description: Schema.Attribute.Text;
    Designation: Schema.Attribute.String;
    Image: Schema.Attribute.Media<'images'>;
    Name: Schema.Attribute.String;
  };
}

export interface ProductBannerDetail extends Struct.ComponentSchema {
  collectionName: 'components_product_banner_details';
  info: {
    displayName: 'Banner_Detail';
  };
  attributes: {
    Banner: Schema.Attribute.Media<'images'>;
    Description: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
    Title: Schema.Attribute.String;
  };
}

export interface ProductBenefitsCard extends Struct.ComponentSchema {
  collectionName: 'components_product_benefits_cards';
  info: {
    description: '';
    displayName: 'Benefits-Card';
    icon: 'discuss';
  };
  attributes: {
    BenefitsSubCard: Schema.Attribute.Component<'common.card-td', true>;
    SRK_Image: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    Title: Schema.Attribute.String & Schema.Attribute.Required;
  };
}

export interface ProductBenefitsSec extends Struct.ComponentSchema {
  collectionName: 'components_product_benefits_secs';
  info: {
    displayName: 'Benefits_Sec';
  };
  attributes: {
    Description: Schema.Attribute.Text;
    Flexible_Tenure_Texts: Schema.Attribute.Component<
      'product.flexible-tenure-texts',
      false
    >;
    Hashtag_Text: Schema.Attribute.String;
    Highlited_Text1: Schema.Attribute.String;
    Highlited_Text2: Schema.Attribute.String;
    Percentage_Text: Schema.Attribute.String;
    SRK_Image: Schema.Attribute.Media<'images'>;
    Text: Schema.Attribute.String;
  };
}

export interface ProductBranchBox extends Struct.ComponentSchema {
  collectionName: 'components_product_branch_boxes';
  info: {
    description: '';
    displayName: 'Branch_Box';
  };
  attributes: {
    Address: Schema.Attribute.Text;
    Location: Schema.Attribute.String;
    Main_location: Schema.Attribute.String;
    Main_location_Link: Schema.Attribute.String;
    Opening_Time: Schema.Attribute.String;
    Phone_Number: Schema.Attribute.String;
    Title: Schema.Attribute.String;
  };
}

export interface ProductBranchesSec extends Struct.ComponentSchema {
  collectionName: 'components_product_branches_secs';
  info: {
    displayName: 'Branches_Sec';
  };
  attributes: {
    Bg_Image: Schema.Attribute.Media<'images'>;
    Branch_Box: Schema.Attribute.Component<'product.branch-box', true>;
    Button: Schema.Attribute.Component<'components.button', false>;
    Description: Schema.Attribute.Text;
    Instruction: Schema.Attribute.String;
    Title: Schema.Attribute.String;
  };
}

export interface ProductCalculatorSec extends Struct.ComponentSchema {
  collectionName: 'components_product_calculator_secs';
  info: {
    displayName: 'Calculator_Sec';
  };
  attributes: {
    Header: Schema.Attribute.Component<'common.header-td', false>;
  };
}

export interface ProductCardPoints extends Struct.ComponentSchema {
  collectionName: 'components_product_card_points';
  info: {
    displayName: 'Card_Points';
  };
  attributes: {
    Title: Schema.Attribute.String;
    Value: Schema.Attribute.String;
  };
}

export interface ProductClientTestimonialSec extends Struct.ComponentSchema {
  collectionName: 'components_product_client_testimonial_secs';
  info: {
    displayName: 'Client_Testimonial_sec';
  };
  attributes: {
    Description: Schema.Attribute.String;
    Title: Schema.Attribute.String;
  };
}

export interface ProductColumn extends Struct.ComponentSchema {
  collectionName: 'components_product_columns';
  info: {
    displayName: 'Column';
  };
  attributes: {
    Column_Options: Schema.Attribute.Component<'product.column-options', true>;
    Column_Title: Schema.Attribute.String;
  };
}

export interface ProductColumnOptions extends Struct.ComponentSchema {
  collectionName: 'components_product_column_options';
  info: {
    displayName: 'Column_Options';
  };
  attributes: {
    Option: Schema.Attribute.String;
  };
}

export interface ProductCriterias extends Struct.ComponentSchema {
  collectionName: 'components_product_criterias';
  info: {
    description: '';
    displayName: 'Criterias';
  };
  attributes: {
    Count: Schema.Attribute.String;
    Criteria_Text: Schema.Attribute.Text;
    Hover_Image: Schema.Attribute.Media<'images'>;
  };
}

export interface ProductDepositTable extends Struct.ComponentSchema {
  collectionName: 'components_product_deposit_tables';
  info: {
    description: '';
    displayName: 'Deposit_Table';
  };
  attributes: {
    Description: Schema.Attribute.Text;
    Table_Content: Schema.Attribute.Component<'product.table-content', true>;
    Title: Schema.Attribute.String;
  };
}

export interface ProductDigitalPlatform extends Struct.ComponentSchema {
  collectionName: 'components_product_digital_platforms';
  info: {
    displayName: 'DigitalPlatform';
  };
  attributes: {
    Description: Schema.Attribute.Text;
    DigitalPlatformCard: Schema.Attribute.Component<
      'product.digital-platform-card',
      true
    >;
    Title: Schema.Attribute.String;
  };
}

export interface ProductDigitalPlatformCard extends Struct.ComponentSchema {
  collectionName: 'components_product_digital_platform_cards';
  info: {
    displayName: 'DigitalPlatformCard';
  };
  attributes: {
    Icon: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    RedirectionLink: Schema.Attribute.String;
    Text: Schema.Attribute.String;
  };
}

export interface ProductDocumentsBox extends Struct.ComponentSchema {
  collectionName: 'components_product_documents_boxes';
  info: {
    displayName: 'Documents_Box';
  };
  attributes: {
    Description: Schema.Attribute.Text;
    Icon: Schema.Attribute.Media<'images'>;
    Title: Schema.Attribute.String;
  };
}

export interface ProductDocumentsCard extends Struct.ComponentSchema {
  collectionName: 'components_product_documents_cards';
  info: {
    description: '';
    displayName: 'Documents-Card';
  };
  attributes: {
    Description: Schema.Attribute.Text;
    DocumentCard: Schema.Attribute.Component<'common.section-td', true>;
    Title: Schema.Attribute.String;
  };
}

export interface ProductDocumentsSec extends Struct.ComponentSchema {
  collectionName: 'components_product_documents_secs';
  info: {
    displayName: 'Documents_Sec';
  };
  attributes: {
    Description: Schema.Attribute.Text;
    Documents_Box: Schema.Attribute.Component<'product.documents-box', true>;
    SRK_Image: Schema.Attribute.Media<'images'>;
    Title: Schema.Attribute.String;
  };
}

export interface ProductEligibilityCard extends Struct.ComponentSchema {
  collectionName: 'components_product_eligibility_cards';
  info: {
    description: '';
    displayName: 'Eligibility-card';
  };
  attributes: {
    Button: Schema.Attribute.Component<'components.button', false>;
    Criterias: Schema.Attribute.Component<'common.card-t', true>;
    Description: Schema.Attribute.Text;
    Title: Schema.Attribute.String & Schema.Attribute.Required;
  };
}

export interface ProductEligibilitySec extends Struct.ComponentSchema {
  collectionName: 'components_product_eligibility_secs';
  info: {
    displayName: 'Eligibility_Sec';
  };
  attributes: {
    Criterias: Schema.Attribute.Component<'product.criterias', true>;
    Description: Schema.Attribute.Text;
    Title: Schema.Attribute.String;
  };
}

export interface ProductFaq extends Struct.ComponentSchema {
  collectionName: 'components_product_faqs';
  info: {
    displayName: 'Faq';
  };
  attributes: {
    Description: Schema.Attribute.Text;
    FAQ: Schema.Attribute.Component<'product.faq-s', true>;
    Title: Schema.Attribute.String;
  };
}

export interface ProductFaqS extends Struct.ComponentSchema {
  collectionName: 'components_product_faq_s';
  info: {
    displayName: 'FAQ,S';
  };
  attributes: {
    Answer: Schema.Attribute.Text;
    Question: Schema.Attribute.String;
  };
}

export interface ProductFaqSection extends Struct.ComponentSchema {
  collectionName: 'components_product_faq_sections';
  info: {
    description: '';
    displayName: 'Faq-Section';
  };
  attributes: {
    Description: Schema.Attribute.String;
    Faq: Schema.Attribute.Component<'components.faq', true>;
    Title: Schema.Attribute.String;
  };
}

export interface ProductFlexibleTenureTexts extends Struct.ComponentSchema {
  collectionName: 'components_product_flexible_tenure_texts';
  info: {
    displayName: 'Flexible_Tenure_Texts';
  };
  attributes: {
    Text: Schema.Attribute.String;
    Text1: Schema.Attribute.String;
    Text2: Schema.Attribute.String;
    Value: Schema.Attribute.String;
  };
}

export interface ProductHeader extends Struct.ComponentSchema {
  collectionName: 'components_product_headers';
  info: {
    description: '';
    displayName: 'Header';
  };
  attributes: {
    Description: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
    Logo: Schema.Attribute.Media<'images'>;
    SRK_Image: Schema.Attribute.Media<'images'>;
    Title: Schema.Attribute.String;
  };
}

export interface ProductImportantDocumentBox extends Struct.ComponentSchema {
  collectionName: 'components_product_important_document_boxes';
  info: {
    displayName: 'Important_Document_Box';
  };
  attributes: {
    Description: Schema.Attribute.Text;
    Document: Schema.Attribute.Media<'files' | 'images'>;
    Title: Schema.Attribute.String;
  };
}

export interface ProductImportantDocumentsSection
  extends Struct.ComponentSchema {
  collectionName: 'components_product_important_documents_sections';
  info: {
    displayName: 'Important_Documents_Section';
  };
  attributes: {
    Important_Document_Box: Schema.Attribute.Component<
      'product.important-document-box',
      true
    >;
    Title: Schema.Attribute.String;
  };
}

export interface ProductKeyBenefitsCard extends Struct.ComponentSchema {
  collectionName: 'components_product_key_benefits_cards';
  info: {
    displayName: 'Key_Benefits_Card';
  };
  attributes: {
    Description: Schema.Attribute.Text;
    Title: Schema.Attribute.String;
  };
}

export interface ProductKeyBenefitsSec extends Struct.ComponentSchema {
  collectionName: 'components_product_key_benefits_secs';
  info: {
    displayName: 'Key_Benefits_Sec';
  };
  attributes: {
    Key_Benefits_Card: Schema.Attribute.Component<
      'product.key-benefits-card',
      true
    >;
    SRK_Image: Schema.Attribute.Media<'images'>;
    Title: Schema.Attribute.String;
  };
}

export interface ProductLoanApplySection extends Struct.ComponentSchema {
  collectionName: 'components_product_loan_apply_sections';
  info: {
    displayName: 'Loan-Apply-Section';
  };
  attributes: {
    Button: Schema.Attribute.Component<'components.button', false>;
    Description: Schema.Attribute.Text;
    Funding_Percentage: Schema.Attribute.String;
    HashTag: Schema.Attribute.String;
    Loan_Percentage: Schema.Attribute.String;
    Tag: Schema.Attribute.String;
    Tenure_Month: Schema.Attribute.String;
  };
}

export interface ProductPayDownloadSec extends Struct.ComponentSchema {
  collectionName: 'components_product_pay_download_secs';
  info: {
    displayName: 'Pay_Download_Sec';
  };
  attributes: {
    Button: Schema.Attribute.Component<'components.button', false>;
    Description: Schema.Attribute.Text;
    Icon: Schema.Attribute.Media<'images'>;
    Title: Schema.Attribute.String;
  };
}

export interface ProductProccesingChargesSec extends Struct.ComponentSchema {
  collectionName: 'components_product_proccesing_charges_secs';
  info: {
    displayName: 'Proccesing_Charges_Sec';
  };
  attributes: {
    Card_Points: Schema.Attribute.Component<'product.card-points', true>;
    Instruction: Schema.Attribute.String;
    Points: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
    Title: Schema.Attribute.String;
  };
}

export interface ProductProductDetails extends Struct.ComponentSchema {
  collectionName: 'components_product_product_details';
  info: {
    description: '';
    displayName: 'Product_Details';
  };
  attributes: {
    Banner_Detail: Schema.Attribute.Component<'product.banner-detail', false>;
    Benefits_Sec: Schema.Attribute.Component<'product.benefits-sec', false>;
    Branches_Sec: Schema.Attribute.Component<'product.branches-sec', false>;
    Calculator_Sec: Schema.Attribute.Component<'product.calculator-sec', false>;
    Client_Testimonial_sec: Schema.Attribute.Component<
      'product.client-testimonial-sec',
      false
    >;
    Deposit_Table: Schema.Attribute.Component<'product.deposit-table', false>;
    DigitalPlatform: Schema.Attribute.Component<
      'product.digital-platform',
      false
    >;
    Documents_Sec: Schema.Attribute.Component<'product.documents-sec', false>;
    Eligibility_Sec: Schema.Attribute.Component<
      'product.eligibility-sec',
      false
    >;
    Faq_Sec: Schema.Attribute.Component<'product.faq', false>;
    Header_Sec: Schema.Attribute.Component<'product.header', false>;
    Important_Documents_Section: Schema.Attribute.Component<
      'product.important-documents-section',
      false
    >;
    Key_Benefits_Sec: Schema.Attribute.Component<
      'product.key-benefits-sec',
      false
    >;
    Pay_Download_Sec: Schema.Attribute.Component<
      'product.pay-download-sec',
      true
    >;
    Proccesing_Charges_Sec: Schema.Attribute.Component<
      'product.proccesing-charges-sec',
      false
    >;
    Proccesing_Charges_Sec2: Schema.Attribute.Component<
      'product.proccesing-charges-sec',
      false
    >;
    Products_Offered_Section: Schema.Attribute.Component<
      'product.products-offered-section',
      false
    >;
  };
}

export interface ProductProductDetailsData extends Struct.ComponentSchema {
  collectionName: 'components_product_product_details_data';
  info: {
    description: '';
    displayName: 'Product_Details_Data';
  };
  attributes: {
    Product_Details: Schema.Attribute.Component<
      'product.product-details',
      false
    >;
  };
}

export interface ProductProductHeader extends Struct.ComponentSchema {
  collectionName: 'components_product_product_headers';
  info: {
    displayName: 'Product_Header';
  };
  attributes: {
    Button: Schema.Attribute.Component<'components.button', false>;
    Points: Schema.Attribute.Blocks;
    SRK_Image: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    Title: Schema.Attribute.Text & Schema.Attribute.Required;
  };
}

export interface ProductProductsOfferedSection extends Struct.ComponentSchema {
  collectionName: 'components_product_products_offered_sections';
  info: {
    displayName: 'Products_Offered_Section';
  };
  attributes: {
    Products_Offered_Box: Schema.Attribute.Component<
      'product.key-benefits-card',
      true
    >;
    Title: Schema.Attribute.String;
  };
}

export interface ProductSchemeContent extends Struct.ComponentSchema {
  collectionName: 'components_product_scheme_contents';
  info: {
    displayName: 'Scheme_Content';
  };
  attributes: {
    Scheme_Name: Schema.Attribute.String;
  };
}

export interface ProductSchemeTab extends Struct.ComponentSchema {
  collectionName: 'components_product_scheme_tabs';
  info: {
    description: '';
    displayName: 'Scheme_Tab';
  };
  attributes: {
    Column: Schema.Attribute.Component<'product.column', true>;
    Scheme_Content: Schema.Attribute.Component<'product.scheme-content', true>;
    Tab_Name: Schema.Attribute.String;
  };
}

export interface ProductShowcaseCard extends Struct.ComponentSchema {
  collectionName: 'components_product_showcase_cards';
  info: {
    description: '';
    displayName: 'Showcase-Card';
  };
  attributes: {
    Description: Schema.Attribute.String;
    Title: Schema.Attribute.String;
    Videos: Schema.Attribute.Component<'template.video', true>;
  };
}

export interface ProductTableContent extends Struct.ComponentSchema {
  collectionName: 'components_product_table_contents';
  info: {
    displayName: 'Table_Content';
  };
  attributes: {
    Scheme_Tab: Schema.Attribute.Component<'product.scheme-tab', false>;
  };
}

export interface QuoteQuoteT extends Struct.ComponentSchema {
  collectionName: 'components_quote_quote_ts';
  info: {
    description: '';
    displayName: 'Quote-Words';
    icon: 'bell';
  };
  attributes: {
    Designation: Schema.Attribute.String;
    Name: Schema.Attribute.String & Schema.Attribute.Required;
    Profile: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    Quote: Schema.Attribute.Text;
  };
}

export interface QuoteQuoteV extends Struct.ComponentSchema {
  collectionName: 'components_quote_quote_vs';
  info: {
    description: '';
    displayName: 'Quote-Video';
    icon: 'cog';
  };
  attributes: {
    Designation: Schema.Attribute.String;
    Name: Schema.Attribute.String & Schema.Attribute.Required;
    Profile: Schema.Attribute.Media<'images'>;
    Video: Schema.Attribute.Media<'videos'>;
  };
}

export interface SectionTdrichdSection7BlueSoch extends Struct.ComponentSchema {
  collectionName: 'components_section_tdrichd_section7_blue_soches';
  info: {
    displayName: 'Section7_BlueSoch';
  };
  attributes: {
    Description: Schema.Attribute.Text;
    Description_2: Schema.Attribute.Blocks;
    Title: Schema.Attribute.String;
  };
}

export interface SubMenuLabelIcon extends Struct.ComponentSchema {
  collectionName: 'components_sub_menu_label_icons';
  info: {
    displayName: 'Label-Icon';
    icon: 'cog';
  };
  attributes: {
    Icon: Schema.Attribute.Media<'images'>;
    Label: Schema.Attribute.String;
    Link: Schema.Attribute.String;
  };
}

export interface SubMenuLabelImage extends Struct.ComponentSchema {
  collectionName: 'components_sub_menu_label_images';
  info: {
    description: '';
    displayName: 'Label-Image';
    icon: 'picture';
  };
  attributes: {
    Image: Schema.Attribute.Media<'images'>;
    Label: Schema.Attribute.String & Schema.Attribute.Required;
    Link: Schema.Attribute.String & Schema.Attribute.Required;
  };
}

export interface SubMenuSubmenu extends Struct.ComponentSchema {
  collectionName: 'components_sub_menu_submenus';
  info: {
    description: '';
    displayName: 'submenu';
  };
  attributes: {
    SubMenu_Data: Schema.Attribute.Component<'sub-menu.label-image', true>;
    SubMenu_SRK_Image: Schema.Attribute.Media<'images'> &
      Schema.Attribute.Required;
    SubMenu_Title: Schema.Attribute.String & Schema.Attribute.Required;
  };
}

export interface SubMenuSubmenuLabel extends Struct.ComponentSchema {
  collectionName: 'components_sub_menu_submenu_labels';
  info: {
    description: '';
    displayName: 'Label-Only';
    icon: 'hashtag';
  };
  attributes: {
    Label: Schema.Attribute.String;
    Link: Schema.Attribute.String;
  };
}

export interface TemplateImage extends Struct.ComponentSchema {
  collectionName: 'components_template_images';
  info: {
    description: '';
    displayName: 'Image';
  };
  attributes: {
    Image: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    Title: Schema.Attribute.String;
  };
}

export interface TemplateTitleColored extends Struct.ComponentSchema {
  collectionName: 'components_template_title_coloreds';
  info: {
    description: '';
    displayName: 'Title-Colored';
  };
  attributes: {
    SRK_Image: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    Title: Schema.Attribute.String;
  };
}

export interface TemplateVideo extends Struct.ComponentSchema {
  collectionName: 'components_template_videos';
  info: {
    displayName: 'Video';
  };
  attributes: {
    Video: Schema.Attribute.Media<'videos'> & Schema.Attribute.Required;
  };
}

declare module '@strapi/strapi' {
  export module Public {
    export interface ComponentSchemas {
      'about-us.chairman-card': AboutUsChairmanCard;
      'about-us.corporate-ethos-card': AboutUsCorporateEthosCard;
      'about-us.director-card': AboutUsDirectorCard;
      'about-us.generation-prople-card': AboutUsGenerationPropleCard;
      'about-us.milstone-card': AboutUsMilstoneCard;
      'about-us.operating-philosophy-card': AboutUsOperatingPhilosophyCard;
      'about-us.pillars-card': AboutUsPillarsCard;
      'about-us.principles-card': AboutUsPrinciplesCard;
      'about.about': AboutAbout;
      'about.milestone-card': AboutMilestoneCard;
      'career.career-card': CareerCareerCard;
      'career.career-header-card': CareerCareerHeaderCard;
      'career.chairman-card': CareerChairmanCard;
      'career.header-card': CareerHeaderCard;
      'career.our-muthootees': CareerOurMuthootees;
      'common.award-card': CommonAwardCard;
      'common.banner-btd': CommonBannerBtd;
      'common.banner-itd': CommonBannerItd;
      'common.banner-tds': CommonBannerTds;
      'common.card-about': CommonCardAbout;
      'common.card-it': CommonCardIt;
      'common.card-itd': CommonCardItd;
      'common.card-itl': CommonCardItl;
      'common.card-t': CommonCardT;
      'common.card-td': CommonCardTd;
      'common.card-tdf': CommonCardTdf;
      'common.csr-card': CommonCsrCard;
      'common.download-card': CommonDownloadCard;
      'common.header': CommonHeader;
      'common.header-td': CommonHeaderTd;
      'common.header-td-colored': CommonHeaderTdColored;
      'common.header-top': CommonHeaderTop;
      'common.header2': CommonHeader2;
      'common.itl': CommonItl;
      'common.land-scape-images': CommonLandScapeImages;
      'common.link-tl': CommonLinkTl;
      'common.main-header': CommonMainHeader;
      'common.memorial-card': CommonMemorialCard;
      'common.section-its': CommonSectionIts;
      'common.section-td': CommonSectionTd;
      'common.section-tv': CommonSectionTv;
      'common.seo': CommonSeo;
      'common.sub-menu': CommonSubMenu;
      'common.sub-menu-type-2': CommonSubMenuType2;
      'common.submenu-navigators': CommonSubmenuNavigators;
      'components.award-card': ComponentsAwardCard;
      'components.button': ComponentsButton;
      'components.card-trichd': ComponentsCardTrichd;
      'components.companies-box': ComponentsCompaniesBox;
      'components.download-button': ComponentsDownloadButton;
      'components.faq': ComponentsFaq;
      'components.media-list-card': ComponentsMediaListCard;
      'components.navbar': ComponentsNavbar;
      'components.social-media': ComponentsSocialMedia;
      'contact.connect-card': ContactConnectCard;
      'contact.connect-card-button': ContactConnectCardButton;
      'contact.contact-address-card': ContactContactAddressCard;
      'footer.address-card': FooterAddressCard;
      'footer.contact-card': FooterContactCard;
      'footer.link-card': FooterLinkCard;
      'footer.stock-logos': FooterStockLogos;
      'header.header1': HeaderHeader1;
      'header.logo': HeaderLogo;
      'home-page.about-us-section': HomePageAboutUsSection;
      'home-page.blog-section': HomePageBlogSection;
      'home-page.blogs-section': HomePageBlogsSection;
      'home-page.branch-section': HomePageBranchSection;
      'home-page.branches-section': HomePageBranchesSection;
      'home-page.calculator-section': HomePageCalculatorSection;
      'home-page.calculators': HomePageCalculators;
      'home-page.cibil-section': HomePageCibilSection;
      'home-page.clients-testimonials-section': HomePageClientsTestimonialsSection;
      'home-page.contact-section': HomePageContactSection;
      'home-page.counter-section': HomePageCounterSection;
      'home-page.counts': HomePageCounts;
      'home-page.csr-section': HomePageCsrSection;
      'home-page.csr-section-home': HomePageCsrSectionHome;
      'home-page.digital-initiative-section': HomePageDigitalInitiativeSection;
      'home-page.digital-initiatives': HomePageDigitalInitiatives;
      'home-page.faq-section': HomePageFaqSection;
      'home-page.feedback-section': HomePageFeedbackSection;
      'home-page.getin-touch-form-section': HomePageGetinTouchFormSection;
      'home-page.home-banner': HomePageHomeBanner;
      'home-page.home-banners': HomePageHomeBanners;
      'home-page.loan-section': HomePageLoanSection;
      'home-page.products-sections': HomePageProductsSections;
      'home-page.showcase-brand-section': HomePageShowcaseBrandSection;
      'home-page.showcase-section': HomePageShowcaseSection;
      'home-page.showcase-videos': HomePageShowcaseVideos;
      'independent-directors-card.independent-directors-card': IndependentDirectorsCardIndependentDirectorsCard;
      'product.banner-detail': ProductBannerDetail;
      'product.benefits-card': ProductBenefitsCard;
      'product.benefits-sec': ProductBenefitsSec;
      'product.branch-box': ProductBranchBox;
      'product.branches-sec': ProductBranchesSec;
      'product.calculator-sec': ProductCalculatorSec;
      'product.card-points': ProductCardPoints;
      'product.client-testimonial-sec': ProductClientTestimonialSec;
      'product.column': ProductColumn;
      'product.column-options': ProductColumnOptions;
      'product.criterias': ProductCriterias;
      'product.deposit-table': ProductDepositTable;
      'product.digital-platform': ProductDigitalPlatform;
      'product.digital-platform-card': ProductDigitalPlatformCard;
      'product.documents-box': ProductDocumentsBox;
      'product.documents-card': ProductDocumentsCard;
      'product.documents-sec': ProductDocumentsSec;
      'product.eligibility-card': ProductEligibilityCard;
      'product.eligibility-sec': ProductEligibilitySec;
      'product.faq': ProductFaq;
      'product.faq-s': ProductFaqS;
      'product.faq-section': ProductFaqSection;
      'product.flexible-tenure-texts': ProductFlexibleTenureTexts;
      'product.header': ProductHeader;
      'product.important-document-box': ProductImportantDocumentBox;
      'product.important-documents-section': ProductImportantDocumentsSection;
      'product.key-benefits-card': ProductKeyBenefitsCard;
      'product.key-benefits-sec': ProductKeyBenefitsSec;
      'product.loan-apply-section': ProductLoanApplySection;
      'product.pay-download-sec': ProductPayDownloadSec;
      'product.proccesing-charges-sec': ProductProccesingChargesSec;
      'product.product-details': ProductProductDetails;
      'product.product-details-data': ProductProductDetailsData;
      'product.product-header': ProductProductHeader;
      'product.products-offered-section': ProductProductsOfferedSection;
      'product.scheme-content': ProductSchemeContent;
      'product.scheme-tab': ProductSchemeTab;
      'product.showcase-card': ProductShowcaseCard;
      'product.table-content': ProductTableContent;
      'quote.quote-t': QuoteQuoteT;
      'quote.quote-v': QuoteQuoteV;
      'section-tdrichd.section7-blue-soch': SectionTdrichdSection7BlueSoch;
      'sub-menu.label-icon': SubMenuLabelIcon;
      'sub-menu.label-image': SubMenuLabelImage;
      'sub-menu.submenu': SubMenuSubmenu;
      'sub-menu.submenu-label': SubMenuSubmenuLabel;
      'template.image': TemplateImage;
      'template.title-colored': TemplateTitleColored;
      'template.video': TemplateVideo;
    }
  }
}
