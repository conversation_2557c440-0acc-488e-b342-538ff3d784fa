import type { Schema, Struct } from '@strapi/strapi';

export interface AdminApiToken extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_api_tokens';
  info: {
    description: '';
    displayName: 'Api Token';
    name: 'Api Token';
    pluralName: 'api-tokens';
    singularName: 'api-token';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    accessKey: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    description: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }> &
      Schema.Attribute.DefaultTo<''>;
    expiresAt: Schema.Attribute.DateTime;
    lastUsedAt: Schema.Attribute.DateTime;
    lifespan: Schema.Attribute.BigInteger;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'admin::api-token'> &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    permissions: Schema.Attribute.Relation<
      'oneToMany',
      'admin::api-token-permission'
    >;
    publishedAt: Schema.Attribute.DateTime;
    type: Schema.Attribute.Enumeration<['read-only', 'full-access', 'custom']> &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'read-only'>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface AdminApiTokenPermission extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_api_token_permissions';
  info: {
    description: '';
    displayName: 'API Token Permission';
    name: 'API Token Permission';
    pluralName: 'api-token-permissions';
    singularName: 'api-token-permission';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'admin::api-token-permission'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    token: Schema.Attribute.Relation<'manyToOne', 'admin::api-token'>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface AdminPermission extends Struct.CollectionTypeSchema {
  collectionName: 'admin_permissions';
  info: {
    description: '';
    displayName: 'Permission';
    name: 'Permission';
    pluralName: 'permissions';
    singularName: 'permission';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    actionParameters: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<{}>;
    conditions: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<[]>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'admin::permission'> &
      Schema.Attribute.Private;
    properties: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<{}>;
    publishedAt: Schema.Attribute.DateTime;
    role: Schema.Attribute.Relation<'manyToOne', 'admin::role'>;
    subject: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface AdminRole extends Struct.CollectionTypeSchema {
  collectionName: 'admin_roles';
  info: {
    description: '';
    displayName: 'Role';
    name: 'Role';
    pluralName: 'roles';
    singularName: 'role';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    code: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    description: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'admin::role'> &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    permissions: Schema.Attribute.Relation<'oneToMany', 'admin::permission'>;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    users: Schema.Attribute.Relation<'manyToMany', 'admin::user'>;
  };
}

export interface AdminTransferToken extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_transfer_tokens';
  info: {
    description: '';
    displayName: 'Transfer Token';
    name: 'Transfer Token';
    pluralName: 'transfer-tokens';
    singularName: 'transfer-token';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    accessKey: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    description: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }> &
      Schema.Attribute.DefaultTo<''>;
    expiresAt: Schema.Attribute.DateTime;
    lastUsedAt: Schema.Attribute.DateTime;
    lifespan: Schema.Attribute.BigInteger;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'admin::transfer-token'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    permissions: Schema.Attribute.Relation<
      'oneToMany',
      'admin::transfer-token-permission'
    >;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface AdminTransferTokenPermission
  extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_transfer_token_permissions';
  info: {
    description: '';
    displayName: 'Transfer Token Permission';
    name: 'Transfer Token Permission';
    pluralName: 'transfer-token-permissions';
    singularName: 'transfer-token-permission';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'admin::transfer-token-permission'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    token: Schema.Attribute.Relation<'manyToOne', 'admin::transfer-token'>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface AdminUser extends Struct.CollectionTypeSchema {
  collectionName: 'admin_users';
  info: {
    description: '';
    displayName: 'User';
    name: 'User';
    pluralName: 'users';
    singularName: 'user';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    blocked: Schema.Attribute.Boolean &
      Schema.Attribute.Private &
      Schema.Attribute.DefaultTo<false>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    email: Schema.Attribute.Email &
      Schema.Attribute.Required &
      Schema.Attribute.Private &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    firstname: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    isActive: Schema.Attribute.Boolean &
      Schema.Attribute.Private &
      Schema.Attribute.DefaultTo<false>;
    lastname: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'admin::user'> &
      Schema.Attribute.Private;
    password: Schema.Attribute.Password &
      Schema.Attribute.Private &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    preferedLanguage: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    registrationToken: Schema.Attribute.String & Schema.Attribute.Private;
    resetPasswordToken: Schema.Attribute.String & Schema.Attribute.Private;
    roles: Schema.Attribute.Relation<'manyToMany', 'admin::role'> &
      Schema.Attribute.Private;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    username: Schema.Attribute.String;
  };
}

export interface ApiAboutUsPageAboutUsPage extends Struct.SingleTypeSchema {
  collectionName: 'about_us_pages';
  info: {
    description: '';
    displayName: 'About Us Page';
    pluralName: 'about-us-pages';
    singularName: 'about-us-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    Banner: Schema.Attribute.Component<'common.banner-tds', false>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    Header: Schema.Attribute.Component<'common.section-td', false>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::about-us-page.about-us-page'
    > &
      Schema.Attribute.Private;
    MilestonesCard: Schema.Attribute.Component<'about.milestone-card', false>;
    Operating_Philosophy: Schema.Attribute.Component<
      'about-us.operating-philosophy-card',
      false
    >;
    publishedAt: Schema.Attribute.DateTime;
    Section_6Aboutus: Schema.Attribute.Component<'common.card-itd', false>;
    Section7_About: Schema.Attribute.Component<
      'section-tdrichd.section7-blue-soch',
      false
    >;
    SEO: Schema.Attribute.Component<'common.seo', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiAwardAward extends Struct.CollectionTypeSchema {
  collectionName: 'awards';
  info: {
    description: '';
    displayName: 'Award';
    pluralName: 'awards';
    singularName: 'award';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    Award_Order: Schema.Attribute.Integer;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    Description: Schema.Attribute.Text;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::award.award'> &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    Thumbnail: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    Title: Schema.Attribute.Text;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiAwardsAndRecognitionPageAwardsAndRecognitionPage
  extends Struct.SingleTypeSchema {
  collectionName: 'awards_and_recognition_pages';
  info: {
    description: '';
    displayName: 'Awards & Recognition Page';
    pluralName: 'awards-and-recognition-pages';
    singularName: 'awards-and-recognition-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    Banner: Schema.Attribute.Component<'common.banner-itd', false>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    Header: Schema.Attribute.Component<'common.header-td-colored', false>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::awards-and-recognition-page.awards-and-recognition-page'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    SEO: Schema.Attribute.Component<'common.seo', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiBlogCategoryBlogCategory
  extends Struct.CollectionTypeSchema {
  collectionName: 'blog_categories';
  info: {
    description: '';
    displayName: 'Blog Category';
    pluralName: 'blog-categories';
    singularName: 'blog-category';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    Blog: Schema.Attribute.Relation<'manyToOne', 'api::blog.blog'>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::blog-category.blog-category'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    Title: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiBlogPageBlogPage extends Struct.SingleTypeSchema {
  collectionName: 'blog_pages';
  info: {
    description: '';
    displayName: 'Blog Page';
    pluralName: 'blog-pages';
    singularName: 'blog-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    Banner: Schema.Attribute.Component<'common.banner-itd', false>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    Header: Schema.Attribute.Component<'common.section-td', false>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::blog-page.blog-page'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    SEO: Schema.Attribute.Component<'common.seo', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiBlogBlog extends Struct.CollectionTypeSchema {
  collectionName: 'blogs';
  info: {
    description: '';
    displayName: 'Blog';
    pluralName: 'blogs';
    singularName: 'blog';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    Banner: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    Blog_categories: Schema.Attribute.Relation<
      'oneToMany',
      'api::blog-category.blog-category'
    >;
    BlogOrder: Schema.Attribute.Integer;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    Date: Schema.Attribute.Date;
    Description: Schema.Attribute.Blocks & Schema.Attribute.Required;
    DescriptionTitle: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::blog.blog'> &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    Tags: Schema.Attribute.JSON &
      Schema.Attribute.CustomField<'plugin::tagsinput.tags'>;
    Thumbnail: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    Title: Schema.Attribute.String & Schema.Attribute.Required;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiBranchPageBranchPage extends Struct.SingleTypeSchema {
  collectionName: 'branch_pages';
  info: {
    description: '';
    displayName: 'Branch Page';
    pluralName: 'branch-pages';
    singularName: 'branch-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    Banner: Schema.Attribute.Component<'common.banner-itd', false>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    Header: Schema.Attribute.Component<'common.section-td', false>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::branch-page.branch-page'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    SEO: Schema.Attribute.Component<'common.seo', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiBrancheBranche extends Struct.CollectionTypeSchema {
  collectionName: 'branches';
  info: {
    description: '';
    displayName: 'Branch';
    pluralName: 'branches';
    singularName: 'branche';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    Address: Schema.Attribute.Text;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    District: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 2;
      }>;
    Filter_Location: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 2;
      }>;
    Link: Schema.Attribute.Text;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::branche.branche'
    > &
      Schema.Attribute.Private;
    Location: Schema.Attribute.String;
    Phone: Schema.Attribute.BigInteger;
    Place: Schema.Attribute.String & Schema.Attribute.Required;
    publishedAt: Schema.Attribute.DateTime;
    State: Schema.Attribute.Enumeration<
      [
        'Andhra Pradesh',
        'Arunachal Pradesh',
        'Assam',
        'Bihar',
        'Chhattisgarh',
        'Goa',
        'Gujarat',
        'Haryana',
        'Himachal Pradesh',
        'Jharkhand',
        'Karnataka',
        'Kerala',
        'Madhya Pradesh',
        'Maharashtra',
        'Manipur',
        'Meghalaya',
        'Mizoram',
        'Nagaland',
        'Odisha',
        'Punjab',
        'Rajasthan',
        'Sikkim',
        'Tamil Nadu',
        'Telangana',
        'Tripura',
        'Uttar Pradesh',
        'Uttarakhand',
        'West Bengal',
        'Pondicherry',
      ]
    > &
      Schema.Attribute.Required;
    Time: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCareerPageCareerPage extends Struct.SingleTypeSchema {
  collectionName: 'career_pages';
  info: {
    description: '';
    displayName: 'Career Page';
    pluralName: 'career-pages';
    singularName: 'career-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    Banner: Schema.Attribute.Component<'common.banner-itd', false>;
    Certified_Label_Image: Schema.Attribute.Media<'images'>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    Header: Schema.Attribute.Component<'career.header-card', false>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::career-page.career-page'
    > &
      Schema.Attribute.Private;
    Muthootees: Schema.Attribute.Component<'career.our-muthootees', false>;
    publishedAt: Schema.Attribute.DateTime;
    SEO: Schema.Attribute.Component<'common.seo', false>;
    Testimonial_Title: Schema.Attribute.String;
    TestimonialSection: Schema.Attribute.Component<'career.career-card', true>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCheckCibilScorePageCheckCibilScorePage
  extends Struct.SingleTypeSchema {
  collectionName: 'check_cibil_score_pages';
  info: {
    description: '';
    displayName: 'Check Cibil Score Page';
    pluralName: 'check-cibil-score-pages';
    singularName: 'check-cibil-score-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    Credit_Result_Card_Description: Schema.Attribute.Text;
    Declaration_Description: Schema.Attribute.Text;
    Employment_Types: Schema.Attribute.Text;
    Enter_OTP_Description: Schema.Attribute.Text;
    Gender: Schema.Attribute.Text;
    Loan_Application_Status_Card_Description: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::check-cibil-score-page.check-cibil-score-page'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    Sign_in_Description: Schema.Attribute.Text;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiClientsTestimonialClientsTestimonial
  extends Struct.CollectionTypeSchema {
  collectionName: 'clients_testimonials';
  info: {
    description: '';
    displayName: 'Clients_Testimonial';
    pluralName: 'clients-testimonials';
    singularName: 'clients-testimonial';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    Categories: Schema.Attribute.Enumeration<
      [
        'two_wheeler_loan',
        'car_loan',
        'loyalty_loan',
        'commercial_vehicle',
        'fixed_deposit',
      ]
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::clients-testimonial.clients-testimonial'
    > &
      Schema.Attribute.Private;
    Name: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    Quote: Schema.Attribute.Text;
    Region: Schema.Attribute.String;
    TestimonialOrder: Schema.Attribute.Integer;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    User_Image: Schema.Attribute.Media<'images'>;
  };
}

export interface ApiContactUsFormContactUsForm
  extends Struct.CollectionTypeSchema {
  collectionName: 'contact_us_forms';
  info: {
    displayName: 'Contact Us Form';
    pluralName: 'contact-us-forms';
    singularName: 'contact-us-form';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    Email: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::contact-us-form.contact-us-form'
    > &
      Schema.Attribute.Private;
    Message: Schema.Attribute.Text;
    Mobile_Number: Schema.Attribute.String;
    Name: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiContactUsPageContactUsPage extends Struct.SingleTypeSchema {
  collectionName: 'contact_us_pages';
  info: {
    description: '';
    displayName: 'Contact Us Page';
    pluralName: 'contact-us-pages';
    singularName: 'contact-us-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    Banner: Schema.Attribute.Component<'common.banner-itd', false>;
    Branch: Schema.Attribute.Component<'common.section-td', false>;
    Conncet_Section: Schema.Attribute.Component<'contact.connect-card', true> &
      Schema.Attribute.SetMinMax<
        {
          max: 2;
        },
        number
      >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    Enquiry: Schema.Attribute.Component<'common.card-it', false>;
    Header: Schema.Attribute.Component<'common.section-td', false>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::contact-us-page.contact-us-page'
    > &
      Schema.Attribute.Private;
    MapLink: Schema.Attribute.Text;
    Office_Address_Title: Schema.Attribute.String;
    OfficeAddress: Schema.Attribute.Component<
      'contact.contact-address-card',
      true
    >;
    publishedAt: Schema.Attribute.DateTime;
    SEO: Schema.Attribute.Component<'common.seo', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCorporateEthosPageCorporateEthosPage
  extends Struct.SingleTypeSchema {
  collectionName: 'corporate_ethos_pages';
  info: {
    description: '';
    displayName: 'Corporate Ethos Page';
    pluralName: 'corporate-ethos-pages';
    singularName: 'corporate-ethos-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    Banner: Schema.Attribute.Component<'common.banner-itd', false>;
    Corporate_Ethos_Card: Schema.Attribute.Component<
      'about-us.corporate-ethos-card',
      false
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    Fundamental_Principle_Cards: Schema.Attribute.Component<
      'about-us.principles-card',
      true
    >;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::corporate-ethos-page.corporate-ethos-page'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    SEO: Schema.Attribute.Component<'common.seo', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCsrCategoryCsrCategory extends Struct.CollectionTypeSchema {
  collectionName: 'csr_categories';
  info: {
    description: '';
    displayName: 'CSR Category';
    pluralName: 'csr-categories';
    singularName: 'csr-category';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    CSR: Schema.Attribute.Relation<'manyToOne', 'api::csr.csr'>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::csr-category.csr-category'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    Title: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCsrNewsLetterCsrNewsLetter
  extends Struct.CollectionTypeSchema {
  collectionName: 'csr_news_letters';
  info: {
    displayName: 'CSR News Letter';
    pluralName: 'csr-news-letters';
    singularName: 'csr-news-letter';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    Date: Schema.Attribute.Date;
    Email: Schema.Attribute.Email & Schema.Attribute.Required;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::csr-news-letter.csr-news-letter'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCsrPageCsrPage extends Struct.SingleTypeSchema {
  collectionName: 'csr_pages';
  info: {
    description: '';
    displayName: 'CSR Page';
    pluralName: 'csr-pages';
    singularName: 'csr-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    Banner: Schema.Attribute.Component<'common.banner-itd', false>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    Csr_Newsletter: Schema.Attribute.Component<'common.section-td', false>;
    Header: Schema.Attribute.Component<'common.header-td', false>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::csr-page.csr-page'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    SEO: Schema.Attribute.Component<'common.seo', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCsrCsr extends Struct.CollectionTypeSchema {
  collectionName: 'csrs';
  info: {
    description: '';
    displayName: 'CSR';
    pluralName: 'csrs';
    singularName: 'csr';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    Banner: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    Banner_Description: Schema.Attribute.Text;
    Banner_Title: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    CSR_categories: Schema.Attribute.Relation<
      'oneToMany',
      'api::csr-category.csr-category'
    >;
    Description: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
    Download_Description: Schema.Attribute.Text;
    Images: Schema.Attribute.Media<'images', true>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::csr.csr'> &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    Reports: Schema.Attribute.Component<'common.csr-card', true>;
    Thumbnail: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    Title: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiDigitalInitiativeCategoryDigitalInitiativeCategory
  extends Struct.CollectionTypeSchema {
  collectionName: 'digital_initiative_categories';
  info: {
    description: '';
    displayName: 'Digital Initiative Category';
    pluralName: 'digital-initiative-categories';
    singularName: 'digital-initiative-category';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    digital_initiatives: Schema.Attribute.Relation<
      'manyToMany',
      'api::digital-initiative.digital-initiative'
    >;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::digital-initiative-category.digital-initiative-category'
    > &
      Schema.Attribute.Private;
    Name: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiDigitalInitiativePageDigitalInitiativePage
  extends Struct.SingleTypeSchema {
  collectionName: 'digital_initiative_pages';
  info: {
    description: '';
    displayName: 'Digital Initiative Page';
    pluralName: 'digital-initiative-pages';
    singularName: 'digital-initiative-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    Banner: Schema.Attribute.Component<'common.banner-itd', false>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    Digital_Initiative_Categories: Schema.Attribute.Relation<
      'oneToMany',
      'api::digital-initiative.digital-initiative'
    >;
    Header: Schema.Attribute.Component<'common.header-td', false>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::digital-initiative-page.digital-initiative-page'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    SEO: Schema.Attribute.Component<'common.seo', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiDigitalInitiativeDigitalInitiative
  extends Struct.CollectionTypeSchema {
  collectionName: 'digital_initiatives';
  info: {
    description: '';
    displayName: 'Digital Initiative';
    pluralName: 'digital-initiatives';
    singularName: 'digital-initiative';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    Banner: Schema.Attribute.Media<'images'>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    Description: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
    DialogTitle: Schema.Attribute.String;
    Digital_initiative_categories: Schema.Attribute.Relation<
      'manyToMany',
      'api::digital-initiative-category.digital-initiative-category'
    >;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::digital-initiative.digital-initiative'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    Thumbnail: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    Title: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiEndorsementEndorsement extends Struct.CollectionTypeSchema {
  collectionName: 'endorsements';
  info: {
    displayName: 'Endorsement';
    pluralName: 'endorsements';
    singularName: 'endorsement';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::endorsement.endorsement'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    Testimonials: Schema.Attribute.DynamicZone<
      ['quote.quote-v', 'quote.quote-t']
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiFaqFaq extends Struct.CollectionTypeSchema {
  collectionName: 'faqs';
  info: {
    description: '';
    displayName: 'Faq';
    pluralName: 'faqs';
    singularName: 'faq';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    Answer: Schema.Attribute.Text;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::faq.faq'> &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    Question: Schema.Attribute.Text & Schema.Attribute.Required;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiFooterFooter extends Struct.SingleTypeSchema {
  collectionName: 'footers';
  info: {
    description: '';
    displayName: 'Footer';
    pluralName: 'footers';
    singularName: 'footer';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    Address: Schema.Attribute.Component<'footer.address-card', false>;
    Contact: Schema.Attribute.Component<'footer.contact-card', false>;
    CopyRight: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    Description: Schema.Attribute.Text;
    LinkSection1: Schema.Attribute.Component<'footer.link-card', false>;
    LinkSection2: Schema.Attribute.Component<'footer.link-card', false>;
    LinkSection3: Schema.Attribute.Component<'footer.link-card', false>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::footer.footer'
    > &
      Schema.Attribute.Private;
    Logo: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    publishedAt: Schema.Attribute.DateTime;
    socialMedia: Schema.Attribute.Component<'components.social-media', true>;
    StockLinks: Schema.Attribute.Component<'common.link-tl', true>;
    StockLogos: Schema.Attribute.Component<'footer.stock-logos', true> &
      Schema.Attribute.SetMinMax<
        {
          max: 2;
        },
        number
      >;
    TermsPrivacy: Schema.Attribute.Component<'common.link-tl', true> &
      Schema.Attribute.SetMinMax<
        {
          max: 2;
        },
        number
      >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiGeneralSettingGeneralSetting
  extends Struct.SingleTypeSchema {
  collectionName: 'general_settings';
  info: {
    displayName: 'General Setting';
    pluralName: 'general-settings';
    singularName: 'general-setting';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::general-setting.general-setting'
    > &
      Schema.Attribute.Private;
    Logo_Blue: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    Logo_White: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiGetInTouchFormGetInTouchForm
  extends Struct.CollectionTypeSchema {
  collectionName: 'get_in_touch_forms';
  info: {
    displayName: 'Get In Touch Form ';
    pluralName: 'get-in-touch-forms';
    singularName: 'get-in-touch-form';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    City: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    Email: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::get-in-touch-form.get-in-touch-form'
    > &
      Schema.Attribute.Private;
    Message: Schema.Attribute.Text;
    Name: Schema.Attribute.String;
    Phone: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    State: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiGroupOfCompanyGroupOfCompany
  extends Struct.SingleTypeSchema {
  collectionName: 'group_of_companies';
  info: {
    description: '';
    displayName: 'Group of companies';
    pluralName: 'group-of-companies';
    singularName: 'group-of-company';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    CompaniesBox: Schema.Attribute.Component<'components.companies-box', true>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    Header: Schema.Attribute.Component<'common.header-td', false>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::group-of-company.group-of-company'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiHeaderHeader extends Struct.SingleTypeSchema {
  collectionName: 'headers';
  info: {
    description: '';
    displayName: 'Header';
    pluralName: 'headers';
    singularName: 'header';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    Download_Button: Schema.Attribute.Component<
      'components.download-button',
      false
    >;
    Header: Schema.Attribute.Component<'common.header', true>;
    HeaderTop: Schema.Attribute.Component<'common.header-top', false>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::header.header'
    > &
      Schema.Attribute.Private;
    Logo: Schema.Attribute.Component<'header.logo', false>;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiHomePageHomePage extends Struct.SingleTypeSchema {
  collectionName: 'home_pages';
  info: {
    description: '';
    displayName: 'Home page';
    pluralName: 'home-pages';
    singularName: 'home-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    AboutUs_Section: Schema.Attribute.Component<
      'home-page.about-us-section',
      false
    >;
    Blog_Section: Schema.Attribute.Component<'home-page.blog-section', false>;
    Branches_Section: Schema.Attribute.Component<
      'home-page.branches-section',
      false
    >;
    Calculators_Section: Schema.Attribute.Component<
      'home-page.calculator-section',
      false
    >;
    Clients_Testimonials_section: Schema.Attribute.Component<
      'home-page.clients-testimonials-section',
      false
    >;
    Contact_Section: Schema.Attribute.Component<
      'home-page.contact-section',
      false
    >;
    Counter_Section: Schema.Attribute.Component<
      'home-page.counter-section',
      true
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    CSR_Section: Schema.Attribute.Component<'home-page.csr-section', false>;
    Digital_Initiative_Section: Schema.Attribute.Component<
      'home-page.digital-initiative-section',
      false
    >;
    Home_Banner: Schema.Attribute.DynamicZone<['home-page.home-banners']>;
    Loan_Redirector: Schema.Attribute.Component<'common.itl', true>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::home-page.home-page'
    > &
      Schema.Attribute.Private;
    Product_Section: Schema.Attribute.Component<
      'home-page.products-sections',
      false
    >;
    publishedAt: Schema.Attribute.DateTime;
    SEO: Schema.Attribute.Component<'common.seo', false>;
    Showcase_Brand_Section: Schema.Attribute.Component<
      'home-page.showcase-brand-section',
      false
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiInMemoriamPageInMemoriamPage
  extends Struct.SingleTypeSchema {
  collectionName: 'in_memoriam_pages';
  info: {
    description: '';
    displayName: 'In Memoriam Page';
    pluralName: 'in-memoriam-pages';
    singularName: 'in-memoriam-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    Folks: Schema.Attribute.Component<'common.memorial-card', true>;
    Header: Schema.Attribute.Component<'common.header-td-colored', false>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::in-memoriam-page.in-memoriam-page'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    SEO: Schema.Attribute.Component<'common.seo', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiInverstorsListingInverstorsListing
  extends Struct.CollectionTypeSchema {
  collectionName: 'inverstors_listings';
  info: {
    description: '';
    displayName: 'Inverstors Listing';
    pluralName: 'inverstors-listings';
    singularName: 'inverstors-listing';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    Attachments: Schema.Attribute.Component<'common.download-card', true>;
    Banner_slug: Schema.Attribute.Component<'common.banner-itd', false>;
    Breadcrumb_Name: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    Header: Schema.Attribute.Component<'common.section-td', false>;
    investors_section_title: Schema.Attribute.Relation<
      'manyToOne',
      'api::investors-section-title.investors-section-title'
    >;
    InvestorsOrder: Schema.Attribute.Integer;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::inverstors-listing.inverstors-listing'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    slug: Schema.Attribute.UID<'Title'> &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    Slug_Name: Schema.Attribute.String;
    Title: Schema.Attribute.String & Schema.Attribute.Required;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiInverstorsPageInverstorsPage
  extends Struct.SingleTypeSchema {
  collectionName: 'inverstors_pages';
  info: {
    description: '';
    displayName: 'Inverstors Page';
    pluralName: 'inverstors-pages';
    singularName: 'inverstors-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    Banner: Schema.Attribute.Component<'common.banner-itd', false>;
    Banner_slug: Schema.Attribute.Component<'common.banner-itd', false>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    Download_Button_Icon: Schema.Attribute.Media<'images'> &
      Schema.Attribute.Required;
    Download_Button_Text: Schema.Attribute.String & Schema.Attribute.Required;
    Download_Pdf_Icon: Schema.Attribute.Media<'images'> &
      Schema.Attribute.Required;
    Header: Schema.Attribute.Component<'common.header-td-colored', false>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::inverstors-page.inverstors-page'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    SEO: Schema.Attribute.Component<'common.seo', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiInvestorsSectionTitleInvestorsSectionTitle
  extends Struct.CollectionTypeSchema {
  collectionName: 'investors_section_titles';
  info: {
    description: '';
    displayName: 'Investors Section Title';
    pluralName: 'investors-section-titles';
    singularName: 'investors-section-title';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    inverstors_listings: Schema.Attribute.Relation<
      'oneToMany',
      'api::inverstors-listing.inverstors-listing'
    >;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::investors-section-title.investors-section-title'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    SectionTitle: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiInvestorsTabInvestorsTab
  extends Struct.CollectionTypeSchema {
  collectionName: 'investors_tabs';
  info: {
    displayName: 'Investors Tab';
    pluralName: 'investors-tabs';
    singularName: 'investors-tab';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::investors-tab.investors-tab'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    Tab_Name: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiLeadershipPageLeadershipPage
  extends Struct.SingleTypeSchema {
  collectionName: 'leadership_pages';
  info: {
    description: '';
    displayName: 'Leadership Page';
    pluralName: 'leadership-pages';
    singularName: 'leadership-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    Banner: Schema.Attribute.Component<'common.banner-itd', false>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    Header: Schema.Attribute.Component<'common.section-td', false>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::leadership-page.leadership-page'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    SEO: Schema.Attribute.Component<'common.seo', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiLeadershipTeamLeadershipTeam
  extends Struct.CollectionTypeSchema {
  collectionName: 'leadership_teams';
  info: {
    description: '';
    displayName: 'Leadership Team';
    pluralName: 'leadership-teams';
    singularName: 'leadership-team';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    Description: Schema.Attribute.Text;
    Designation: Schema.Attribute.String & Schema.Attribute.Required;
    Image: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    Leader_Order: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::leadership-team.leadership-team'
    > &
      Schema.Attribute.Private;
    Name: Schema.Attribute.String & Schema.Attribute.Required;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiMediaPageMediaPage extends Struct.SingleTypeSchema {
  collectionName: 'media_pages';
  info: {
    description: '';
    displayName: 'Media Page';
    pluralName: 'media-pages';
    singularName: 'media-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    Award_Section: Schema.Attribute.Component<
      'components.media-list-card',
      false
    >;
    Banner: Schema.Attribute.Component<'common.banner-itd', false>;
    Blog_Section: Schema.Attribute.Component<
      'components.media-list-card',
      false
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::media-page.media-page'
    > &
      Schema.Attribute.Private;
    News_Section: Schema.Attribute.Component<
      'components.media-list-card',
      false
    >;
    publishedAt: Schema.Attribute.DateTime;
    SEO: Schema.Attribute.Component<'common.seo', false>;
    Testimonial_Section: Schema.Attribute.Component<
      'components.media-list-card',
      false
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiMilestonePageMilestonePage extends Struct.SingleTypeSchema {
  collectionName: 'milestone_pages';
  info: {
    description: '';
    displayName: 'Milestone Page';
    pluralName: 'milestone-pages';
    singularName: 'milestone-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    Banner: Schema.Attribute.Component<'common.banner-itd', false>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    Header: Schema.Attribute.Component<'common.header-td-colored', false>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::milestone-page.milestone-page'
    > &
      Schema.Attribute.Private;
    Milstone: Schema.Attribute.Component<'about-us.milstone-card', true> &
      Schema.Attribute.SetMinMax<
        {
          min: 9;
        },
        number
      >;
    publishedAt: Schema.Attribute.DateTime;
    SEO: Schema.Attribute.Component<'common.seo', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiNewsAndEventsCategoryNewsAndEventsCategory
  extends Struct.CollectionTypeSchema {
  collectionName: 'news_and_events_categories';
  info: {
    description: '';
    displayName: 'News & Events Category';
    pluralName: 'news-and-events-categories';
    singularName: 'news-and-events-category';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::news-and-events-category.news-and-events-category'
    > &
      Schema.Attribute.Private;
    News_and_Event: Schema.Attribute.Relation<
      'manyToOne',
      'api::news-event.news-event'
    >;
    publishedAt: Schema.Attribute.DateTime;
    Title: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiNewsAndEventsPageNewsAndEventsPage
  extends Struct.SingleTypeSchema {
  collectionName: 'news_and_events_pages';
  info: {
    description: '';
    displayName: 'News & Events Page';
    pluralName: 'news-and-events-pages';
    singularName: 'news-and-events-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    Banner: Schema.Attribute.Component<'common.banner-itd', false>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    Header: Schema.Attribute.Component<'common.header-td', false>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::news-and-events-page.news-and-events-page'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    SEO: Schema.Attribute.Component<'common.seo', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiNewsEventNewsEvent extends Struct.CollectionTypeSchema {
  collectionName: 'news_events';
  info: {
    description: '';
    displayName: 'News & Event';
    pluralName: 'news-events';
    singularName: 'news-event';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    Banner: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    Date: Schema.Attribute.Date;
    Description: Schema.Attribute.Blocks;
    DescriptionTitle: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::news-event.news-event'
    > &
      Schema.Attribute.Private;
    News_and_Events_categories: Schema.Attribute.Relation<
      'oneToMany',
      'api::news-and-events-category.news-and-events-category'
    >;
    NewsOrder: Schema.Attribute.Integer;
    publishedAt: Schema.Attribute.DateTime;
    Tags: Schema.Attribute.JSON &
      Schema.Attribute.CustomField<'plugin::tagsinput.tags'>;
    Thumbnail: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    Title: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiNextGenerationPageNextGenerationPage
  extends Struct.SingleTypeSchema {
  collectionName: 'next_generation_pages';
  info: {
    description: '';
    displayName: 'Our Directors';
    pluralName: 'next-generation-pages';
    singularName: 'next-generation-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    Banner: Schema.Attribute.Component<'common.banner-itd', false>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    Header: Schema.Attribute.Component<'common.header-td-colored', false>;
    Header2: Schema.Attribute.Component<'common.section-td', false>;
    Independent_Directors_Card: Schema.Attribute.Component<
      'independent-directors-card.independent-directors-card',
      true
    >;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::next-generation-page.next-generation-page'
    > &
      Schema.Attribute.Private;
    Nextgen_Card: Schema.Attribute.Component<
      'about-us.generation-prople-card',
      true
    >;
    publishedAt: Schema.Attribute.DateTime;
    SEO: Schema.Attribute.Component<'common.seo', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiNoResultPageNoResultPage extends Struct.SingleTypeSchema {
  collectionName: 'no_result_pages';
  info: {
    displayName: 'No Result Page';
    pluralName: 'no-result-pages';
    singularName: 'no-result-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::no-result-page.no-result-page'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    Section: Schema.Attribute.Component<'common.section-its', false>;
    SEO: Schema.Attribute.Component<'common.seo', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiNotFoundPageNotFoundPage extends Struct.SingleTypeSchema {
  collectionName: 'not_found_pages';
  info: {
    displayName: 'Not Found Page';
    pluralName: 'not-found-pages';
    singularName: 'not-found-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::not-found-page.not-found-page'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    Section: Schema.Attribute.Component<'common.section-its', false>;
    SEO: Schema.Attribute.Component<'common.seo', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiOurDirectorPageOurDirectorPage
  extends Struct.SingleTypeSchema {
  collectionName: 'our_director_pages';
  info: {
    description: '';
    displayName: 'Our Promoters';
    pluralName: 'our-director-pages';
    singularName: 'our-director-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    Banner: Schema.Attribute.Component<'common.banner-itd', false>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    Directors: Schema.Attribute.Component<'about-us.director-card', true>;
    Directors_Section: Schema.Attribute.Component<
      'common.header-td-colored',
      false
    >;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::our-director-page.our-director-page'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    SEO: Schema.Attribute.Component<'common.seo', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiOurHistoryPageOurHistoryPage
  extends Struct.SingleTypeSchema {
  collectionName: 'our_history_pages';
  info: {
    description: '';
    displayName: 'Our History Page';
    pluralName: 'our-history-pages';
    singularName: 'our-history-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    Banner: Schema.Attribute.Component<'common.banner-itd', false>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    Header: Schema.Attribute.Component<'common.header-td-colored', false>;
    Images: Schema.Attribute.Component<'template.image', true>;
    LandScapeImages: Schema.Attribute.Component<
      'common.land-scape-images',
      true
    >;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::our-history-page.our-history-page'
    > &
      Schema.Attribute.Private;
    Pillars_Section: Schema.Attribute.Component<'about-us.pillars-card', false>;
    publishedAt: Schema.Attribute.DateTime;
    SEO: Schema.Attribute.Component<'common.seo', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiPoliciesPagePoliciesPage extends Struct.SingleTypeSchema {
  collectionName: 'policies_pages';
  info: {
    description: '';
    displayName: 'Policies Page';
    pluralName: 'policies-pages';
    singularName: 'policies-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    Banner: Schema.Attribute.Component<'common.banner-itd', false>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    Header: Schema.Attribute.Component<'common.section-td', false>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::policies-page.policies-page'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    SEO: Schema.Attribute.Component<'common.seo', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiPolicyPolicy extends Struct.CollectionTypeSchema {
  collectionName: 'policies';
  info: {
    description: '';
    displayName: 'Policy';
    pluralName: 'policies';
    singularName: 'policy';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    Description: Schema.Attribute.Text;
    Detail_Title: Schema.Attribute.String;
    Downloads: Schema.Attribute.Component<'common.card-tdf', true>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::policy.policy'
    > &
      Schema.Attribute.Private;
    PolicyOrder: Schema.Attribute.Integer;
    publishedAt: Schema.Attribute.DateTime;
    slug: Schema.Attribute.UID<'Title'> &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    Tabs: Schema.Attribute.DynamicZone<['components.card-trichd']>;
    Title: Schema.Attribute.String & Schema.Attribute.Required;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiPostPost extends Struct.SingleTypeSchema {
  collectionName: 'posts';
  info: {
    displayName: 'post';
    pluralName: 'posts';
    singularName: 'post';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::post.post'> &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    slug: Schema.Attribute.UID<'title'> &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    title: Schema.Attribute.String & Schema.Attribute.Required;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiPrivacyPolicyPagePrivacyPolicyPage
  extends Struct.SingleTypeSchema {
  collectionName: 'privacy_policy_pages';
  info: {
    description: '';
    displayName: 'Privacy Policy Page';
    pluralName: 'privacy-policy-pages';
    singularName: 'privacy-policy-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    Banner: Schema.Attribute.Component<'common.banner-itd', false>;
    Content: Schema.Attribute.Blocks;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    Header: Schema.Attribute.Component<'common.section-td', true>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::privacy-policy-page.privacy-policy-page'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    SEO: Schema.Attribute.Component<'common.seo', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiProductDetailsPageProductDetailsPage
  extends Struct.SingleTypeSchema {
  collectionName: 'product_details_pages';
  info: {
    displayName: 'Product Details Page';
    pluralName: 'product-details-pages';
    singularName: 'product-details-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-details-page.product-details-page'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    Title: Schema.Attribute.Text;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiProductListingPageProductListingPage
  extends Struct.SingleTypeSchema {
  collectionName: 'product_listing_pages';
  info: {
    description: '';
    displayName: 'Product Listing Page';
    pluralName: 'product-listing-pages';
    singularName: 'product-listing-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    Banner: Schema.Attribute.Component<'common.banner-itd', false>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-listing-page.product-listing-page'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    SEO: Schema.Attribute.Component<'common.seo', false>;
    Title: Schema.Attribute.String & Schema.Attribute.Required;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiProductProduct extends Struct.CollectionTypeSchema {
  collectionName: 'products';
  info: {
    description: '';
    displayName: 'Product';
    pluralName: 'products';
    singularName: 'product';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    Breadcrumb_Name: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product.product'
    > &
      Schema.Attribute.Private;
    Product_Details: Schema.Attribute.Component<
      'product.product-details',
      false
    >;
    Product_Order: Schema.Attribute.Integer;
    publishedAt: Schema.Attribute.DateTime;
    Short_Description: Schema.Attribute.Text;
    slug: Schema.Attribute.UID<'Title'> &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    Thumbnail: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    Title: Schema.Attribute.String & Schema.Attribute.Required;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiSecuredChargesPageSecuredChargesPage
  extends Struct.SingleTypeSchema {
  collectionName: 'secured_charges_pages';
  info: {
    description: '';
    displayName: 'Secured Charges Page';
    pluralName: 'secured-charges-pages';
    singularName: 'secured-charges-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    Banner: Schema.Attribute.Component<'common.banner-itd', false>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::secured-charges-page.secured-charges-page'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    SEO: Schema.Attribute.Component<'common.seo', false>;
    Table: Schema.Attribute.Component<'product.deposit-table', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiServerErrorPageServerErrorPage
  extends Struct.SingleTypeSchema {
  collectionName: 'server_error_pages';
  info: {
    displayName: 'Server Error Page';
    pluralName: 'server-error-pages';
    singularName: 'server-error-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::server-error-page.server-error-page'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    Section: Schema.Attribute.Component<'common.section-its', false>;
    SEO: Schema.Attribute.Component<'common.seo', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiServicePageServicePage extends Struct.SingleTypeSchema {
  collectionName: 'service_pages';
  info: {
    displayName: 'Service Page';
    pluralName: 'service-pages';
    singularName: 'service-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    Banner: Schema.Attribute.Component<'common.banner-itd', false>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::service-page.service-page'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    SEO: Schema.Attribute.Component<'common.seo', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiServiceService extends Struct.CollectionTypeSchema {
  collectionName: 'services';
  info: {
    displayName: 'Service';
    pluralName: 'services';
    singularName: 'service';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::service.service'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    Title: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiSidebarSidebar extends Struct.CollectionTypeSchema {
  collectionName: 'sidebars';
  info: {
    description: '';
    displayName: 'Sidebar';
    pluralName: 'sidebars';
    singularName: 'sidebar';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    Icon: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    Link: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::sidebar.sidebar'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    SideBar_Order: Schema.Attribute.Integer;
    Title: Schema.Attribute.String & Schema.Attribute.Required;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiTermsAndConditionPageTermsAndConditionPage
  extends Struct.SingleTypeSchema {
  collectionName: 'terms_and_condition_pages';
  info: {
    description: '';
    displayName: 'Terms & Condition Page';
    pluralName: 'terms-and-condition-pages';
    singularName: 'terms-and-condition-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    Banner: Schema.Attribute.Component<'common.banner-itd', false>;
    Content: Schema.Attribute.Blocks;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    Header: Schema.Attribute.Component<'common.section-td', false>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::terms-and-condition-page.terms-and-condition-page'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    SEO: Schema.Attribute.Component<'common.seo', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiTestimonialTestimonial extends Struct.SingleTypeSchema {
  collectionName: 'testimonials';
  info: {
    description: '';
    displayName: 'Testimonial Page';
    pluralName: 'testimonials';
    singularName: 'testimonial';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    Banner: Schema.Attribute.Component<'common.banner-itd', false>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    Header: Schema.Attribute.Component<'common.header-td-colored', false>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::testimonial.testimonial'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    SEO: Schema.Attribute.Component<'common.seo', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiTestimonialsListingTestimonialsListing
  extends Struct.CollectionTypeSchema {
  collectionName: 'testimonials_listings';
  info: {
    displayName: 'Testimonials Listing';
    pluralName: 'testimonials-listings';
    singularName: 'testimonials-listing';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    Designation: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::testimonials-listing.testimonials-listing'
    > &
      Schema.Attribute.Private;
    Name: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    Quote: Schema.Attribute.Text;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    User_Image: Schema.Attribute.Media<'images'>;
  };
}

export interface PluginContentReleasesRelease
  extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_releases';
  info: {
    displayName: 'Release';
    pluralName: 'releases';
    singularName: 'release';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    actions: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::content-releases.release-action'
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::content-releases.release'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String & Schema.Attribute.Required;
    publishedAt: Schema.Attribute.DateTime;
    releasedAt: Schema.Attribute.DateTime;
    scheduledAt: Schema.Attribute.DateTime;
    status: Schema.Attribute.Enumeration<
      ['ready', 'blocked', 'failed', 'done', 'empty']
    > &
      Schema.Attribute.Required;
    timezone: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface PluginContentReleasesReleaseAction
  extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_release_actions';
  info: {
    displayName: 'Release Action';
    pluralName: 'release-actions';
    singularName: 'release-action';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    contentType: Schema.Attribute.String & Schema.Attribute.Required;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    entryDocumentId: Schema.Attribute.String;
    isEntryValid: Schema.Attribute.Boolean;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::content-releases.release-action'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    release: Schema.Attribute.Relation<
      'manyToOne',
      'plugin::content-releases.release'
    >;
    type: Schema.Attribute.Enumeration<['publish', 'unpublish']> &
      Schema.Attribute.Required;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface PluginI18NLocale extends Struct.CollectionTypeSchema {
  collectionName: 'i18n_locale';
  info: {
    collectionName: 'locales';
    description: '';
    displayName: 'Locale';
    pluralName: 'locales';
    singularName: 'locale';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    code: Schema.Attribute.String & Schema.Attribute.Unique;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::i18n.locale'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.SetMinMax<
        {
          max: 50;
          min: 1;
        },
        number
      >;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface PluginReviewWorkflowsWorkflow
  extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_workflows';
  info: {
    description: '';
    displayName: 'Workflow';
    name: 'Workflow';
    pluralName: 'workflows';
    singularName: 'workflow';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    contentTypes: Schema.Attribute.JSON &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'[]'>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::review-workflows.workflow'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    publishedAt: Schema.Attribute.DateTime;
    stageRequiredToPublish: Schema.Attribute.Relation<
      'oneToOne',
      'plugin::review-workflows.workflow-stage'
    >;
    stages: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::review-workflows.workflow-stage'
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface PluginReviewWorkflowsWorkflowStage
  extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_workflows_stages';
  info: {
    description: '';
    displayName: 'Stages';
    name: 'Workflow Stage';
    pluralName: 'workflow-stages';
    singularName: 'workflow-stage';
  };
  options: {
    draftAndPublish: false;
    version: '1.1.0';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    color: Schema.Attribute.String & Schema.Attribute.DefaultTo<'#4945FF'>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::review-workflows.workflow-stage'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String;
    permissions: Schema.Attribute.Relation<'manyToMany', 'admin::permission'>;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    workflow: Schema.Attribute.Relation<
      'manyToOne',
      'plugin::review-workflows.workflow'
    >;
  };
}

export interface PluginUploadFile extends Struct.CollectionTypeSchema {
  collectionName: 'files';
  info: {
    description: '';
    displayName: 'File';
    pluralName: 'files';
    singularName: 'file';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    alternativeText: Schema.Attribute.String;
    caption: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    ext: Schema.Attribute.String;
    folder: Schema.Attribute.Relation<'manyToOne', 'plugin::upload.folder'> &
      Schema.Attribute.Private;
    folderPath: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Private &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    formats: Schema.Attribute.JSON;
    hash: Schema.Attribute.String & Schema.Attribute.Required;
    height: Schema.Attribute.Integer;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::upload.file'
    > &
      Schema.Attribute.Private;
    mime: Schema.Attribute.String & Schema.Attribute.Required;
    name: Schema.Attribute.String & Schema.Attribute.Required;
    previewUrl: Schema.Attribute.String;
    provider: Schema.Attribute.String & Schema.Attribute.Required;
    provider_metadata: Schema.Attribute.JSON;
    publishedAt: Schema.Attribute.DateTime;
    related: Schema.Attribute.Relation<'morphToMany'>;
    size: Schema.Attribute.Decimal & Schema.Attribute.Required;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    url: Schema.Attribute.String & Schema.Attribute.Required;
    width: Schema.Attribute.Integer;
  };
}

export interface PluginUploadFolder extends Struct.CollectionTypeSchema {
  collectionName: 'upload_folders';
  info: {
    displayName: 'Folder';
    pluralName: 'folders';
    singularName: 'folder';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    children: Schema.Attribute.Relation<'oneToMany', 'plugin::upload.folder'>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    files: Schema.Attribute.Relation<'oneToMany', 'plugin::upload.file'>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::upload.folder'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    parent: Schema.Attribute.Relation<'manyToOne', 'plugin::upload.folder'>;
    path: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    pathId: Schema.Attribute.Integer &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface PluginUsersPermissionsPermission
  extends Struct.CollectionTypeSchema {
  collectionName: 'up_permissions';
  info: {
    description: '';
    displayName: 'Permission';
    name: 'permission';
    pluralName: 'permissions';
    singularName: 'permission';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Schema.Attribute.String & Schema.Attribute.Required;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::users-permissions.permission'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    role: Schema.Attribute.Relation<
      'manyToOne',
      'plugin::users-permissions.role'
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface PluginUsersPermissionsRole
  extends Struct.CollectionTypeSchema {
  collectionName: 'up_roles';
  info: {
    description: '';
    displayName: 'Role';
    name: 'role';
    pluralName: 'roles';
    singularName: 'role';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    description: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::users-permissions.role'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 3;
      }>;
    permissions: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::users-permissions.permission'
    >;
    publishedAt: Schema.Attribute.DateTime;
    type: Schema.Attribute.String & Schema.Attribute.Unique;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    users: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::users-permissions.user'
    >;
  };
}

export interface PluginUsersPermissionsUser
  extends Struct.CollectionTypeSchema {
  collectionName: 'up_users';
  info: {
    description: '';
    displayName: 'User';
    name: 'user';
    pluralName: 'users';
    singularName: 'user';
  };
  options: {
    draftAndPublish: false;
    timestamps: true;
  };
  attributes: {
    blocked: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
    confirmationToken: Schema.Attribute.String & Schema.Attribute.Private;
    confirmed: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    email: Schema.Attribute.Email &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::users-permissions.user'
    > &
      Schema.Attribute.Private;
    password: Schema.Attribute.Password &
      Schema.Attribute.Private &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    provider: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    resetPasswordToken: Schema.Attribute.String & Schema.Attribute.Private;
    role: Schema.Attribute.Relation<
      'manyToOne',
      'plugin::users-permissions.role'
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    username: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 3;
      }>;
  };
}

declare module '@strapi/strapi' {
  export module Public {
    export interface ContentTypeSchemas {
      'admin::api-token': AdminApiToken;
      'admin::api-token-permission': AdminApiTokenPermission;
      'admin::permission': AdminPermission;
      'admin::role': AdminRole;
      'admin::transfer-token': AdminTransferToken;
      'admin::transfer-token-permission': AdminTransferTokenPermission;
      'admin::user': AdminUser;
      'api::about-us-page.about-us-page': ApiAboutUsPageAboutUsPage;
      'api::award.award': ApiAwardAward;
      'api::awards-and-recognition-page.awards-and-recognition-page': ApiAwardsAndRecognitionPageAwardsAndRecognitionPage;
      'api::blog-category.blog-category': ApiBlogCategoryBlogCategory;
      'api::blog-page.blog-page': ApiBlogPageBlogPage;
      'api::blog.blog': ApiBlogBlog;
      'api::branch-page.branch-page': ApiBranchPageBranchPage;
      'api::branche.branche': ApiBrancheBranche;
      'api::career-page.career-page': ApiCareerPageCareerPage;
      'api::check-cibil-score-page.check-cibil-score-page': ApiCheckCibilScorePageCheckCibilScorePage;
      'api::clients-testimonial.clients-testimonial': ApiClientsTestimonialClientsTestimonial;
      'api::contact-us-form.contact-us-form': ApiContactUsFormContactUsForm;
      'api::contact-us-page.contact-us-page': ApiContactUsPageContactUsPage;
      'api::corporate-ethos-page.corporate-ethos-page': ApiCorporateEthosPageCorporateEthosPage;
      'api::csr-category.csr-category': ApiCsrCategoryCsrCategory;
      'api::csr-news-letter.csr-news-letter': ApiCsrNewsLetterCsrNewsLetter;
      'api::csr-page.csr-page': ApiCsrPageCsrPage;
      'api::csr.csr': ApiCsrCsr;
      'api::digital-initiative-category.digital-initiative-category': ApiDigitalInitiativeCategoryDigitalInitiativeCategory;
      'api::digital-initiative-page.digital-initiative-page': ApiDigitalInitiativePageDigitalInitiativePage;
      'api::digital-initiative.digital-initiative': ApiDigitalInitiativeDigitalInitiative;
      'api::endorsement.endorsement': ApiEndorsementEndorsement;
      'api::faq.faq': ApiFaqFaq;
      'api::footer.footer': ApiFooterFooter;
      'api::general-setting.general-setting': ApiGeneralSettingGeneralSetting;
      'api::get-in-touch-form.get-in-touch-form': ApiGetInTouchFormGetInTouchForm;
      'api::group-of-company.group-of-company': ApiGroupOfCompanyGroupOfCompany;
      'api::header.header': ApiHeaderHeader;
      'api::home-page.home-page': ApiHomePageHomePage;
      'api::in-memoriam-page.in-memoriam-page': ApiInMemoriamPageInMemoriamPage;
      'api::inverstors-listing.inverstors-listing': ApiInverstorsListingInverstorsListing;
      'api::inverstors-page.inverstors-page': ApiInverstorsPageInverstorsPage;
      'api::investors-section-title.investors-section-title': ApiInvestorsSectionTitleInvestorsSectionTitle;
      'api::investors-tab.investors-tab': ApiInvestorsTabInvestorsTab;
      'api::leadership-page.leadership-page': ApiLeadershipPageLeadershipPage;
      'api::leadership-team.leadership-team': ApiLeadershipTeamLeadershipTeam;
      'api::media-page.media-page': ApiMediaPageMediaPage;
      'api::milestone-page.milestone-page': ApiMilestonePageMilestonePage;
      'api::news-and-events-category.news-and-events-category': ApiNewsAndEventsCategoryNewsAndEventsCategory;
      'api::news-and-events-page.news-and-events-page': ApiNewsAndEventsPageNewsAndEventsPage;
      'api::news-event.news-event': ApiNewsEventNewsEvent;
      'api::next-generation-page.next-generation-page': ApiNextGenerationPageNextGenerationPage;
      'api::no-result-page.no-result-page': ApiNoResultPageNoResultPage;
      'api::not-found-page.not-found-page': ApiNotFoundPageNotFoundPage;
      'api::our-director-page.our-director-page': ApiOurDirectorPageOurDirectorPage;
      'api::our-history-page.our-history-page': ApiOurHistoryPageOurHistoryPage;
      'api::policies-page.policies-page': ApiPoliciesPagePoliciesPage;
      'api::policy.policy': ApiPolicyPolicy;
      'api::post.post': ApiPostPost;
      'api::privacy-policy-page.privacy-policy-page': ApiPrivacyPolicyPagePrivacyPolicyPage;
      'api::product-details-page.product-details-page': ApiProductDetailsPageProductDetailsPage;
      'api::product-listing-page.product-listing-page': ApiProductListingPageProductListingPage;
      'api::product.product': ApiProductProduct;
      'api::secured-charges-page.secured-charges-page': ApiSecuredChargesPageSecuredChargesPage;
      'api::server-error-page.server-error-page': ApiServerErrorPageServerErrorPage;
      'api::service-page.service-page': ApiServicePageServicePage;
      'api::service.service': ApiServiceService;
      'api::sidebar.sidebar': ApiSidebarSidebar;
      'api::terms-and-condition-page.terms-and-condition-page': ApiTermsAndConditionPageTermsAndConditionPage;
      'api::testimonial.testimonial': ApiTestimonialTestimonial;
      'api::testimonials-listing.testimonials-listing': ApiTestimonialsListingTestimonialsListing;
      'plugin::content-releases.release': PluginContentReleasesRelease;
      'plugin::content-releases.release-action': PluginContentReleasesReleaseAction;
      'plugin::i18n.locale': PluginI18NLocale;
      'plugin::review-workflows.workflow': PluginReviewWorkflowsWorkflow;
      'plugin::review-workflows.workflow-stage': PluginReviewWorkflowsWorkflowStage;
      'plugin::upload.file': PluginUploadFile;
      'plugin::upload.folder': PluginUploadFolder;
      'plugin::users-permissions.permission': PluginUsersPermissionsPermission;
      'plugin::users-permissions.role': PluginUsersPermissionsRole;
      'plugin::users-permissions.user': PluginUsersPermissionsUser;
    }
  }
}
