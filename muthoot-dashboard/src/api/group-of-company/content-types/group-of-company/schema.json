{"kind": "singleType", "collectionName": "group_of_companies", "info": {"singularName": "group-of-company", "pluralName": "group-of-companies", "displayName": "Group of companies", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"Header": {"type": "component", "repeatable": false, "component": "common.header-td"}, "CompaniesBox": {"displayName": "CompaniesBox", "type": "component", "repeatable": true, "component": "components.companies-box"}}}