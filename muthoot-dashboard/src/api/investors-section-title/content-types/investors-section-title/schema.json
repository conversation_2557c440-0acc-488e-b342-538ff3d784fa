{"kind": "collectionType", "collectionName": "investors_section_titles", "info": {"singularName": "investors-section-title", "pluralName": "investors-section-titles", "displayName": "Investors Section Title", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"SectionTitle": {"type": "string"}, "inverstors_listings": {"type": "relation", "relation": "oneToMany", "target": "api::inverstors-listing.inverstors-listing", "mappedBy": "investors_section_title"}}}