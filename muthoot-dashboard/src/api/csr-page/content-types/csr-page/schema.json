{"kind": "singleType", "collectionName": "csr_pages", "info": {"singularName": "csr-page", "pluralName": "csr-pages", "displayName": "CSR Page", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"SEO": {"type": "component", "repeatable": false, "component": "common.seo"}, "Banner": {"type": "component", "repeatable": false, "component": "common.banner-itd"}, "Header": {"type": "component", "repeatable": false, "component": "common.header-td"}, "Csr_Newsletter": {"type": "component", "repeatable": false, "component": "common.section-td"}}}