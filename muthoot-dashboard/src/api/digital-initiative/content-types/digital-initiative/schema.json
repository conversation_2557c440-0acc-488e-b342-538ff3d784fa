{"kind": "collectionType", "collectionName": "digital_initiatives", "info": {"singularName": "digital-initiative", "pluralName": "digital-initiatives", "displayName": "Digital Initiative", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"Title": {"type": "string"}, "Thumbnail": {"type": "media", "multiple": false, "required": true, "allowedTypes": ["images"]}, "Digital_initiative_categories": {"type": "relation", "relation": "manyToMany", "target": "api::digital-initiative-category.digital-initiative-category", "mappedBy": "digital_initiatives"}, "Banner": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}, "Description": {"type": "customField", "options": {"preset": "defaultHtml"}, "customField": "plugin::ckeditor5.CKEditor"}, "DialogTitle": {"type": "string"}}}