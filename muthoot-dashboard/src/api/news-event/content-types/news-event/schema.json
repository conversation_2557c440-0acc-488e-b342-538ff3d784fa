{"kind": "collectionType", "collectionName": "news_events", "info": {"singularName": "news-event", "pluralName": "news-events", "displayName": "News & Event", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"Title": {"type": "string"}, "Thumbnail": {"type": "media", "multiple": false, "required": true, "allowedTypes": ["images"]}, "Banner": {"type": "media", "multiple": false, "required": true, "allowedTypes": ["images"]}, "Description": {"type": "blocks"}, "Date": {"type": "date"}, "Tags": {"type": "customField", "customField": "plugin::tagsinput.tags"}, "News_and_Events_categories": {"type": "relation", "relation": "oneToMany", "target": "api::news-and-events-category.news-and-events-category", "mappedBy": "News_and_Event"}, "DescriptionTitle": {"type": "string"}, "NewsOrder": {"type": "integer"}}}