{"kind": "singleType", "collectionName": "footers", "info": {"singularName": "footer", "pluralName": "footers", "displayName": "Footer", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"Logo": {"type": "media", "multiple": false, "required": true, "allowedTypes": ["images"]}, "Description": {"type": "text"}, "socialMedia": {"displayName": "Social Media", "type": "component", "repeatable": true, "component": "components.social-media"}, "StockLogos": {"type": "component", "repeatable": true, "component": "footer.stock-logos", "max": 2}, "StockLinks": {"type": "component", "repeatable": true, "component": "common.link-tl"}, "LinkSection1": {"type": "component", "repeatable": false, "component": "footer.link-card"}, "LinkSection2": {"type": "component", "repeatable": false, "component": "footer.link-card"}, "LinkSection3": {"type": "component", "repeatable": false, "component": "footer.link-card"}, "Address": {"type": "component", "repeatable": false, "component": "footer.address-card"}, "Contact": {"type": "component", "repeatable": false, "component": "footer.contact-card"}, "CopyRight": {"type": "string"}, "TermsPrivacy": {"type": "component", "repeatable": true, "component": "common.link-tl", "max": 2}}}