{"kind": "singleType", "collectionName": "headers", "info": {"singularName": "header", "pluralName": "headers", "displayName": "Header", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"Download_Button": {"type": "component", "repeatable": false, "component": "components.download-button"}, "Header": {"displayName": "Header", "type": "component", "repeatable": true, "component": "common.header"}, "Logo": {"displayName": "Logo", "type": "component", "repeatable": false, "component": "header.logo"}, "HeaderTop": {"displayName": "HeaderTop", "type": "component", "repeatable": false, "component": "common.header-top"}}}