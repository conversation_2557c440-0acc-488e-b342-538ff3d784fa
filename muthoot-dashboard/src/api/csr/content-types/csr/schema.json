{"kind": "collectionType", "collectionName": "csrs", "info": {"singularName": "csr", "pluralName": "csrs", "displayName": "CSR", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"Title": {"type": "string"}, "Thumbnail": {"type": "media", "multiple": false, "required": true, "allowedTypes": ["images"]}, "Banner": {"type": "media", "multiple": false, "required": true, "allowedTypes": ["images"]}, "Images": {"type": "media", "multiple": true, "required": false, "allowedTypes": ["images"]}, "Download_Description": {"type": "text"}, "Reports": {"type": "component", "repeatable": true, "component": "common.csr-card"}, "CSR_categories": {"type": "relation", "relation": "oneToMany", "target": "api::csr-category.csr-category", "mappedBy": "CSR"}, "Banner_Title": {"type": "string"}, "Banner_Description": {"type": "text"}, "Description": {"type": "customField", "options": {"preset": "defaultHtml"}, "customField": "plugin::ckeditor5.CKEditor"}}}