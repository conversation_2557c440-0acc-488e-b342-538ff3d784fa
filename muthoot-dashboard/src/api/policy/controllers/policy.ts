/**
 * policy controller
 */

import { factories } from "@strapi/strapi";

export default factories.createCoreController(
  "api::policy.policy",
  ({ strapi }) => ({
    async find(ctx) {
      await this.validateQuery(ctx);
      const sanitizedQueryParams = await this.sanitizeQuery(ctx);
      const { results, pagination } = await strapi
        .service("api::policy.policy")
        .find(sanitizedQueryParams);
      const sanitizedResults = await this.sanitizeOutput(results, ctx);
      return this.transformResponse(sanitizedResults, { pagination });
    },

    async findBySlug(ctx) {
      const { slug } = ctx.params;
      let populate = [""];

      if (ctx.query.populate) {
        populate = Array.isArray(ctx.query.populate)
          ? ctx.query.populate
          : typeof ctx.query.populate === "string"
            ? [ctx.query.populate]
            : [""];
      }

      const entity = await strapi.db.query("api::policy.policy").findOne({
        where: { slug },
        populate: populate,
      });

      if (!entity) {
        return ctx.notFound();
      }

      const sanitizedEntity = await this.sanitizeOutput(entity, ctx);
      return this.transformResponse(sanitizedEntity);
    },
  })
);
