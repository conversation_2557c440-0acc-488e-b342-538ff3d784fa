{"kind": "collectionType", "collectionName": "policies", "info": {"singularName": "policy", "pluralName": "policies", "displayName": "Policy", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"Title": {"type": "string", "required": true}, "slug": {"type": "uid", "targetField": "Title", "required": true, "unique": true}, "Description": {"type": "text"}, "Tabs": {"type": "dynamiczone", "components": ["components.card-trichd"]}, "Downloads": {"type": "component", "repeatable": true, "component": "common.card-tdf"}, "Detail_Title": {"type": "string"}, "PolicyOrder": {"type": "integer"}}}