/**
 * product controller
 */

import { factories } from "@strapi/strapi";

export default factories.createCoreController(
  "api::product.product",
  ({ strapi }) => ({
    async find(ctx) {
      await this.validateQuery(ctx);
      const sanitizedQueryParams = await this.sanitizeQuery(ctx);
      const { results, pagination } = await strapi
        .service("api::product.product")
        .find(sanitizedQueryParams);
      const sanitizedResults = await this.sanitizeOutput(results, ctx);
      return this.transformResponse(sanitizedResults, { pagination });
    },

    async findBySlug(ctx) {
      const { slug } = ctx.params;
      let populate = ["Thumbnail", "Icon", "Product_Details"];

      if (ctx.query.populate) {
        populate = Array.isArray(ctx.query.populate)
          ? ctx.query.populate
          : typeof ctx.query.populate === "string"
            ? [ctx.query.populate]
            : ["Thumbnail", "Icon", "Product_Details"];
      }

      const entity = await strapi.db.query("api::product.product").findOne({
        where: { slug },
        populate: populate,
      });

      if (!entity) {
        return ctx.notFound();
      }

      const sanitizedEntity = await this.sanitizeOutput(entity, ctx);
      return this.transformResponse(sanitizedEntity);
    },
  })
);
