{"kind": "collectionType", "collectionName": "inverstors_listings", "info": {"singularName": "inverstors-listing", "pluralName": "inverstors-listings", "displayName": "Inverstors Listing", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"Title": {"type": "string", "required": true}, "slug": {"type": "uid", "targetField": "Title", "required": true, "unique": true}, "Attachments": {"type": "component", "repeatable": true, "component": "common.download-card"}, "Slug_Name": {"type": "string"}, "Breadcrumb_Name": {"type": "string"}, "Banner_slug": {"type": "component", "repeatable": false, "component": "common.banner-itd"}, "Header": {"type": "component", "repeatable": false, "component": "common.section-td"}, "InvestorsOrder": {"type": "integer"}, "investors_section_title": {"type": "relation", "relation": "manyToOne", "target": "api::investors-section-title.investors-section-title", "inversedBy": "inverstors_listings"}}}