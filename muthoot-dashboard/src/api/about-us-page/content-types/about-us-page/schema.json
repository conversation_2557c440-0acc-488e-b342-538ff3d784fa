{"kind": "singleType", "collectionName": "about_us_pages", "info": {"singularName": "about-us-page", "pluralName": "about-us-pages", "displayName": "About Us Page", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"SEO": {"type": "component", "repeatable": false, "component": "common.seo"}, "Header": {"type": "component", "repeatable": false, "component": "common.section-td"}, "Operating_Philosophy": {"type": "component", "repeatable": false, "component": "about-us.operating-philosophy-card"}, "Banner": {"type": "component", "repeatable": false, "component": "common.banner-tds"}, "Section_6Aboutus": {"type": "component", "repeatable": false, "component": "common.card-itd"}, "Section7_About": {"displayName": "Section7_BlueSoch", "type": "component", "repeatable": false, "component": "section-tdrichd.section7-blue-soch"}, "MilestonesCard": {"type": "component", "repeatable": false, "component": "about.milestone-card"}}}