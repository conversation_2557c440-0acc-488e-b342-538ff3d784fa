{"kind": "collectionType", "collectionName": "blogs", "info": {"singularName": "blog", "pluralName": "blogs", "displayName": "Blog", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"Title": {"type": "string", "required": true}, "Thumbnail": {"type": "media", "multiple": false, "required": true, "allowedTypes": ["images"]}, "Banner": {"type": "media", "multiple": false, "required": true, "allowedTypes": ["images"]}, "Description": {"type": "blocks", "required": true}, "Date": {"type": "date"}, "Tags": {"type": "customField", "customField": "plugin::tagsinput.tags"}, "Blog_categories": {"type": "relation", "relation": "oneToMany", "target": "api::blog-category.blog-category", "mappedBy": "Blog"}, "DescriptionTitle": {"type": "string"}, "BlogOrder": {"type": "integer"}}}