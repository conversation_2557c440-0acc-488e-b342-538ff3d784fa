export default {
  config: {
    auth: {
      logo: "/uploads/custom-logo.png",
      backgroundImage: "/uploads/login-background.jpg",
    },
    translations: {
      en: {
        // Login translations
        "Auth.form.welcome.title": "Welcome to Muthoot Capital",
        "Auth.form.welcome.subtitle": "Please sign in to access admin panel",
        "Auth.form.button.login.strapi": "Log in",

        // Registration page translations
        "Auth.form.register.title": "Welcome to Muthoot Capital",
        "Auth.form.register.subtitle": "Create admin account",
        "Auth.form.firstname.label": "First Name",
        "Auth.form.lastname.label": "Last Name",
        "Auth.form.email.label": "Email Address",
        "Auth.form.password.label": "Password",
        "Auth.form.confirmPassword.label": "Confirm Password",
        "Auth.form.register.description":
          "Credentials are only used to authenticate in Muthoot Capital. All saved data will be stored in your database.",
        "Auth.form.register.news.label":
          "Keep me updated about new features & upcoming improvements",
        "Auth.form.button.register": "Create Account",
        "Auth.form.button.terms": "terms",
        "Auth.form.button.policy": "privacy policy",
      },
    },
    theme: {
      colors: {
        primary100: "#f6ecfc",
        primary200: "#e0c1f4",
        primary500: "#ac73e6",
        primary600: "#9736e8",
        primary700: "#8312d1",
      },
      global: {
        metaTitleSuffix: "Muthoot Capital Admin",
        labels: {
          productName: "Muthoot Capital Admin",
        },
      },
    },
    notifications: {
      releases: false,
    },
    tutorials: false,
    styles: {
      global: {
        ".auth-wrapper": {
          "max-width": "32rem",
          margin: "0 auto",
          padding: "0 1rem",
        },
        ".auth-title": {
          "font-size": "2rem",
          "font-weight": "600",
          "text-align": "center",
          "margin-bottom": "1rem",
          "line-height": "1.2",
          "white-space": "pre-line",
          width: "100%",
        },
        ".auth-title span": {
          display: "block",
          width: "100%",
          "text-align": "center",
          "margin-bottom": "0.5rem",
        },
        ".auth-subtitle": {
          "text-align": "center",
          "margin-bottom": "2rem",
          color: "#240687",
          "font-size": "1rem",
        },
        ".auth-register-title": {
          "font-size": "2rem",
          "font-weight": "600",
          "text-align": "center",
          "margin-bottom": "1rem",
          "line-height": "1.2",
        },
        ".auth-register-subtitle": {
          "text-align": "center",
          "margin-bottom": "2rem",
          color: "#240687",
          "font-size": "1rem",
        },
      },
    },
    head: {
      favicon: "/uploads/custom-logo.png",
      title: "Muthoot Capital Admin",
    },
  },
  bootstrap(app: any) {
    // Override the default title
    document.title = "Muthoot Capital Admin";
    console.log("Custom admin loaded");
  },
};
