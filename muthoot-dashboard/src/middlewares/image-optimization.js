const sharp = require("sharp");

module.exports = (config, { strapi }) => {
  return async (ctx, next) => {
    if (ctx.method === "POST" && ctx.url === "/upload") {
      const { files } = ctx.request.files;

      if (files) {
        for (const file of files) {
          if (file.mime.startsWith("image/")) {
            const optimizedImage = await sharp(file.path)
              .resize(800, 800, {
                fit: "inside",
                withoutEnlargement: true,
              })
              .jpeg({ quality: 80 }) // Adjust quality as needed
              .toBuffer();

            // Replace the original file buffer with the optimized one
            file.buffer = optimizedImage;
          }
        }
      }
    }

    await next();
  };
};
