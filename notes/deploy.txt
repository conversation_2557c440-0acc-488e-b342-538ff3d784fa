
<EMAIL>
G9$vKmzX@qL#TcN

SERVER CONNECT ====

sudo ssh -i  ssh-key-2025-06-17.key -N -L 22:***********:22 -p 22 <EMAIL> -v

sudo ssh -i ssh-key-2025-06-17.key -N -L 22:***********:22 -p 22 <EMAIL> -v


sudo ssh -i ssh-key-2025-06-17.key -N -L 22:***********:22 -p 22 <EMAIL> -v

SSH ===
sudo ssh -i npwebadmin.pem npwebadmin@127.0.0.1

spc

HARD DISK ====

df -h 

sudo chown -R npwebadmin:npwebadmin /muthootcap

sudo yum updatel
sudo yum install npm nodejs nginx certbot python3-certbot-nginx

mysql -u corp_website_usr -p a7k1+oAVXpwUQn#


sudo scp uploads.zip ssh -i npwebadmin.pem npwebadmin@127.0.0.1:/muthootcap/dashboard/public


COMMAND TO RUN DOWNLOAD SCP
===========================

scp root@************:/root/projects/muthootcap/muthootcap-ui/public/uploads.zip .